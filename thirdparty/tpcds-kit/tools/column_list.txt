#
#  Legal Notice
#
#  This document and associated source code (the "Work") is a part of a
#  benchmark specification maintained by the TPC.
# 
#  The TPC reserves all right, title, and interest to the Work as provided
#  under U.S. and international laws, including without limitation all patent
#  and trademark rights therein.
# 
#  No Warranty
# 
#  1.1 TO THE MAXIMUM EXTENT PERMITTED BY APPLICABLE LAW, THE INFORMATION
#      CONTAINED HEREIN IS PROVIDED "AS IS" AND WITH ALL FAULTS, AND THE
#      AUTHORS AND DEVELOPERS OF THE WORK HEREBY DISCLAIM ALL OTHER
#      WARRANTIES AND CONDITIONS, EITHER EXPRESS, IMPLIED OR STATUTORY,
#      INCLUDING, BUT NOT LIMITED TO, ANY (IF ANY) IMPLIED WARRANTIES,
#      DUTIES OR CONDITIONS OF MERCHANTABILITY, OF FITNESS FOR A PARTICULAR
#      PURPOSE, OF ACCURACY OR COMPLETENESS OF RESPONSES, OF RESULTS, OF
#      WOR<PERSON><PERSON>NLIKE EFFORT, OF LACK OF VIRUSES, AND OF LACK OF NEGLIGENCE.
#      ALSO, THERE IS NO WARRANTY OR CONDITION OF TITLE, <PERSON><PERSON>ET ENJOYMENT,
#      QUIET POSSESSION, CORRESPONDENCE TO DESCRIPTION OR NON-INFRINGEMENT
#      WITH REGARD TO THE WORK.
#  1.2 IN NO EVENT WILL ANY AUTHOR OR DEVELOPER OF THE WORK BE LIABLE TO
#      ANY OTHER PARTY FOR ANY DAMAGES, INCLUDING BUT NOT LIMITED TO THE
#      COST OF PROCURING SUBSTITUTE GOODS OR SERVICES, LOST PROFITS, LOSS
#      OF USE, LOSS OF DATA, OR ANY INCIDENTAL, CONSEQUENTIAL, DIRECT,
#      INDIRECT, OR SPECIAL DAMAGES WHETHER UNDER CONTRACT, TORT, WARRANTY,
#      OR OTHERWISE, ARISING IN ANY WAY OUT OF THIS OR ANY OTHER AGREEMENT
#      RELATING TO THE WORK, WHETHER OR NOT SUCH AUTHOR OR DEVELOPER HAD
#      ADVANCE NOTICE OF THE POSSIBILITY OF SUCH DAMAGES.
# 
#  Contributors:
#  Gradient Systems
# 

CC_CALL_CENTER_SK		CALL_CENTER	0
CC_CALL_CENTER_ID		CALL_CENTER	15
CC_REC_START_DATE_ID	CALL_CENTER	10
CC_REC_END_DATE_ID		CALL_CENTER	
CC_CLOSED_DATE_ID		CALL_CENTER	4
CC_OPEN_DATE_ID			CALL_CENTER	10
CC_NAME					CALL_CENTER	0
CC_CLASS				CALL_CENTER	2
CC_EMPLOYEES			CALL_CENTER
CC_SQ_FT				CALL_CENTER
CC_HOURS				CALL_CENTER
CC_MANAGER				CALL_CENTER	2
CC_MARKET_ID			CALL_CENTER
CC_MARKET_CLASS			CALL_CENTER	50
CC_MARKET_DESC			CALL_CENTER	50
CC_MARKET_MANAGER		CALL_CENTER	2
CC_DIVISION				CALL_CENTER	2
CC_DIVISION_NAME		CALL_CENTER	2
CC_COMPANY				CALL_CENTER	2
CC_COMPANY_NAME			CALL_CENTER	2
CC_STREET_NUMBER		CALL_CENTER	0
CC_STREET_NAME			CALL_CENTER	0
CC_STREET_TYPE			CALL_CENTER	0
CC_SUITE_NUMBER			CALL_CENTER	0
CC_CITY					CALL_CENTER	0
CC_COUNTY				CALL_CENTER	0
CC_STATE				CALL_CENTER	0
CC_ZIP					CALL_CENTER	0
CC_COUNTRY				CALL_CENTER	0
CC_GMT_OFFSET			CALL_CENTER	0
CC_ADDRESS				CALL_CENTER	15
CC_TAX_PERCENTAGE		CALL_CENTER
CC_SCD					CALL_CENTER	
CC_NULLS				CALL_CENTER	2
#
# CATALOG_PAGE table structure
#
CP_CATALOG_PAGE_SK		CATALOG_PAGE
CP_CATALOG_PAGE_ID		CATALOG_PAGE
CP_START_DATE_ID		CATALOG_PAGE
CP_END_DATE_ID			CATALOG_PAGE
CP_PROMO_ID				CATALOG_PAGE
CP_DEPARTMENT			CATALOG_PAGE
CP_CATALOG_NUMBER		CATALOG_PAGE
CP_CATALOG_PAGE_NUMBER	CATALOG_PAGE
CP_DESCRIPTION			CATALOG_PAGE	100 S_CP_DESCRIPTION
CP_TYPE					CATALOG_PAGE
CP_NULLS				CATALOG_PAGE	2
#
# CATALOG_RETURNS table structure
#
CR_RETURNED_DATE_SK			CATALOG_RETURNS 28
CR_RETURNED_TIME_SK			CATALOG_RETURNS	28
CR_ITEM_SK					CATALOG_RETURNS	14
CR_REFUNDED_CUSTOMER_SK		CATALOG_RETURNS	14
CR_REFUNDED_CDEMO_SK		CATALOG_RETURNS	14
CR_REFUNDED_HDEMO_SK		CATALOG_RETURNS	14
CR_REFUNDED_ADDR_SK			CATALOG_RETURNS	14
CR_RETURNING_CUSTOMER_SK    CATALOG_RETURNS	28
CR_RETURNING_CDEMO_SK		CATALOG_RETURNS	14
CR_RETURNING_HDEMO_SK		CATALOG_RETURNS	14
CR_RETURNING_ADDR_SK		CATALOG_RETURNS	14
CR_CALL_CENTER_SK			CATALOG_RETURNS 0
CR_CATALOG_PAGE_SK			CATALOG_RETURNS	14
CR_SHIP_MODE_SK				CATALOG_RETURNS	14
CR_WAREHOUSE_SK				CATALOG_RETURNS	14
CR_REASON_SK				CATALOG_RETURNS	14
CR_ORDER_NUMBER				CATALOG_RETURNS	0
CR_PRICING_QUANTITY			CATALOG_RETURNS	0
CR_PRICING_NET_PAID			CATALOG_RETURNS	0
CR_PRICING_EXT_TAX			CATALOG_RETURNS	0
CR_PRICING_NET_PAID_INC_TAX CATALOG_RETURNS	0
CR_PRICING_FEE				CATALOG_RETURNS	0
CR_PRICING_EXT_SHIP_COST	CATALOG_RETURNS	0
CR_PRICING_REFUNDED_CASH	CATALOG_RETURNS	0
CR_PRICING_REVERSED_CHARGE	CATALOG_RETURNS	0
CR_PRICING_STORE_CREDIT		CATALOG_RETURNS	0
CR_PRICING_NET_LOSS			CATALOG_RETURNS	0
CR_NULLS					CATALOG_RETURNS	28
CR_PRICING					CATALOG_RETURNS	70
#
# CATALOG_SALES table structure
#
CS_SOLD_DATE_SK						CATALOG_SALES	
CS_SOLD_TIME_SK						CATALOG_SALES	2
CS_SHIP_DATE_SK						CATALOG_SALES	14
CS_BILL_CUSTOMER_SK					CATALOG_SALES
CS_BILL_CDEMO_SK					CATALOG_SALES
CS_BILL_HDEMO_SK					CATALOG_SALES
CS_BILL_ADDR_SK						CATALOG_SALES
CS_SHIP_CUSTOMER_SK					CATALOG_SALES	2
CS_SHIP_CDEMO_SK					CATALOG_SALES
CS_SHIP_HDEMO_SK					CATALOG_SALES
CS_SHIP_ADDR_SK						CATALOG_SALES
CS_CALL_CENTER_SK					CATALOG_SALES
CS_CATALOG_PAGE_SK					CATALOG_SALES	42
CS_SHIP_MODE_SK						CATALOG_SALES	14
CS_WAREHOUSE_SK						CATALOG_SALES	14
CS_SOLD_ITEM_SK		 				CATALOG_SALES	1
CS_PROMO_SK							CATALOG_SALES		14
CS_ORDER_NUMBER						CATALOG_SALES	
CS_PRICING_QUANTITY					CATALOG_SALES	0
CS_PRICING_WHOLESALE_COST			CATALOG_SALES	0
CS_PRICING_LIST_PRICE				CATALOG_SALES	0
CS_PRICING_SALES_PRICE				CATALOG_SALES	0
CS_PRICING_COUPON_AMT				CATALOG_SALES	0
CS_PRICING_EXT_SALES_PRICE			CATALOG_SALES	0
CS_PRICING_EXT_DISCOUNT_AMOUNT		CATALOG_SALES	0
CS_PRICING_EXT_WHOLESALE_COST		CATALOG_SALES	0
CS_PRICING_EXT_LIST_PRICE			CATALOG_SALES	0
CS_PRICING_EXT_TAX					CATALOG_SALES	0
CS_PRICING_EXT_SHIP_COST			CATALOG_SALES	0
CS_PRICING_NET_PAID					CATALOG_SALES	0
CS_PRICING_NET_PAID_INC_TAX			CATALOG_SALES	0
CS_PRICING_NET_PAID_INC_SHIP		CATALOG_SALES	0
CS_PRICING_NET_PAID_INC_SHIP_TAX	CATALOG_SALES	0
CS_PRICING_NET_PROFIT				CATALOG_SALES	0
CS_PRICING							CATALOG_SALES	112
CS_PERMUTE							CATALOG_SALES	0
CS_NULLS							CATALOG_SALES	28
CR_IS_RETURNED				CATALOG_SALES	14
CS_PERMUTATION CATALOG_SALES 0
#
# CUSTOMER table structure
#
C_CUSTOMER_SK			CUSTOMER
C_CUSTOMER_ID			CUSTOMER
C_CURRENT_CDEMO_SK      CUSTOMER
C_CURRENT_HDEMO_SK      CUSTOMER
C_CURRENT_ADDR_SK		CUSTOMER
C_FIRST_SHIPTO_DATE_ID  CUSTOMER	0
C_FIRST_SALES_DATE_ID   CUSTOMER	
C_SALUTATION			CUSTOMER
C_FIRST_NAME			CUSTOMER
C_LAST_NAME				CUSTOMER
C_PREFERRED_CUST_FLAG   CUSTOMER	2
C_BIRTH_DAY				CUSTOMER
C_BIRTH_MONTH			CUSTOMER	0
C_BIRTH_YEAR			CUSTOMER	0
C_BIRTH_COUNTRY			CUSTOMER
C_LOGIN					CUSTOMER
C_EMAIL_ADDRESS			CUSTOMER	23
C_LAST_REVIEW_DATE		CUSTOMER
C_NULLS					CUSTOMER	2
#
# CUSTOMER_ADDRESS table structure
#
CA_ADDRESS_SK		CUSTOMER_ADDRESS
CA_ADDRESS_ID		CUSTOMER_ADDRESS
CA_ADDRESS_STREET_NUM	CUSTOMER_ADDRESS
CA_ADDRESS_STREET_NAME1 CUSTOMER_ADDRESS
CA_ADDRESS_STREET_TYPE	CUSTOMER_ADDRESS
CA_ADDRESS_SUITE_NUM	CUSTOMER_ADDRESS
CA_ADDRESS_CITY			CUSTOMER_ADDRESS
CA_ADDRESS_COUNTY		CUSTOMER_ADDRESS
CA_ADDRESS_STATE		CUSTOMER_ADDRESS
CA_ADDRESS_ZIP			CUSTOMER_ADDRESS
CA_ADDRESS_COUNTRY		CUSTOMER_ADDRESS
CA_ADDRESS_GMT_OFFSET	CUSTOMER_ADDRESS
CA_LOCATION_TYPE		CUSTOMER_ADDRESS
CA_NULLS				CUSTOMER_ADDRESS	2
CA_ADDRESS				CUSTOMER_ADDRESS	7 
CA_ADDRESS_STREET_NAME2 CUSTOMER_ADDRESS
#
# CUSTOMER_DEMOGRAPHICS table structure
#
CD_DEMO_SK				CUSTOMER_DEMOGRAPHICS
CD_GENDER				CUSTOMER_DEMOGRAPHICS
CD_MARITAL_STATUS		CUSTOMER_DEMOGRAPHICS
CD_EDUCATION_STATUS		CUSTOMER_DEMOGRAPHICS
CD_PURCHASE_ESTIMATE	CUSTOMER_DEMOGRAPHICS
CD_CREDIT_RATING		CUSTOMER_DEMOGRAPHICS
CD_DEP_COUNT			CUSTOMER_DEMOGRAPHICS
CD_DEP_EMPLOYED_COUNT   CUSTOMER_DEMOGRAPHICS
CD_DEP_COLLEGE_COUNT    CUSTOMER_DEMOGRAPHICS
CD_NULLS				CUSTOMER_DEMOGRAPHICS	2

#
# DATE table structure
#

D_DATE_SK		DATE	0
D_DATE_ID		DATE	0
D_DATE			DATE	0
D_MONTH_SEQ     DATE	0
D_WEEK_SEQ      DATE	0
D_QUARTER_SEQ   DATE	0
D_YEAR			DATE	0
D_DOW			DATE	0
D_MOY			DATE	0
D_DOM			DATE	0
D_QOY			DATE	0
D_FY_YEAR		DATE	0
D_FY_QUARTER_SEQ	DATE	0
D_FY_WEEK_SEQ   DATE	0
D_DAY_NAME      DATE	0
D_QUARTER_NAME  DATE	0
D_HOLIDAY		DATE	0
D_WEEKEND		DATE	0
D_FOLLOWING_HOLIDAY	DATE	0
D_FIRST_DOM     DATE	0
D_LAST_DOM      DATE	0
D_SAME_DAY_LY   DATE	0
D_SAME_DAY_LQ   DATE	0
D_CURRENT_DAY   DATE	0
D_CURRENT_WEEK  DATE	0
D_CURRENT_MONTH DATE	0
D_CURRENT_QUARTER	DATE	0
D_CURRENT_YEAR  DATE	0
D_NULLS			DATE	2
#
# HOUSEHOLD_DEMOGRAPHICS table structure
#
HD_DEMO_SK			HOUSEHOLD_DEMOGRAPHICS
HD_INCOME_BAND_ID   HOUSEHOLD_DEMOGRAPHICS
HD_BUY_POTENTIAL    HOUSEHOLD_DEMOGRAPHICS
HD_DEP_COUNT		HOUSEHOLD_DEMOGRAPHICS
HD_VEHICLE_COUNT    HOUSEHOLD_DEMOGRAPHICS
HD_NULLS			HOUSEHOLD_DEMOGRAPHICS	2
#
# INCOME_BAND table structure
#
IB_INCOME_BAND_ID   INCOME_BAND
IB_LOWER_BOUND      INCOME_BAND
IB_UPPER_BOUND      INCOME_BAND
IB_NULLS			INCOME_BAND	2
#
# INVENTORY table structure
#
INV_DATE_SK			INVENTORY
INV_ITEM_SK			INVENTORY
INV_WAREHOUSE_SK	INVENTORY
INV_QUANTITY_ON_HAND INVENTORY	
INV_NULLS			INVENTORY	2
#
# ITEM table structure
#
I_ITEM_SK			ITEM
I_ITEM_ID			ITEM
I_REC_START_DATE_ID	ITEM
I_REC_END_DATE_ID	ITEM	2
I_ITEM_DESC			ITEM	200 S_ITEM_DESC
I_CURRENT_PRICE     ITEM	2
I_WHOLESALE_COST	ITEM
I_BRAND_ID			ITEM
I_BRAND				ITEM
I_CLASS_ID			ITEM
I_CLASS				ITEM
I_CATEGORY_ID		ITEM
I_CATEGORY			ITEM
I_MANUFACT_ID		ITEM	2
I_MANUFACT			ITEM
I_SIZE				ITEM
I_FORMULATION		ITEM	50
I_COLOR				ITEM
I_UNITS				ITEM
I_CONTAINER			ITEM
I_MANAGER_ID		ITEM	2
I_PRODUCT_NAME      ITEM
I_NULLS				ITEM	2
I_SCD				ITEM	
I_PROMO_SK			ITEM	2
#
# PROMOTION table structure
#
P_PROMO_SK			PROMOTION
P_PROMO_ID			PROMOTION
P_START_DATE_ID		PROMOTION
P_END_DATE_ID		PROMOTION
P_ITEM_SK			PROMOTION
P_COST				PROMOTION
P_RESPONSE_TARGET	PROMOTION
P_PROMO_NAME		PROMOTION
P_CHANNEL_DMAIL		PROMOTION
P_CHANNEL_EMAIL		PROMOTION
P_CHANNEL_CATALOG	PROMOTION
P_CHANNEL_TV		PROMOTION
P_CHANNEL_RADIO		PROMOTION
P_CHANNEL_PRESS		PROMOTION
P_CHANNEL_EVENT		PROMOTION
P_CHANNEL_DEMO		PROMOTION
P_CHANNEL_DETAILS	PROMOTION	100
P_PURPOSE			PROMOTION
P_DISCOUNT_ACTIVE	PROMOTION
P_NULLS				PROMOTION	2
#
# REASON table structure
#
R_REASON_SK				REASON
R_REASON_ID				REASON
R_REASON_DESCRIPTION    REASON
R_NULLS				    REASON	2
#
# SHIP_MODE table structure
#
SM_SHIP_MODE_SK		SHIP_MODE
SM_SHIP_MODE_ID		SHIP_MODE
SM_TYPE				SHIP_MODE
SM_CODE				SHIP_MODE
SM_CONTRACT			SHIP_MODE	21
SM_CARRIER		    SHIP_MODE
SM_NULLS			SHIP_MODE	2
#
# STORE table structure
#
W_STORE_SK					STORE
W_STORE_ID					STORE
W_STORE_REC_START_DATE_ID	STORE
W_STORE_REC_END_DATE_ID		STORE	2
W_STORE_CLOSED_DATE_ID		STORE 2
W_STORE_NAME				STORE	0
W_STORE_EMPLOYEES			STORE
W_STORE_FLOOR_SPACE			STORE
W_STORE_HOURS				STORE
W_STORE_MANAGER					STORE	2
W_STORE_MARKET_ID			STORE
W_STORE_TAX_PERCENTAGE		STORE
W_STORE_GEOGRAPHY_CLASS		STORE
W_STORE_MARKET_DESC			STORE	100
W_STORE_MARKET_MANAGER			STORE	2
W_STORE_DIVISION_ID			STORE
W_STORE_DIVISION_NAME		STORE
W_STORE_COMPANY_ID			STORE
W_STORE_COMPANY_NAME		STORE
W_STORE_ADDRESS_STREET_NUM	STORE
W_STORE_ADDRESS_STREET_NAME1		STORE
W_STORE_ADDRESS_STREET_TYPE	STORE
W_STORE_ADDRESS_SUITE_NUM	STORE
W_STORE_ADDRESS_CITY		STORE
W_STORE_ADDRESS_COUNTY		STORE
W_STORE_ADDRESS_STATE		STORE
W_STORE_ADDRESS_ZIP			STORE
W_STORE_ADDRESS_COUNTRY		STORE
W_STORE_ADDRESS_GMT_OFFSET	STORE
W_STORE_NULLS				STORE	2
W_STORE_TYPE				STORE
W_STORE_SCD				STORE	
W_STORE_ADDRESS				STORE	7
#
# STORE_RETURNS table structure
#
SR_RETURNED_DATE_SK     STORE_RETURNS	32
SR_RETURNED_TIME_SK     STORE_RETURNS	32
SR_ITEM_SK				STORE_RETURNS	16
SR_CUSTOMER_SK			STORE_RETURNS	16
SR_CDEMO_SK				STORE_RETURNS	16
SR_HDEMO_SK				STORE_RETURNS	16
SR_ADDR_SK				STORE_RETURNS	16
SR_STORE_SK				STORE_RETURNS	16
SR_REASON_SK			STORE_RETURNS	16
SR_TICKET_NUMBER		STORE_RETURNS	16	
SR_PRICING_QUANTITY			STORE_RETURNS	0
SR_PRICING_NET_PAID			STORE_RETURNS	0
SR_PRICING_EXT_TAX			STORE_RETURNS	0
SR_PRICING_NET_PAID_INC_TAX STORE_RETURNS	0
SR_PRICING_FEE				STORE_RETURNS	0
SR_PRICING_EXT_SHIP_COST	STORE_RETURNS	0
SR_PRICING_REFUNDED_CASH	STORE_RETURNS	0
SR_PRICING_REVERSED_CHARGE	STORE_RETURNS	0
SR_PRICING_STORE_CREDIT		STORE_RETURNS	0
SR_PRICING_NET_LOSS			STORE_RETURNS	0
SR_PRICING				STORE_RETURNS	80
SR_NULLS				STORE_RETURNS	32
#
# STORE_SALES table structure
#
SS_SOLD_DATE_SK     STORE_SALES	2
SS_SOLD_TIME_SK     STORE_SALES	2
SS_SOLD_ITEM_SK     STORE_SALES
SS_SOLD_CUSTOMER_SK STORE_SALES
SS_SOLD_CDEMO_SK    STORE_SALES
SS_SOLD_HDEMO_SK    STORE_SALES
SS_SOLD_ADDR_SK     STORE_SALES
SS_SOLD_STORE_SK    STORE_SALES
SS_SOLD_PROMO_SK    STORE_SALES	16
SS_TICKET_NUMBER    STORE_SALES	
SS_PRICING_QUANTITY			STORE_SALES	
SS_PRICING_WHOLESALE_COST	STORE_SALES	0
SS_PRICING_LIST_PRICE		STORE_SALES	0
SS_PRICING_SALES_PRICE		STORE_SALES	0
SS_PRICING_COUPON_AMT		STORE_SALES	0
SS_PRICING_EXT_SALES_PRICE	STORE_SALES	0
SS_PRICING_EXT_WHOLESALE_COST STORE_SALES	0
SS_PRICING_EXT_LIST_PRICE	STORE_SALES	0
SS_PRICING_EXT_TAX			STORE_SALES	0
SS_PRICING_NET_PAID			STORE_SALES	0
SS_PRICING_NET_PAID_INC_TAX STORE_SALES	0
SS_PRICING_NET_PROFIT		STORE_SALES	0
SR_IS_RETURNED		STORE_SALES	16
SS_PRICING			STORE_SALES	128
SS_NULLS			STORE_SALES	32
SS_PERMUTATION STORE_SALES 0
#
# TIME table structure
#
T_TIME_SK	TIME
T_TIME_ID	TIME
T_TIME      TIME
T_HOUR      TIME
T_MINUTE    TIME
T_SECOND    TIME
T_AM_PM     TIME
T_SHIFT     TIME
T_SUB_SHIFT TIME
T_MEAL_TIME TIME
T_NULLS		TIME	2
#
# WAREHOUSE table structure
#
W_WAREHOUSE_SK		WAREHOUSE
W_WAREHOUSE_ID		WAREHOUSE
W_WAREHOUSE_NAME    WAREHOUSE	80
W_WAREHOUSE_SQ_FT   WAREHOUSE
W_ADDRESS_STREET_NUM		WAREHOUSE
W_ADDRESS_STREET_NAME1		WAREHOUSE
W_ADDRESS_STREET_TYPE		WAREHOUSE
W_ADDRESS_SUITE_NUM			WAREHOUSE
W_ADDRESS_CITY				WAREHOUSE
W_ADDRESS_COUNTY			WAREHOUSE
W_ADDRESS_STATE				WAREHOUSE
W_ADDRESS_ZIP				WAREHOUSE
W_ADDRESS_COUNTRY			WAREHOUSE
W_ADDRESS_GMT_OFFSET		WAREHOUSE
W_NULLS				WAREHOUSE	2
W_WAREHOUSE_ADDRESS	WAREHOUSE	7
#
# WEB_PAGE table structure
#
WP_PAGE_SK			WEB_PAGE
WP_PAGE_ID			WEB_PAGE
WP_REC_START_DATE_ID	WEB_PAGE
WP_REC_END_DATE_ID	WEB_PAGE
WP_CREATION_DATE_SK WEB_PAGE	2
WP_ACCESS_DATE_SK   WEB_PAGE
WP_AUTOGEN_FLAG     WEB_PAGE
WP_CUSTOMER_SK		WEB_PAGE
WP_URL				WEB_PAGE
WP_TYPE				WEB_PAGE
WP_CHAR_COUNT		WEB_PAGE
WP_LINK_COUNT		WEB_PAGE
WP_IMAGE_COUNT      WEB_PAGE
WP_MAX_AD_COUNT     WEB_PAGE
WP_NULLS		    WEB_PAGE	2
WP_SCD		    WEB_PAGE	
#
# WEB_RETURNS table structure
#
WR_RETURNED_DATE_SK		WEB_RETURNS 32
WR_RETURNED_TIME_SK     WEB_RETURNS	32
WR_ITEM_SK				WEB_RETURNS	16
WR_REFUNDED_CUSTOMER_SK WEB_RETURNS	16
WR_REFUNDED_CDEMO_SK    WEB_RETURNS	16
WR_REFUNDED_HDEMO_SK    WEB_RETURNS	16
WR_REFUNDED_ADDR_SK     WEB_RETURNS	16
WR_RETURNING_CUSTOMER_SK	WEB_RETURNS	16
WR_RETURNING_CDEMO_SK   WEB_RETURNS	16
WR_RETURNING_HDEMO_SK   WEB_RETURNS	16
WR_RETURNING_ADDR_SK    WEB_RETURNS	16
WR_WEB_PAGE_SK			WEB_RETURNS	16
WR_REASON_SK			WEB_RETURNS	16
WR_ORDER_NUMBER			WEB_RETURNS	0
WR_PRICING_QUANTITY			WEB_RETURNS 0
WR_PRICING_NET_PAID			WEB_RETURNS	0
WR_PRICING_EXT_TAX			WEB_RETURNS	0
WR_PRICING_NET_PAID_INC_TAX	WEB_RETURNS	0
WR_PRICING_FEE				WEB_RETURNS	0
WR_PRICING_EXT_SHIP_COST	WEB_RETURNS	0
WR_PRICING_REFUNDED_CASH	WEB_RETURNS	0
WR_PRICING_REVERSED_CHARGE	WEB_RETURNS	0
WR_PRICING_STORE_CREDIT		WEB_RETURNS	0
WR_PRICING_NET_LOSS			WEB_RETURNS	0
WR_PRICING				WEB_RETURNS	80
WR_NULLS				WEB_RETURNS	32
#
# WEB_SALES table structure
#
WS_SOLD_DATE_SK		WEB_SALES	2
WS_SOLD_TIME_SK     WEB_SALES	2
WS_SHIP_DATE_SK     WEB_SALES		16
WS_ITEM_SK			WEB_SALES
WS_BILL_CUSTOMER_SK WEB_SALES
WS_BILL_CDEMO_SK    WEB_SALES
WS_BILL_HDEMO_SK    WEB_SALES
WS_BILL_ADDR_SK     WEB_SALES
WS_SHIP_CUSTOMER_SK WEB_SALES	2
WS_SHIP_CDEMO_SK    WEB_SALES	2
WS_SHIP_HDEMO_SK    WEB_SALES
WS_SHIP_ADDR_SK     WEB_SALES
WS_WEB_PAGE_SK		WEB_SALES		16
WS_WEB_SITE_SK		WEB_SALES		16
WS_SHIP_MODE_SK     WEB_SALES		16
WS_WAREHOUSE_SK     WEB_SALES		16
WS_PROMO_SK			WEB_SALES		16
WS_ORDER_NUMBER     WEB_SALES	
WS_PRICING_QUANTITY			WEB_SALES	
WS_PRICING_WHOLESALE_COST	WEB_SALES	
WS_PRICING_LIST_PRICE		WEB_SALES	0
WS_PRICING_SALES_PRICE		WEB_SALES	0
WS_PRICING_EXT_DISCOUNT_AMT WEB_SALES	0
WS_PRICING_EXT_SALES_PRICE	WEB_SALES	0
WS_PRICING_EXT_WHOLESALE_COST WEB_SALES	0
WS_PRICING_EXT_LIST_PRICE	WEB_SALES	0
WS_PRICING_EXT_TAX			WEB_SALES	0
WS_PRICING_COUPON_AMT		WEB_SALES	0
WS_PRICING_EXT_SHIP_COST	WEB_SALES	0
WS_PRICING_NET_PAID			WEB_SALES	0
WS_PRICING_NET_PAID_INC_TAX WEB_SALES	0
WS_PRICING_NET_PAID_INC_SHIP WEB_SALES	0
WS_PRICING_NET_PAID_INC_SHIP_TAX WEB_SALES	0
WS_PRICING_NET_PROFIT		WEB_SALES	0
WS_PRICING			WEB_SALES	128
WS_NULLS			WEB_SALES	32
WR_IS_RETURNED			WEB_SALES	16
WS_PERMUTATION			WEB_SALES   0
#
# WEB_SITE table structure
#
WEB_SITE_SK			WEB_SITE
WEB_SITE_ID			WEB_SITE
WEB_REC_START_DATE_ID	WEB_SITE
WEB_REC_END_DATE_ID	WEB_SITE
WEB_NAME			WEB_SITE
WEB_OPEN_DATE		WEB_SITE
WEB_CLOSE_DATE		WEB_SITE
WEB_CLASS			WEB_SITE
WEB_MANAGER				WEB_SITE	2
WEB_MARKET_ID			WEB_SITE
WEB_MARKET_CLASS		WEB_SITE	20
WEB_MARKET_DESC		WEB_SITE	100
WEB_MARKET_MANAGER			WEB_SITE	2
WEB_COMPANY_ID		WEB_SITE
WEB_COMPANY_NAME	WEB_SITE
WEB_ADDRESS_STREET_NUM		WEB_SITE
WEB_ADDRESS_STREET_NAME1	WEB_SITE
WEB_ADDRESS_STREET_TYPE		WEB_SITE
WEB_ADDRESS_SUITE_NUM		WEB_SITE
WEB_ADDRESS_CITY			WEB_SITE
WEB_ADDRESS_COUNTY			WEB_SITE
WEB_ADDRESS_STATE			WEB_SITE
WEB_ADDRESS_ZIP				WEB_SITE
WEB_ADDRESS_COUNTRY			WEB_SITE
WEB_ADDRESS_GMT_OFFSET		WEB_SITE
WEB_TAX_PERCENTAGE	WEB_SITE	
WEB_NULLS			WEB_SITE	2
WEB_ADDRESS			WEB_SITE	7
WEB_SCD				WEB_SITE	70
#
# DBGEN_VERSION
#
DV_VERSION	DBGEN_VERSION	0
DV_CREATE_DATE	DBGEN_VERSION	0
DV_CREATE_TIME	DBGEN_VERSION	0
DV_CMDLINE_ARGS	DBGEN_VERSION	0
#
# VALIDATION
#
VALIDATE_STREAM		DBGEN_VERSION	0
#
# S_BRAND
#
S_BRAND_ID				S_BRAND		0
S_BRAND_SUBCLASS_ID		S_BRAND		0
S_BRAND_MANAGER_ID		S_BRAND
S_BRAND_MANUFACTURER_ID	S_BRAND
S_BRAND_NAME			S_BRAND		6
#
# S_CUSTOMER_ADDRESS
#
S_CADR_ID		S_CUSTOMER_ADDRESS	0
S_CADR_ADDRESS_STREET_NUMBER	S_CUSTOMER_ADDRESS
S_CADR_ADDRESS_STREET_NAME1		S_CUSTOMER_ADDRESS
S_CADR_ADDRESS_STREET_NAME2		S_CUSTOMER_ADDRESS
S_CADR_ADDRESS_STREET_TYPE		S_CUSTOMER_ADDRESS
S_CADR_ADDRESS_SUITE_NUM		S_CUSTOMER_ADDRESS
S_CADR_ADDRESS_CITY				S_CUSTOMER_ADDRESS
S_CADR_ADDRESS_COUNTY			S_CUSTOMER_ADDRESS
S_CADR_ADDRESS_STATE			S_CUSTOMER_ADDRESS
S_CADR_ADDRESS_ZIP				S_CUSTOMER_ADDRESS
S_CADR_ADDRESS_COUNTRY			S_CUSTOMER_ADDRESS
S_BADDR_ADDRESS	S_CUSTOMER_ADDRESS	7 CA_ADDRESS
#
# S_CALL_CENTER
#	
S_CALL_CENTER_ID			S_CALL_CENTER	
S_CALL_CENTER_DIVISION_ID	S_CALL_CENTER
S_CALL_CENTER_OPEN_DATE		S_CALL_CENTER
S_CALL_CENTER_CLOSED_DATE	S_CALL_CENTER	0
S_CALL_CENTER_NAME			S_CALL_CENTER	0
S_CALL_CENTER_CLASS			S_CALL_CENTER	0
S_CALL_CENTER_EMPLOYEES		S_CALL_CENTER
S_CALL_CENTER_SQFT			S_CALL_CENTER
S_CALL_CENTER_HOURS			S_CALL_CENTER
S_CALL_CENTER_MANAGER_ID	S_CALL_CENTER
S_CALL_CENTER_MARKET_ID		S_CALL_CENTER
S_CALL_CENTER_ADDRESS_ID	S_CALL_CENTER
S_CALL_CENTER_TAX_PERCENTAGE	S_CALL_CENTER
S_CALL_CENTER_SCD			S_CALL_CENTER
# 
# S_CATALOG
#
S_CATALOG_NUMBER			S_CATALOG	0
S_CATALOG_START_DATE		S_CATALOG
S_CATALOG_END_DATE			S_CATALOG
S_CATALOG_DESC				S_CATALOG	10
S_CATALOG_TYPE				S_CATALOG
#
# S_CATALOG_ORDER
#
S_CORD_ID				S_CATALOG_ORDER	0
S_CORD_BILL_CUSTOMER_ID	S_CATALOG_ORDER
S_CORD_SHIP_CUSTOMER_ID	S_CATALOG_ORDER 2
S_CORD_ORDER_DATE		S_CATALOG_ORDER
S_CORD_ORDER_TIME		S_CATALOG_ORDER
S_CORD_SHIP_MODE_ID		S_CATALOG_ORDER 
S_CORD_CALL_CENTER_ID	S_CATALOG_ORDER
S_CLIN_ITEM_ID			S_CATALOG_ORDER
S_CORD_COMMENT			S_CATALOG_ORDER	100
#
# S_CATALOG_LINEITEM
#
S_CLIN_ORDER_ID			S_CATALOG_ORDER_LINEITEM	1
S_CLIN_LINE_NUMBER		S_CATALOG_ORDER_LINEITEM	0
S_CLIN_PROMOTION_ID		S_CATALOG_ORDER_LINEITEM   1
S_CLIN_QUANTITY			S_CATALOG_ORDER_LINEITEM   1
S_CLIN_COUPON_AMT		S_CATALOG_ORDER_LINEITEM   1
S_CLIN_WAREHOUSE_ID		S_CATALOG_ORDER_LINEITEM   1
S_CLIN_SHIP_DATE		S_CATALOG_ORDER_LINEITEM 1
S_CLIN_CATALOG_ID		S_CATALOG_ORDER_LINEITEM   1
S_CLIN_CATALOG_PAGE_ID	S_CATALOG_ORDER_LINEITEM   2
S_CLIN_PRICING			S_CATALOG_ORDER_LINEITEM	8
S_CLIN_SHIP_COST		S_CATALOG_ORDER_LINEITEM	0
S_CLIN_IS_RETURNED		S_CATALOG_ORDER_LINEITEM   1
S_CLIN_PERMUTE		S_CATALOG_ORDER_LINEITEM 0
#
# S_CATALOG_PAGE
#
S_CATALOG_PAGE_CATALOG_NUMBER		S_CATALOG_PAGE	0
S_CATALOG_PAGE_NUMBER				S_CATALOG_PAGE	0
S_CATALOG_PAGE_DEPARTMENT			S_CATALOG_PAGE
S_CP_ID								S_CATALOG_PAGE
S_CP_START_DATE						S_CATALOG_PAGE
S_CP_END_DATE							S_CATALOG_PAGE
S_CP_DESCRIPTION						S_CATALOG_PAGE 100 CP_DESCRIPTION
S_CP_TYPE								S_CATALOG_PAGE
#
# S_CATALOG_PROMOTIONAL_ITEM
#
S_CATALOG_PROMOTIONAL_ITEM_CATALOG_NUMBER		S_CATALOG_PROMOTIONAL_ITEM
S_CATALOG_PROMOTIONAL_ITEM_CATALOG_PAGE_NUMBER	S_CATALOG_PROMOTIONAL_ITEM
S_CATALOG_PROMOTIONAL_ITEM_ITEM_ID				S_CATALOG_PROMOTIONAL_ITEM
S_CATALOG_PROMOTIONAL_ITEM_PROMOTION_ID			S_CATALOG_PROMOTIONAL_ITEM	0
#
# S_CATALOG_RETURNS
#
S_CRET_CALL_CENTER_ID	S_CATALOG_RETURNS	9
S_CRET_ORDER_ID			S_CATALOG_RETURNS	0
S_CRET_LINE_NUMBER		S_CATALOG_RETURNS	0
S_CRET_ITEM_ID			S_CATALOG_RETURNS	0
S_CRET_RETURN_CUSTOMER_ID		S_CATALOG_RETURNS	0
S_CRET_REFUND_CUSTOMER_ID		S_CATALOG_RETURNS	0
S_CRET_DATE				S_CATALOG_RETURNS	9
S_CRET_TIME				S_CATALOG_RETURNS	18
S_CRET_QUANTITY			S_CATALOG_RETURNS	0
S_CRET_AMOUNT			S_CATALOG_RETURNS	0
S_CRET_TAX				S_CATALOG_RETURNS	0
S_CRET_FEE				S_CATALOG_RETURNS	0
S_CRET_SHIP_COST		S_CATALOG_RETURNS	0
S_CRET_REFUNDED_CASH	S_CATALOG_RETURNS	0
S_CRET_REVERSED_CHARGE	S_CATALOG_RETURNS	0
S_CRET_MERCHANT_CREDIT	S_CATALOG_RETURNS	0
S_CRET_REASON_ID		S_CATALOG_RETURNS	9
S_CRET_PRICING			S_CATALOG_RETURNS	72
S_CRET_SHIPMODE_ID		S_CATALOG_RETURNS 9
S_CRET_WAREHOUSE_ID		S_CATALOG_RETURNS 9
S_CRET_CATALOG_PAGE_ID	S_CATALOG_RETURNS	0
#
# S_CATEGORY
#
S_CATEGORY_ID			S_CATEGORY	0
S_CATEGORY_NAME			S_CATEGORY	0
S_CATEGORY_DESC			S_CATEGORY	10
#
# S_CLASS
#
S_CLASS_ID			S_CLASS	0
S_CLASS_SUBCAT_ID	S_CLASS
S_CLASS_DESC		S_CLASS	10
#
# S_COMPANY
#
S_COMPANY_ID		S_COMPANY	0
S_COMPANY_NAME		S_COMPANY	0
#
# S_CUSTOMER
#
S_CUST_ID			S_CUSTOMER	0
S_CUST_SALUTATION	S_CUSTOMER
S_CUST_LAST_NAME	S_CUSTOMER
S_CUST_FIRST_NAME	S_CUSTOMER
S_CUST_PREFERRED_FLAG	S_CUSTOMER	
S_CUST_BIRTH_DATE	S_CUSTOMER
S_CUST_FIRST_PURCHASE_DATE	S_CUSTOMER
S_CUST_FIRST_SHIPTO_DATE	S_CUSTOMER
S_CUST_BIRTH_COUNTRY	S_CUSTOMER
S_CUST_LOGIN		S_CUSTOMER  25
S_CUST_EMAIL		S_CUSTOMER	23
S_CUST_LAST_LOGIN	S_CUSTOMER
S_CUST_LAST_REVIEW	S_CUSTOMER
S_CUST_PRIMARY_MACHINE	S_CUSTOMER 4
S_CUST_SECONDARY_MACHINE	S_CUSTOMER 4
S_CUST_ADDRESS		S_CUSTOMER	7
S_CUST_ADDRESS_STREET_NUM	S_CUSTOMER
S_CUST_ADDRESS_STREET_NAME1	S_CUSTOMER
S_CUST_ADDRESS_STREET_NAME2	S_CUSTOMER
S_CUST_ADDRESS_STREET_TYPE	S_CUSTOMER
S_CUST_ADDRESS_SUITE_NUM	S_CUSTOMER
S_CUST_ADDRESS_CITY			S_CUSTOMER
S_CUST_ADDRESS_ZIP			S_CUSTOMER
S_CUST_ADDRESS_COUNTY		S_CUSTOMER
S_CUST_ADDRESS_STATE		S_CUSTOMER
S_CUST_ADDRESS_COUNTRY		S_CUSTOMER
S_CUST_LOCATION_TYPE		S_CUSTOMER
S_CUST_GENDER		S_CUSTOMER
S_CUST_MARITAL_STATUS		S_CUSTOMER
S_CUST_EDUCATION	S_CUSTOMER
S_CUST_CREDIT_RATING		S_CUSTOMER
S_CUST_PURCHASE_ESTIMATE	S_CUSTOMER
S_CUST_BUY_POTENTIAL	S_CUSTOMER
S_CUST_DEPENDENT_CNT	S_CUSTOMER
S_CUST_EMPLOYED_CNT	S_CUSTOMER
S_CUST_COLLEGE_CNT	S_CUSTOMER
S_CUST_VEHICLE_CNT	S_CUSTOMER
S_CUST_INCOME		S_CUSTOMER
#
# S_DIVISION
#
S_DIVISION_ID		S_DIVISION	0
S_DIVISION_COMPANY	S_DIVISION	0
S_DIVISION_NAME		S_DIVISION	0
#
# S_INVENTORY
#
S_INVN_WAREHOUSE	S_INVENTORY
S_INVN_ITEM			S_INVENTORY
S_INVN_DATE			S_INVENTORY 2
S_INVN_QUANTITY		S_INVENTORY
#
# S_ITEM
#
S_ITEM_ID			S_ITEM	1
S_ITEM_PERMUTE		S_ITEM	0
S_ITEM_PRODUCT_ID	S_ITEM   1  
S_ITEM_DESC			S_ITEM	200 I_ITEM_DESC
S_ITEM_LIST_PRICE	S_ITEM   2  I_CURRENT_PRICE
S_ITEM_WHOLESALE_COST	S_ITEM 1 I_WHOLESALE_COST
S_ITEM_MANAGER_ID	S_ITEM 1 I_MANAGER_ID
S_ITEM_SIZE			S_ITEM 1 I_SIZE
S_ITEM_FORMULATION	S_ITEM 50 I_FORMULATION
S_ITEM_FLAVOR		S_ITEM 1 I_COLOR
S_ITEM_UNITS		S_ITEM 1 I_UNITS
S_ITEM_CONTAINER	S_ITEM 1 I_CONTAINER
S_ITEM_SCD	S_ITEM 1 I_SCD
#
# S_MANAGER
#
S_MANAGER_ID	S_MANAGER	0
S_MANAGER_NAME	S_MANAGER	2
#
# S_MANUFACTURER
#
S_MANUFACTURER_ID	S_MANUFACTURER	0
S_MANUFACTURER_NAME	S_MANUFACTURER	0
#
# S_MARKET
#
S_MARKET_ID			S_MARKET	0
S_MARKET_CLASS_NAME	S_MARKET	0
S_MARKET_DESC		S_MARKET	10
S_MARKET_MANAGER_ID	S_MARKET
#
# S_PRODUCT
#
S_PRODUCT_ID		S_PRODUCT	0
S_PRODUCT_BRAND_ID	S_PRODUCT
S_PRODUCT_NAME		S_PRODUCT	0
S_PRODUCT_TYPE		S_PRODUCT
#
# S_PROMOTION
#
S_PROMOTION_ID		S_PROMOTION	
S_PROMOTION_ITEM_ID	S_PROMOTION
S_PROMOTION_START_DATE	S_PROMOTION
S_PROMOTION_END_DATE	S_PROMOTION
S_PROMOTION_COST	S_PROMOTION
S_PROMOTION_RESPONSE_TARGET	S_PROMOTION
S_PROMOTION_DMAIL	S_PROMOTION	0
S_PROMOTION_EMAIL	S_PROMOTION	0
S_PROMOTION_CATALOG	S_PROMOTION	0
S_PROMOTION_TV		S_PROMOTION	0
S_PROMOTION_RADIO	S_PROMOTION	0
S_PROMOTION_PRESS	S_PROMOTION	0
S_PROMOTION_EVENT	S_PROMOTION	0
S_PROMOTION_DEMO	S_PROMOTION	0
S_PROMOTION_DETAILS	S_PROMOTION	100 P_CHANNEL_DETAILS
S_PROMOTION_PURPOSE	S_PROMOTION
S_PROMOTION_DISCOUNT_ACTIVE	S_PROMOTION
S_PROMOTION_DISCOUNT_PCT	S_PROMOTION
S_PROMOTION_NAME	S_PROMOTION	0
S_PROMOTION_BITFIELD	S_PROMOTION
#
# S_PURCHASE
#
S_PURCHASE_ID			S_PURCHASE	0
S_PURCHASE_STORE_ID		S_PURCHASE
S_PURCHASE_CUSTOMER_ID	S_PURCHASE
S_PURCHASE_DATE			S_PURCHASE
S_PURCHASE_TIME			S_PURCHASE
S_PURCHASE_REGISTER		S_PURCHASE
S_PURCHASE_CLERK		S_PURCHASE
S_PURCHASE_COMMENT		S_PURCHASE	100
S_PURCHASE_PRICING		S_PURCHASE	7
S_PLINE_ITEM_ID			S_PURCHASE
#
# S_PLINE
#
S_PLINE_PURCHASE_ID		S_PURCHASE_LINEITEM 12
S_PLINE_NUMBER			S_PURCHASE_LINEITEM	 12
S_PLINE_PROMOTION_ID	S_PURCHASE_LINEITEM 12
S_PLINE_SALE_PRICE		S_PURCHASE_LINEITEM 12
S_PLINE_QUANTITY		S_PURCHASE_LINEITEM 12
S_PLINE_COUPON_AMT		S_PURCHASE_LINEITEM 12
S_PLINE_COMMENT			S_PURCHASE_LINEITEM	1200
S_PLINE_PRICING			S_PURCHASE_LINEITEM	96
S_PLINE_IS_RETURNED		S_PURCHASE_LINEITEM  12
S_PLINE_PERMUTE		S_PURCHASE_LINEITEM 0
#
# S_REASON
#
S_REASON_ID		S_REASON	0
S_REASON_DESC	S_REASON	10
#
# S_STORE
#
S_STORE_ID				S_STORE	
S_STORE_ADDRESS_ID		S_STORE
S_STORE_DIVISION_ID		S_STORE
S_STORE_OPEN_DATE		S_STORE
S_STORE_CLOSE_DATE		S_STORE
S_STORE_NAME			S_STORE	0
S_STORE_CLASS			S_STORE	0
S_STORE_EMPLOYEES		S_STORE
S_STORE_FLOOR_SPACE		S_STORE
S_STORE_HOURS			S_STORE
S_STORE_MARKET_MANAGER_ID	S_STORE	0
S_STORE_MANAGER_ID		S_STORE
S_STORE_MARKET_ID		S_STORE
S_STORE_GEOGRAPHY_CLASS	S_STORE
S_STORE_TAX_PERCENTAGE	S_STORE
#
# S_STORE_PROMOTIONAL_ITEM
#
S_SITM_PROMOTION_ID	S_STORE_PROMOTIONAL_ITEM
S_SITM_ITEM_ID		S_STORE_PROMOTIONAL_ITEM
S_SITM_STORE_ID		S_STORE_PROMOTIONAL_ITEM
#
# S_STORE_RETURNS
#
S_SRET_STORE_ID	S_STORE_RETURNS	0
S_SRET_PURCHASE_ID	S_STORE_RETURNS	0
S_SRET_LINENUMBER	S_STORE_RETURNS	0
S_SRET_ITEM_ID		S_STORE_RETURNS	0
S_SRET_CUSTOMER_ID	S_STORE_RETURNS	0
S_SRET_RETURN_DATE	S_STORE_RETURNS	24
S_SRET_RETURN_TIME	S_STORE_RETURNS	12
S_SRET_TICKET_NUMBER	S_STORE_RETURNS	0
S_SRET_RETURN_QUANTITY	S_STORE_RETURNS	0
S_SRET_RETURN_AMT		S_STORE_RETURNS	0
S_SRET_RETURN_TAX		S_STORE_RETURNS	0
S_SRET_RETURN_FEE		S_STORE_RETURNS	0
S_SRET_RETURN_SHIP_COST	S_STORE_RETURNS	0
S_SRET_REFUNDED_CASH	S_STORE_RETURNS	0
S_SRET_REVERSED_CHARGE	S_STORE_RETURNS	0
S_SRET_MERCHANT_CREDIT		S_STORE_RETURNS	0	
S_SRET_REASON_ID		S_STORE_RETURNS	12
S_SRET_PRICING			S_STORE_RETURNS	84
#
# S_SUBCATEGORY
#
S_SBCT_ID			S_SUBCATEGORY	0
S_SBCT_CATEGORY_ID	S_SUBCATEGORY
S_SBCT_NAME			S_SUBCATEGORY	0
S_SBCT_DESC			S_SUBCATEGORY	10
#
# S_SUBCLASS
#
S_SUBC_ID			S_SUBCLASS	0
S_SUBC_CLASS_ID		S_SUBCLASS
S_SUBC_NAME			S_SUBCLASS	0
S_SUBC_DESC			S_SUBCLASS	10
#
# S_WAREHOUSE
#
S_WRHS_ID			S_WAREHOUSE	
S_WRHS_DESC			S_WAREHOUSE	10
S_WRHS_SQFT			S_WAREHOUSE
S_WRHS_ADDRESS_ID	S_WAREHOUSE
#
# S_WEB_ORDER
#
S_WORD_ID				S_WEB_ORDER	
S_WORD_BILL_CUSTOMER_ID	S_WEB_ORDER
S_WORD_SHIP_CUSTOMER_ID	S_WEB_ORDER 2
S_WORD_ORDER_DATE		S_WEB_ORDER
S_WORD_ORDER_TIME		S_WEB_ORDER
S_WORD_SHIP_MODE_ID		S_WEB_ORDER	
S_WORD_WEB_SITE_ID		S_WEB_ORDER
S_WORD_COMMENT			S_WEB_ORDER 100
S_WLIN_ITEM_ID		S_WEB_ORDER
#
# S_WEB_ORDER_LINEITEM
#
S_WLIN_ID			S_WEB_ORDER_LINEITEM 12
S_WLIN_LINE_NUMBER	S_WEB_ORDER_LINEITEM	0
S_WLIN_PROMOTION_ID	S_WEB_ORDER_LINEITEM 12
S_WLIN_QUANTITY		S_WEB_ORDER_LINEITEM 12
S_WLIN_COUPON_AMT	S_WEB_ORDER_LINEITEM 12
S_WLIN_WAREHOUSE_ID	S_WEB_ORDER_LINEITEM 12
S_WLIN_SHIP_DATE	S_WEB_ORDER_LINEITEM 12
S_WLIN_WEB_PAGE_ID	S_WEB_ORDER_LINEITEM 12
S_WLIN_PRICING		S_WEB_ORDER_LINEITEM	96
S_WLIN_SHIP_COST	S_WEB_ORDER_LINEITEM	0
S_WLIN_IS_RETURNED	S_WEB_ORDER_LINEITEM 12
S_WLIN_PERMUTE	S_WEB_ORDER_LINEITEM 0
#
# S_WEB_PAGE
#
S_WPAG_SITE_ID		S_WEB_PAGE
S_WPAG_ID			S_WEB_PAGE
S_WPAG_CREATE_DATE	S_WEB_PAGE
S_WPAG_ACCESS_DATE	S_WEB_PAGE
S_WPAG_AUTOGEN_FLAG	S_WEB_PAGE
S_WPAG_DEPARTMENT	S_WEB_PAGE
S_WPAG_URL			S_WEB_PAGE
S_WPAG_TYPE			S_WEB_PAGE
S_WPAG_CHAR_CNT		S_WEB_PAGE
S_WPAG_LINK_CNT		S_WEB_PAGE
S_WPAG_IMAGE_CNT	S_WEB_PAGE
S_WPAG_MAX_AD_CNT	S_WEB_PAGE
S_WPAG_PERMUTE	S_WEB_PAGE 0
#
# S_WEB_PROMOTIONAL_ITEM
#
S_WITM_SITE_ID		S_WEB_PROMOTIONAL_ITEM
S_WITM_PAGE_ID		S_WEB_PROMOTIONAL_ITEM
S_WITM_ITEM_ID		S_WEB_PROMOTIONAL_ITEM
S_WITM_PROMOTION_ID	S_WEB_PROMOTIONAL_ITEM
#
# S_WEB_RETURNS
#
S_WRET_SITE_ID		S_WEB_RETURNS	0			
S_WRET_ORDER_ID		S_WEB_RETURNS	0
S_WRET_LINE_NUMBER	S_WEB_RETURNS	0
S_WRET_ITEM_ID		S_WEB_RETURNS	0
S_WRET_RETURN_CUST_ID	S_WEB_RETURNS	0
S_WRET_REFUND_CUST_ID	S_WEB_RETURNS	0
S_WRET_RETURN_DATE	S_WEB_RETURNS	24
S_WRET_RETURN_TIME	S_WEB_RETURNS	12
S_WRET_REASON_ID	S_WEB_RETURNS	12
S_WRET_PRICING		S_WEB_RETURNS	84
#
# S_WEB_SITE
#
S_WSIT_ID			S_WEB_SITE
S_WSIT_OPEN_DATE	S_WEB_SITE
S_WSIT_CLOSE_DATE	S_WEB_SITE
S_WSIT_NAME			S_WEB_SITE	0
S_WSIT_ADDRESS_ID	S_WEB_SITE
S_WSIT_DIVISION_ID	S_WEB_SITE
S_WSIT_CLASS		S_WEB_SITE
S_WSIT_MANAGER_ID	S_WEB_SITE
S_WSIT_MARKET_ID	S_WEB_SITE
S_WSIT_TAX_PERCENTAGE	S_WEB_SITE
#
# S_ZIP_TO_GMT
#
S_ZIPG_ZIP			S_ZIPG	0
S_ZIPG_GMT			S_ZIPG	0

