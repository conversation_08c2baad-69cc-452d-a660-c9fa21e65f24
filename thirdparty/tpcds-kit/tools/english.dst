--
-- Legal Notice
--
-- This document and associated source code (the "Work") is a part of a
-- benchmark specification maintained by the TPC.
--
-- The TPC reserves all right, title, and interest to the Work as provided
-- under U.S. and international laws, including without limitation all patent
-- and trademark rights therein.
--
-- No Warranty
--
-- 1.1 TO THE MAXIMUM EXTENT PERMITTED BY APPLICABLE LAW, THE INFORMATION
--     CONTAINED HEREIN IS PROVIDED "AS IS" AND WITH ALL FAULTS, AND THE
--     AUTHORS AND DEVELOPERS OF THE WORK HEREBY DISCLAIM ALL OTHER
--     WARRANTIES AND CONDITIONS, EITHER EXPRESS, IMPLIED OR STATUTORY,
--     INCLUDING, BUT NOT LIMITED TO, ANY (IF ANY) IMPLIED WARRANTIES,
--     DUTIES OR CONDITIONS OF MERCHANTABILITY, OF FITNESS FOR A PARTICULAR
--     PURPOSE, OF ACCURACY OR COMPLETENESS OF RESPONSES, OF RESULTS, OF
--     W<PERSON><PERSON><PERSON><PERSON>IKE EFFORT, OF LACK OF VIRUSES, AND OF LACK OF NEGLIGENCE.
--     ALSO, THERE IS NO WARRANTY OR CONDITION OF TITLE, QUIET ENJOYMENT,
--     QUIET POSSESSION, CORRESPONDENCE TO DESCRIPTION OR NON-INFRINGEMENT
--     WITH REGARD TO THE WORK.
-- 1.2 IN NO EVENT WILL ANY AUTHOR OR DEVELOPER OF THE WORK BE LIABLE TO
--     ANY OTHER PARTY FOR ANY DAMAGES, INCLUDING BUT NOT LIMITED TO THE
--     COST OF PROCURING SUBSTITUTE GOODS OR SERVICES, LOST PROFITS, LOSS
--     OF USE, LOSS OF DATA, OR ANY INCIDENTAL, CONSEQUENTIAL, DIRECT,
--     INDIRECT, OR SPECIAL DAMAGES WHETHER UNDER CONTRACT, TORT, WARRANTY,
--     OR OTHERWISE, ARISING IN ANY WAY OUT OF THIS OR ANY OTHER AGREEMENT
--     RELATING TO THE WORK, WHETHER OR NOT SUCH AUTHOR OR DEVELOPER HAD
--     ADVANCE NOTICE OF THE POSSIBILITY OF SUCH DAMAGES.
--
-- Contributors:
-- Gradient Systems
--
--
--
--
-- 
-- adjectives.dst based on the common word list
create adjectives;
set types = (varchar);
set weights = 1;
add ("other":116);
add ("new":108);
add ("good":59);
add ("old":49);
add ("different":43);
add ("local":41);
add ("social":40);
add ("small":40);
add ("great":39);
add ("important":36);
add ("national":35);
add ("british":33);
add ("possible":32);
add ("large":32);
add ("political":29);
add ("young":28);
add ("public":28);
add ("high":27);
add ("available":26);
add ("able":26);
add ("full":24);
add ("only":22);
add ("major":22);
add ("main":22);
add ("economic":22);
add ("real":21);
add ("long":21);
add ("likely":21);
add ("international":21);
add ("special":20);
add ("particular":19);
add ("difficult":19);
add ("certain":19);
add ("black":19);
add ("big":19);
add ("little":18);
add ("general":18);
add ("free":18);
add ("clear":18);
add ("white":17);
add ("similar":17);
add ("necessary":17);
add ("english":17);
add ("early":17);
add ("central":17);
add ("personal":16);
add ("common":16);
add ("true":15);
add ("strong":15);
add ("single":15);
add ("recent":15);
add ("private":15);
add ("foreign":15);
add ("financial":15);
add ("american":15);
add ("various":14);
add ("royal":14);
add ("poor":14);
add ("open":14);
add ("european":14);
add ("whole":13);
add ("simple":13);
add ("right":13);
add ("natural":13);
add ("sure":12);
add ("short":12);
add ("modern":12);
add ("legal":12);
add ("human":12);
add ("following":12);
add ("final":12);
add ("significant":11);
add ("serious":11);
add ("prime":11);
add ("previous":11);
add ("normal":11);
add ("industrial":11);
add ("current":11);
add ("bad":11);
add ("wrong":10);
add ("successful":10);
add ("specific":10);
add ("red":10);
add ("popular":10);
add ("military":10);
add ("low":10);
add ("labour":10);
add ("dead":10);
add ("concerned":10);
add ("basic":10);
add ("appropriate":10);
add ("alone":10);
add ("useful":9);
add ("traditional":9);
add ("scottish":9);
add ("professional":9);
add ("present":9);
add ("physical":9);
add ("original":9);
add ("individual":9);
add ("happy":9);
add ("fine":9);
add ("effective":9);
add ("easy":9);
add ("considerable":9);
add ("complete":9);
add ("aware":9);
add ("wide":8);
add ("total":8);
add ("responsible":8);
add ("ready":8);
add ("obvious":8);
add ("medical":8);
add ("left":8);
add ("interesting":8);
add ("independent":8);
add ("heavy":8);
add ("hard":8);
add ("existing":8);
add ("essential":8);
add ("direct":8);
add ("civil":8);
add ("blue":8);
add ("western":7);
add ("top":7);
add ("separate":7);
add ("relevant":7);
add ("regular":7);
add ("practical":7);
add ("powerful":7);
add ("positive":7);
add ("nuclear":7);
add ("light":7);
add ("late":7);
add ("huge":7);
add ("hot":7);
add ("future":7);
add ("french":7);
add ("extra":7);
add ("environmental":7);
add ("complex":7);
add ("commercial":7);
add ("close":7);
add ("chief":7);
add ("beautiful":7);
add ("annual":7);
add ("active":7);
add ("working":6);
add ("very":6);
add ("usual":6);
add ("unable":6);
add ("technical":6);
add ("soviet":6);
add ("sorry":6);
add ("sexual":6);
add ("rich":6);
add ("religious":6);
add ("regional":6);
add ("primary":6);
add ("ordinary":6);
add ("nice":6);
add ("male":6);
add ("internal":6);
add ("inc":6);
add ("impossible":6);
add ("fresh":6);
add ("formal":6);
add ("famous":6);
add ("excellent":6);
add ("domestic":6);
add ("dark":6);
add ("cultural":6);
add ("average":6);
add ("additional":6);
add ("warm":5);
add ("urban":5);
add ("upper":5);
add ("unlikely":5);
add ("tiny":5);
add ("suitable":5);
add ("sufficient":5);
add ("substantial":5);
add ("strange":5);
add ("soft":5);
add ("senior":5);
add ("scientific":5);
add ("safe":5);
add ("rural":5);
add ("reasonable":5);
add ("proper":5);
add ("perfect":5);
add ("past":5);
add ("mental":5);
add ("key":5);
add ("joint":5);
add ("interested":5);
add ("immediate":5);
add ("historical":5);
add ("german":5);
add ("familiar":5);
add ("expensive":5);
add ("equal":5);
add ("empty":5);
add ("educational":5);
add ("due":5);
add ("detailed":5);
add ("democratic":5);
add ("deep":5);
add ("dangerous":5);
add ("critical":5);
add ("correct":5);
add ("cold":5);
add ("christian":5);
add ("bright":5);
add ("apparent":5);
add ("alternative":5);
add ("afraid":5);
add ("actual":5);
add ("wonderful":4);
add ("wild":4);
add ("vital":4);
add ("vast":4);
add ("unknown":4);
add ("united":4);
add ("unique":4);
add ("typical":4);
add ("thin":4);
add ("tall":4);
add ("subsequent":4);
add ("standard":4);
add ("southern":4);
add ("severe":4);
add ("secondary":4);
add ("russian":4);
add ("rare":4);
add ("quiet":4);
add ("potential":4);
add ("permanent":4);
add ("parliamentary":4);
add ("official":4);
add ("northern":4);
add ("narrow":4);
add ("minor":4);
add ("massive":4);
add ("long-term":4);
add ("limited":4);
add ("liberal":4);
add ("level":4);
add ("leading":4);
add ("italian":4);
add ("irish":4);
add ("inner":4);
add ("initial":4);
add ("increased":4);
add ("growing":4);
add ("grey":4);
add ("green":4);
add ("fundamental":4);
add ("front":4);
add ("female":4);
add ("far":4);
add ("fair":4);
add ("external":4);
add ("extensive":4);
add ("entire":4);
add ("elderly":4);
add ("eastern":4);
add ("dry":4);
add ("crucial":4);
add ("criminal":4);
add ("corporate":4);
add ("contemporary":4);
add ("constant":4);
add ("careful":4);
add ("capable":4);
add ("busy":4);
add ("broad":4);
add ("brief":4);
add ("attractive":4);
add ("angry":4);
add ("ancient":4);
add ("academic":4);
add ("yellow":3);
add ("wooden":3);
add ("willing":3);
add ("widespread":3);
add ("welsh":3);
add ("weak":3);
add ("voluntary":3);
add ("visual":3);
add ("video-taped":3);
add ("valuable":3);
add ("used":3);
add ("unusual":3);
add ("tired":3);
add ("terrible":3);
add ("temporary":3);
add ("surprising":3);
add ("supreme":3);
add ("sudden":3);
add ("subject":3);
add ("statutory":3);
add ("spanish":3);
add ("solid":3);
add ("slow":3);
add ("silent":3);
add ("sharp":3);
add ("sensitive":3);
add ("sad":3);
add ("running":3);
add ("rough":3);
add ("remarkable":3);
add ("remaining":3);
add ("relative":3);
add ("rapid":3);
add ("quick":3);
add ("pure":3);
add ("proposed":3);
add ("pale":3);
add ("overall":3);
add ("odd":3);
add ("numerous":3);
add ("negative":3);
add ("married":3);
add ("lucky":3);
add ("lovely":3);
add ("living":3);
add ("literary":3);
add ("keen":3);
add ("just":3);
add ("japanese":3);
add ("indian":3);
add ("healthy":3);
add ("guilty":3);
add ("golden":3);
add ("global":3);
add ("glad":3);
add ("genuine":3);
add ("friendly":3);
add ("firm":3);
add ("federal":3);
add ("expected":3);
add ("exciting":3);
add ("enormous":3);
add ("emotional":3);
add ("electronic":3);
add ("efficient":3);
add ("dramatic":3);
add ("double":3);
add ("distinct":3);
add ("dependent":3);
add ("daily":3);
add ("conventional":3);
add ("conservative":3);
add ("confident":3);
add ("comprehensive":3);
add ("competitive":3);
add ("coming":3);
add ("comfortable":3);
add ("clean":3);
add ("classical":3);
add ("capital":3);
add ("brown":3);
add ("back":3);
add ("alive":3);
add ("agricultural":3);
add ("african":3);
add ("administrative":3);
add ("adequate":3);
add ("acceptable":3);
add ("absolute":3);
add ("written":2);
add ("wet":2);
add ("vulnerable":2);
add ("visible":2);
add ("violent":2);
add ("victorian":2);
add ("valid":2);
add ("urgent":2);
add ("universal":2);
add ("unexpected":2);
add ("unemployed":2);
add ("underlying":2);
add ("ultimate":2);
add ("trying":2);
add ("tough":2);
add ("tory":2);
add ("thick":2);
add ("theoretical":2);
add ("sweet":2);
add ("surprised":2);
add ("stupid":2);
add ("strategic":2);
add ("statistical":2);
add ("spiritual":2);
add ("specialist":2);
add ("sound":2);
add ("sophisticated":2);
add ("sole":2);
add ("so-called":2);
add ("smooth":2);
add ("slight":2);
add ("silver":2);
add ("silly":2);
add ("sensible":2);
add ("secret":2);
add ("satisfactory":2);
add ("round":2);
add ("roman":2);
add ("revolutionary":2);
add ("residential":2);
add ("remote":2);
add ("reliable":2);
add ("related":2);
add ("raw":2);
add ("rational":2);
add ("radical":2);
add ("psychological":2);
add ("proud":2);
add ("principal":2);
add ("pretty":2);
add ("presidential":2);
add ("pregnant":2);
add ("precise":2);
add ("pleasant":2);
add ("pink":2);
add ("patient":2);
add ("overseas":2);
add ("outstanding":2);
add ("outside":2);
add ("outer":2);
add ("organic":2);
add ("opposite":2);
add ("occasional":2);
add ("net":2);
add ("nervous":2);
add ("mutual":2);
add ("musical":2);
add ("multiple":2);
add ("moving":2);
add ("moral":2);
add ("monetary":2);
add ("modest":2);
add ("minute":2);
add ("middle":2);
add ("mere":2);
add ("mean":2);
add ("maximum":2);
add ("material":2);
add ("mass":2);
add ("marginal":2);
add ("mad":2);
add ("logical":2);
add ("live":2);
add ("linguistic":2);
add ("like":2);
add ("liable":2);
add ("known":2);
add ("junior":2);
add ("judicial":2);
add ("jewish":2);
add ("isolated":2);
add ("involved":2);
add ("intense":2);
add ("informal":2);
add ("inevitable":2);
add ("increasing":2);
add ("inadequate":2);
add ("impressive":2);
add ("ill":2);
add ("illegal":2);
add ("identical":2);
add ("ideal":2);
add ("honest":2);
add ("holy":2);
add ("historic":2);
add ("helpful":2);
add ("head":2);
add ("grateful":2);
add ("grand":2);
add ("gold":2);
add ("given":2);
add ("gentle":2);
add ("generous":2);
add ("gastric":2);
add ("fun":2);
add ("funny":2);
add ("frequent":2);
add ("forward":2);
add ("flexible":2);
add ("flat":2);
add ("fixed":2);
add ("favorite":2);
add ("fat":2);
add ("fast":2);
add ("false":2);
add ("extreme":2);
add ("extraordinary":2);
add ("experimental":2);
add ("exact":2);
add ("evident":2);
add ("everyday":2);
add ("ethnic":2);
add ("electric":2);
add ("electrical":2);
add ("electoral":2);
add ("dominant":2);
add ("distinctive":2);
add ("distant":2);
add ("disabled":2);
add ("dirty":2);
add ("determined":2);
add ("desperate":2);
add ("desirable":2);
add ("dear":2);
add ("deaf":2);
add ("curious":2);
add ("creative":2);
add ("controversial":2);
add ("continuous":2);
add ("constitutional":2);
add ("consistent":2);
add ("conscious":2);
add ("clinical":2);
add ("chinese":2);
add ("chemical":2);
add ("cheap":2);
add ("catholic":2);
add ("broken":2);
add ("brilliant":2);
add ("bloody":2);
add ("blind":2);
add ("bitter":2);
add ("bare":2);
add ("awful":2);
add ("automatic":2);
add ("australian":2);
add ("assistant":2);
add ("asleep":2);
add ("armed":2);
add ("anxious":2);
add ("advanced":2);
add ("adult":2);
add ("acute":2);
add ("accurate":2);
add ("worthy":1);
add ("worthwhile":1);
add ("worried":1);
add ("working-class":1);
add ("wise":1);
add ("well-known":1);
add ("welcome":1);
add ("weekly":1);
add ("wealthy":1);
add ("waste":1);
add ("waiting":1);
add ("vocational":1);
add ("vertical":1);
add ("variable":1);
add ("vague":1);
add ("useless":1);
add ("unpleasant":1);
add ("unnecessary":1);
add ("unlike":1);
add ("unhappy":1);
add ("unfair":1);
add ("uncomfortable":1);
add ("unchanged":1);
add ("uncertain":1);
add ("unaware":1);
add ("unacceptable":1);
add ("ugly":1);
add ("twin":1);
add ("turkish":1);
add ("tropical":1);
add ("tremendous":1);
add ("tragic":1);
add ("toxic":1);
add ("tight":1);
add ("thorough":1);
add ("thinking":1);
add ("territorial":1);
add ("tender":1);
add ("teenage":1);
add ("technological":1);
add ("systematic":1);
add ("sympathetic":1);
add ("symbolic":1);
add ("swiss":1);
add ("suspicious":1);
add ("supposed":1);
add ("super":1);
add ("superior":1);
add ("superb":1);
add ("successive":1);
add ("subtle":1);
add ("subjective":1);
add ("structural":1);
add ("strict":1);
add ("straight":1);
add ("straightforward":1);
add ("still":1);
add ("steep":1);
add ("steady":1);
add ("static":1);
add ("star":1);
add ("stable":1);
add ("square":1);
add ("splendid":1);
add ("spectacular":1);
add ("specified":1);
add ("spatial":1);
add ("spare":1);
add ("solar":1);
add ("socialist":1);
add ("smart":1);
add ("slim":1);
add ("skilled":1);
add ("sick":1);
add ("shy":1);
add ("short-term":1);
add ("sheer":1);
add ("shared":1);
add ("shallow":1);
add ("semantic":1);
add ("select":1);
add ("selective":1);
add ("secure":1);
add ("satisfied":1);
add ("sacred":1);
add ("ruling":1);
add ("routine":1);
add ("romantic":1);
add ("rigid":1);
add ("ridiculous":1);
add ("retail":1);
add ("resulting":1);
add ("respective":1);
add ("respectable":1);
add ("required":1);
add ("representative":1);
add ("reluctant":1);
add ("regulatory":1);
add ("redundant":1);
add ("reduced":1);
add ("realistic":1);
add ("random":1);
add ("racial":1);
add ("psychiatric":1);
add ("provincial":1);
add ("protective":1);
add ("prospective":1);
add ("prominent":1);
add ("progressive":1);
add ("profound":1);
add ("profitable":1);
add ("productive":1);
add ("probable":1);
add ("primitive":1);
add ("prepared":1);
add ("premier":1);
add ("preliminary":1);
add ("precious":1);
add ("post-war":1);
add ("polite":1);
add ("polish":1);
add ("pleased":1);
add ("planned":1);
add ("plain":1);
add ("philosophical":1);
add ("persistent":1);
add ("peculiar":1);
add ("peaceful":1);
add ("payable":1);
add ("passive":1);
add ("partial":1);
add ("part-time":1);
add ("parental":1);
add ("palestinian":1);
add ("painful":1);
add ("outdoor":1);
add ("orthodox":1);
add ("organisational":1);
add ("orange":1);
add ("oral":1);
add ("optimistic":1);
add ("operational":1);
add ("opening":1);
add ("olympic":1);
add ("old-fashioned":1);
add ("occupational":1);
add ("objective":1);
add ("novel":1);
add ("notable":1);
add ("noble":1);
add ("neutral":1);
add ("neighbouring":1);
add ("neat":1);
add ("nearby":1);
add ("naval":1);
add ("native":1);
add ("nasty":1);
add ("naked":1);
add ("mysterious":1);
add ("monthly":1);
add ("molecular":1);
add ("model":1);
add ("mixed":1);
add ("misleading":1);
add ("miserable":1);
add ("minimum":1);
add ("minimal":1);
add ("mild":1);
add ("middle-class":1);
add ("metropolitan":1);
add ("medium":1);
add ("mediterranean":1);
add ("medieval":1);
add ("mechanical":1);
add ("mature":1);
add ("mathematical":1);
add ("marvellous":1);
add ("marked":1);
add ("marine":1);
add ("manufacturing":1);
add ("managerial":1);
add ("magnificent":1);
add ("magnetic":1);
add ("magic":1);
add ("ltd.":1);
add ("loyal":1);
add ("lost":1);
add ("loose":1);
add ("lonely":1);
add ("lively":1);
add ("linear":1);
add ("lexical":1);
add ("lesser":1);
add ("lengthy":1);
add ("legitimate":1);
add ("legislative":1);
add ("large-scale":1);
add ("lacking":1);
add ("kind":1);
add ("islamic":1);
add ("irrelevant":1);
add ("invisible":1);
add ("intimate":1);
add ("intermediate":1);
add ("interior":1);
add ("intensive":1);
add ("intelligent":1);
add ("intellectual":1);
add ("integrated":1);
add ("intact":1);
add ("insufficient":1);
add ("institutional":1);
add ("innocent":1);
add ("inland":1);
add ("inherent":1);
add ("influential":1);
add ("indirect":1);
add ("incredible":1);
add ("inappropriate":1);
add ("improved":1);
add ("implicit":1);
add ("imperial":1);
add ("immense":1);
add ("imaginative":1);
add ("ideological":1);
add ("hungry":1);
add ("hostile":1);
add ("horrible":1);
add ("horizontal":1);
add ("hidden":1);
add ("harsh":1);
add ("handsome":1);
add ("gross":1);
add ("grim":1);
add ("greek":1);
add ("gradual":1);
add ("gothic":1);
add ("glorious":1);
add ("giant":1);
add ("geographical":1);
add ("genetic":1);
add ("gay":1);
add ("furious":1);
add ("functional":1);
add ("full-time":1);
add ("fortunate":1);
add ("forthcoming":1);
add ("formidable":1);
add ("fond":1);
add ("fit":1);
add ("fiscal":1);
add ("fierce":1);
add ("fellow":1);
add ("favorable":1);
add ("fatal":1);
add ("fashionable":1);
add ("fascinating":1);
add ("faint":1);
add ("extended":1);
add ("explicit":1);
add ("expert":1);
add ("experienced":1);
add ("exotic":1);
add ("executive":1);
add ("exclusive":1);
add ("excessive":1);
add ("exceptional":1);
add ("evolutionary":1);
add ("evil":1);
add ("eventual":1);
add ("ethical":1);
add ("estimated":1);
add ("established":1);
add ("equivalent":1);
add ("enthusiastic":1);
add ("endless":1);
add ("encouraging":1);
add ("empirical":1);
add ("eligible":1);
add ("elegant":1);
add ("elected":1);
add ("elaborate":1);
add ("eager":1);
add ("dynamic":1);
add ("dutch":1);
add ("dull":1);
add ("dual":1);
add ("dreadful":1);
add ("doubtful":1);
add ("divine":1);
add ("diverse":1);
add ("distinguished":1);
add ("disciplinary":1);
add ("disastrous":1);
add ("diplomatic":1);
add ("digital":1);
add ("developing":1);
add ("demanding":1);
add ("delightful":1);
add ("delighted":1);
add ("delicious":1);
add ("delicate":1);
add ("deliberate":1);
add ("definite":1);
add ("defensive":1);
add ("decisive":1);
add ("decent":1);
add ("darling":1);
add ("damp":1);
add ("cruel":1);
add ("crude":1);
add ("crazy":1);
add ("costly":1);
add ("corresponding":1);
add ("cool":1);
add ("convincing":1);
add ("convenient":1);
add ("contrary":1);
add ("continuing":1);
add ("continued":1);
add ("continental":1);
add ("content":1);
add ("confidential":1);
add ("concrete":1);
add ("compulsory":1);
add ("complicated":1);
add ("competent":1);
add ("compatible":1);
add ("comparative":1);
add ("comparable":1);
add ("communist":1);
add ("combined":1);
add ("colourful":1);
add ("coloured":1);
add ("colonial":1);
add ("collective":1);
add ("coherent":1);
add ("cognitive":1);
add ("coastal":1);
add ("closed":1);
add ("clever":1);
add ("classic":1);
add ("chronic":1);
add ("chosen":1);
add ("cheerful":1);
add ("charming":1);
add ("changing":1);
add ("cautious":1);
add ("causal":1);
add ("casual":1);
add ("capitalist":1);
add ("canadian":1);
add ("burning":1);
add ("brave":1);
add ("bottom":1);
add ("boring":1);
add ("bold":1);
add ("blank":1);
add ("bizarre":1);
add ("biological":1);
add ("beneficial":1);
add ("base":1);
add ("awkward":1);
add ("autonomous":1);
add ("atomic":1);
add ("atlantic":1);
add ("associated":1);
add ("asian":1);
add ("ashamed":1);
add ("artistic":1);
add ("artificial":1);
add ("arbitrary":1);
add ("arab":1);
add ("appointed":1);
add ("applicable":1);
add ("anonymous":1);
add ("ambitious":1);
add ("amazing":1);
add ("alleged":1);
add ("aggressive":1);
add ("advisory":1);
add ("adverse":1);
add ("adjacent":1);
add ("added":1);
add ("accused":1);
add ("accessible":1);
add ("abstract":1);
add ("absent":1);
add ("above":1);

-- 
-- adverbs
-- from the common word list
create adverbs;
set types = (varchar);
set weights = 1;
add ("then":619);
add ("more":615);
add ("also":592);
add ("so":540);
add ("now":538);
add ("only":524);
add ("as":436);
add ("very":431);
add ("just":426);
add ("even":329);
add ("still":318);
add ("too":316);
add ("however":280);
add ("there":269);
add ("most":263);
add ("here":263);
add ("much":257);
add ("never":241);
add ("again":236);
add ("well":216);
add ("always":199);
add ("often":178);
add ("about":171);
add ("already":156);
add ("yet":151);
add ("perhaps":151);
add ("almost":151);
add ("quite":150);
add ("really":149);
add ("later":141);
add ("all":133);
add ("both":129);
add ("of course":126);
add ("at least":119);
add ("less":117);
add ("ever":114);
add ("enough":114);
add ("for example":113);
add ("together":111);
add ("therefore":107);
add ("over":106);
add ("probably":105);
add ("today":102);
add ("thus":101);
add ("particularly":101);
add ("far":100);
add ("rather":95);
add ("further":94);
add ("sometimes":91);
add ("yesterday":87);
add ("usually":87);
add ("indeed":85);
add ("ago":85);
add ("simply":83);
add ("especially":83);
add ("else":79);
add ("home":78);
add ("certainly":77);
add ("once":75);
add ("soon":74);
add ("clearly":70);
add ("either":68);
add ("long":66);
add ("away":66);
add ("better":65);
add ("actually":65);
add ("finally":62);
add ("at all":62);
add ("in order":61);
add ("recently":57);
add ("quickly":57);
add ("suddenly":56);
add ("generally":54);
add ("nearly":50);
add ("above":50);
add ("more than":49);
add ("forward":49);
add ("right":47);
add ("please":46);
add ("easily":46);
add ("immediately":45);
add ("highly":44);
add ("earlier":44);
add ("early":43);
add ("no longer":42);
add ("fully":42);
add ("exactly":42);
add ("eventually":42);
add ("directly":42);
add ("hardly":41);
add ("best":41);
add ("below":41);
add ("around":41);
add ("slightly":40);
add ("alone":40);
add ("otherwise":39);
add ("obviously":39);
add ("a little":39);
add ("slowly":38);
add ("completely":38);
add ("relatively":37);
add ("merely":36);
add ("maybe":36);
add ("high":36);
add ("normally":35);
add ("late":35);
add ("largely":35);
add ("instead":35);
add ("sure":34);
add ("nevertheless":34);
add ("little":34);
add ("carefully":34);
add ("apparently":34);
add ("anyway":34);
add ("tomorrow":33);
add ("previously":33);
add ("mainly":33);
add ("for instance":33);
add ("currently":33);
add ("increasingly":32);
add ("in particular":32);
add ("entirely":32);
add ("as well":32);
add ("possibly":31);
add ("extremely":31);
add ("equally":31);
add ("hard":29);
add ("surely":28);
add ("frequently":28);
add ("ahead":28);
add ("widely":27);
add ("twice":27);
add ("partly":27);
add ("fairly":27);
add ("somewhere":26);
add ("seriously":26);
add ("elsewhere":26);
add ("closely":26);
add ("straight":25);
add ("no doubt":25);
add ("neither":25);
add ("necessarily":25);
add ("before":25);
add ("all right":25);
add ("totally":24);
add ("tonight":24);
add ("properly":24);
add ("etc":24);
add ("meanwhile":23);
add ("hence":23);
add ("effectively":23);
add ("somewhat":22);
add ("similarly":22);
add ("rapidly":22);
add ("at first":22);
add ("under":21);
add ("strongly":21);
add ("moreover":21);
add ("in addition":21);
add ("at last":21);
add ("virtually":20);
add ("unfortunately":20);
add ("that is":20);
add ("somehow":20);
add ("significantly":20);
add ("rarely":20);
add ("perfectly":20);
add ("originally":20);
add ("low":20);
add ("inside":20);
add ("badly":20);
add ("afterwards":20);
add ("quietly":19);
add ("naturally":19);
add ("in general":19);
add ("heavily":19);
add ("gently":19);
add ("firmly":19);
add ("at once":19);
add ("and so on":19);
add ("absolutely":19);
add ("shortly":18);
add ("regularly":18);
add ("occasionally":18);
add ("mostly":18);
add ("initially":18);
add ("deeply":18);
add ("close":18);
add ("abroad":18);
add ("subsequently":17);
add ("specifically":17);
add ("short":17);
add ("gradually":17);
add ("aside":17);
add ("successfully":16);
add ("precisely":16);
add ("outside":16);
add ("once again":16);
add ("greatly":16);
add ("essentially":16);
add ("easy":16);
add ("apart":16);
add ("anywhere":16);
add ("respectively":15);
add ("pretty":15);
add ("open":15);
add ("truly":14);
add ("that":14);
add ("primarily":14);
add ("inevitably":14);
add ("furthermore":14);
add ("fast":14);
add ("constantly":14);
add ("briefly":14);
add ("at present":14);
add ("altogether":14);
add ("across":14);
add ("a bit":14);
add ("ultimately":13);
add ("reasonably":13);
add ("readily":13);
add ("presumably":13);
add ("newly":13);
add ("less than":13);
add ("everywhere":13);
add ("deliberately":13);
add ("considerably":13);
add ("automatically":13);
add ("approximately":13);
add ("thereby":12);
add ("surprisingly":12);
add ("sufficiently":12);
add ("softly":12);
add ("sharply":12);
add ("secondly":12);
add ("deep":12);
add ("commonly":12);
add ("behind":12);
add ("undoubtedly":11);
add ("purely":11);
add ("potentially":11);
add ("personally":11);
add ("once more":11);
add ("notably":11);
add ("near":11);
add ("ill":11);
add ("consequently":11);
add ("closer":11);
add ("barely":11);
add ("accordingly":11);
add ("wide":10);
add ("wholly":10);
add ("up to":10);
add ("typically":10);
add ("since":10);
add ("roughly":10);
add ("nowhere":10);
add ("namely":10);
add ("longer":10);
add ("good":10);
add ("easier":10);
add ("definitely":10);
add ("by now":10);
add ("traditionally":9);
add ("though":9);
add ("thoroughly":9);
add ("strictly":9);
add ("specially":9);
add ("sadly":9);
add ("physically":9);
add ("mentally":9);
add ("lightly":9);
add ("formally":9);
add ("desperately":9);
add ("substantially":8);
add ("steadily":8);
add ("sooner":8);
add ("solely":8);
add ("so as":8);
add ("simultaneously":8);
add ("severely":8);
add ("separately":8);
add ("scarcely":8);
add ("safely":8);
add ("politically":8);
add ("officially":8);
add ("locally":8);
add ("literally":8);
add ("in part":8);
add ("happily":8);
add ("from time to time":8);
add ("formerly":8);
add ("forever":8);
add ("fair":8);
add ("exclusively":8);
add ("direct":8);
add ("correctly":8);
add ("besides":8);
add ("basically":8);
add ("alternatively":8);
add ("upwards":7);
add ("upstairs":7);
add ("thereafter":7);
add ("socially":7);
add ("seldom":7);
add ("reportedly":7);
add ("remarkably":7);
add ("regardless":7);
add ("publicly":7);
add ("past":7);
add ("invariably":7);
add ("instantly":7);
add ("in short":7);
add ("hitherto":7);
add ("freely":7);
add ("fortunately":7);
add ("firstly":7);
add ("evidently":7);
add ("dramatically":7);
add ("downstairs":7);
add ("consistently":7);
add ("by no means":7);
add ("broadly":7);
add ("backwards":7);
add ("all but":7);
add ("actively":7);
add ("utterly":6);
add ("tight":6);
add ("tightly":6);
add ("thick":6);
add ("temporarily":6);
add ("swiftly":6);
add ("seemingly":6);
add ("repeatedly":6);
add ("predominantly":6);
add ("practically":6);
add ("positively":6);
add ("partially":6);
add ("openly":6);
add ("onwards":6);
add ("no":6);
add ("nowadays":6);
add ("nonetheless":6);
add ("independently":6);
add ("importantly":6);
add ("hopefully":6);
add ("genuinely":6);
add ("forwards":6);
add ("forth":6);
add ("for long":6);
add ("for ever":6);
add ("far from":6);
add ("explicitly":6);
add ("even so":6);
add ("differently":6);
add ("continually":6);
add ("as yet":6);
add ("as usual":6);
add ("accurately":6);
add ("worldwide":5);
add ("within":5);
add ("terribly":5);
add ("sincerely":5);
add ("silently":5);
add ("rightly":5);
add ("privately":5);
add ("permanently":5);
add ("overall":5);
add ("neatly":5);
add ("nearby":5);
add ("loudly":5);
add ("likewise":5);
add ("legally":5);
add ("just about":5);
add ("individually":5);
add ("indirectly":5);
add ("in full":5);
add ("in common":5);
add ("ideally":5);
add ("half":5);
add ("faster":5);
add ("efficiently":5);
add ("daily":5);
add ("comparatively":5);
add ("clear":5);
add ("bitterly":5);
add ("beyond":5);
add ("beautifully":5);
add ("annually":5);
add ("angrily":5);
add ("allegedly":5);
add ("alike":5);
add ("adequately":5);
add ("abruptly":5);
add ("wrong":4);
add ("worse":4);
add ("wild":4);
add ("vaguely":4);
add ("urgently":4);
add ("unusually":4);
add ("unexpectedly":4);
add ("under way":4);
add ("technically":4);
add ("supposedly":4);
add ("strangely":4);
add ("smoothly":4);
add ("slow":4);
add ("sideways":4);
add ("sharp":4);
add ("sexually":4);
add ("reluctantly":4);
add ("quick":4);
add ("public":4);
add ("promptly":4);
add ("principally":4);
add ("presently":4);
add ("poorly":4);
add ("plain":4);
add ("per annum":4);
add ("overseas":4);
add ("overnight":4);
add ("on board":4);
add ("jointly":4);
add ("ironically":4);
add ("incidentally":4);
add ("in public":4);
add ("honestly":4);
add ("hastily":4);
add ("harder":4);
add ("fundamentally":4);
add ("fiercely":4);
add ("false":4);
add ("extra":4);
add ("extensively":4);
add ("exceptionally":4);
add ("economically":4);
add ("duly":4);
add ("doubtless":4);
add ("distinctly":4);
add ("curiously":4);
add ("cool":4);
add ("continuously":4);
add ("comfortably":4);
add ("cheap":4);
add ("by far":4);
add ("but":4);
add ("as good as":4);
add ("as a whole":4);
add ("appropriately":4);
add ("aloud":4);
add ("all the same":4);
add ("wrongly":3);
add ("wildly":3);
add ("violently":3);
add ("vigorously":3);
add ("vice versa":3);
add ("upright":3);
add ("underneath":3);
add ("true":3);
add ("thoughtfully":3);
add ("this":3);
add ("thirdly":3);
add ("that is to say":3);
add ("systematically":3);
add ("suitably":3);
add ("sort of":3);
add ("secretly":3);
add ("radically":3);
add ("proudly":3);
add ("progressively":3);
add ("profoundly":3);
add ("preferably":3);
add ("politely":3);
add ("plainly":3);
add ("painfully":3);
add ("over there":3);
add ("oddly":3);
add ("none the less":3);
add ("nicely":3);
add ("nervously":3);
add ("nearer":3);
add ("nationally":3);
add ("narrowly":3);
add ("mutually":3);
add ("mildly":3);
add ("markedly":3);
add ("loud":3);
add ("loose":3);
add ("loosely":3);
add ("like":3);
add ("least":3);
add ("lately":3);
add ("kindly":3);
add ("internationally":3);
add ("interestingly":3);
add ("instinctively":3);
add ("indoors":3);
add ("incredibly":3);
add ("immensely":3);
add ("historically":3);
add ("highest":3);
add ("halfway":3);
add ("gmt":3);
add ("furiously":3);
add ("freshly":3);
add ("frankly":3);
add ("for the time being":3);
add ("for the most part":3);
add ("for once":3);
add ("for good":3);
add ("for certain":3);
add ("flat":3);
add ("finely":3);
add ("financially":3);
add ("faintly":3);
add ("evenly":3);
add ("enormously":3);
add ("emotionally":3);
add ("eagerly":3);
add ("due":3);
add ("downwards":3);
add ("deeper":3);
add ("dead":3);
add ("conversely":3);
add ("conveniently":3);
add ("consciously":3);
add ("commercially":3);
add ("clean":3);
add ("chiefly":3);
add ("centrally":3);
add ("cautiously":3);
add ("casually":3);
add ("calmly":3);
add ("bloody":3);
add ("big":3);
add ("bad":3);
add ("at large":3);
add ("at best":3);
add ("ashore":3);
add ("arguably":3);
add ("any longer":3);
add ("anxiously":3);
add ("admittedly":3);
add ("wonderfully":2);
add ("willingly":2);
add ("wearily":2);
add ("weakly":2);
add ("warmly":2);
add ("voluntarily":2);
add ("vividly":2);
add ("visually":2);
add ("vastly":2);
add ("usefully":2);
add ("unnecessarily":2);
add ("universally":2);
add ("uniquely":2);
add ("unduly":2);
add ("underway":2);
add ("understandably":2);
add ("underground":2);
add ("uncomfortably":2);
add ("unanimously":2);
add ("throughout":2);
add ("thinly":2);
add ("thereof":2);
add ("theoretically":2);
add ("thankfully":2);
add ("stiff":2);
add ("stiffly":2);
add ("steady":2);
add ("statistically":2);
add ("spontaneously":2);
add ("sometime":2);
add ("so much as":2);
add ("small":2);
add ("sensibly":2);
add ("satisfactorily":2);
add ("richly":2);
add ("professionally":2);
add ("powerfully":2);
add ("pleasantly":2);
add ("patiently":2);
add ("parallel":2);
add ("paradoxically":2);
add ("overwhelmingly":2);
add ("overhead":2);
add ("over here":2);
add ("outwards":2);
add ("ostensibly":2);
add ("notoriously":2);
add ("morally":2);
add ("momentarily":2);
add ("moderately":2);
add ("marginally":2);
add ("luckily":2);
add ("logically":2);
add ("live":2);
add ("likely":2);
add ("internally":2);
add ("intently":2);
add ("intensely":2);
add ("inherently":2);
add ("infinitely":2);
add ("indefinitely":2);
add ("in vain":2);
add ("in private":2);
add ("in between":2);
add ("implicitly":2);
add ("impatiently":2);
add ("illegally":2);
add ("hurriedly":2);
add ("hopelessly":2);
add ("higher":2);
add ("helplessly":2);
add ("grimly":2);
add ("great":2);
add ("generously":2);
add ("from now on":2);
add ("frantically":2);
add ("favourably":2);
add ("faithfully":2);
add ("extraordinarily":2);
add ("expressly":2);
add ("environmentally":2);
add ("enthusiastically":2);
add ("en route":2);
add ("drily":2);
add ("decidedly":2);
add ("dangerously":2);
add ("critically":2);
add ("confidently":2);
add ("collectively":2);
add ("coldly":2);
add ("cheerfully":2);
add ("cheaply":2);
add ("characteristically":2);
add ("briskly":2);
add ("brilliantly":2);
add ("brightly":2);
add ("beforehand":2);
add ("bc":2);
add ("at random":2);
add ("at length":2);
add ("as it were":2);
add ("anything but":2);
add ("alongside":2);
add ("additionally":2);
add ("acutely":2);
add ("accidentally":2);
add ("wryly":1);
add ("worst":1);
add ("without":1);
add ("wistfully":1);
add ("wisely":1);
add ("weekly":1);
add ("warily":1);
add ("vitally":1);
add ("visibly":1);
add ("vertically":1);
add ("vehemently":1);
add ("variously":1);
add ("upward":1);
add ("upstream":1);
add ("upside down":1);
add ("up front":1);
add ("unwittingly":1);
add ("unsuccessfully":1);
add ("unreasonably":1);
add ("unquestionably":1);
add ("uniformly":1);
add ("unhappily":1);
add ("unfairly":1);
add ("unequivocally":1);
add ("uneasily":1);
add ("unconsciously":1);
add ("uncertainly":1);
add ("triumphantly":1);
add ("tremendously":1);
add ("tragically":1);
add ("to and fro":1);
add ("time and again":1);
add ("thickly":1);
add ("therein":1);
add ("thence":1);
add ("tentatively":1);
add ("tenderly":1);
add ("sympathetically":1);
add ("sweetly":1);
add ("suspiciously":1);
add ("superficially":1);
add ("superbly":1);
add ("subtly":1);
add ("stubbornly":1);
add ("strikingly":1);
add ("strategically":1);
add ("sternly":1);
add ("steeply":1);
add ("squarely":1);
add ("speedily":1);
add ("solidly":1);
add ("solemnly":1);
add ("smartly":1);
add ("slower":1);
add ("skilfully":1);
add ("selectively":1);
add ("securely":1);
add ("scientifically":1);
add ("scarce":1);
add ("savagely":1);
add ("safer":1);
add ("ruthlessly":1);
add ("ruefully":1);
add ("routinely":1);
add ("rigorously":1);
add ("rigidly":1);
add ("resolutely":1);
add ("remotely":1);
add ("reliably":1);
add ("relentlessly":1);
add ("regrettably":1);
add ("real":1);
add ("realistically":1);
add ("ready":1);
add ("rationally":1);
add ("randomly":1);
add ("quicker":1);
add ("psychologically":1);
add ("prominently":1);
add ("prior":1);
add ("prematurely":1);
add ("predictably":1);
add ("popularly":1);
add ("pointedly":1);
add ("persistently":1);
add ("periodically":1);
add ("per se":1);
add ("per capita":1);
add ("peculiarly":1);
add ("peacefully":1);
add ("patently":1);
add ("passionately":1);
add ("part-time":1);
add ("overtly":1);
add ("outright":1);
add ("ordinarily":1);
add ("once and for all":1);
add ("ok":1);
add ("okay":1);
add ("offshore":1);
add ("objectively":1);
add ("not":1);
add ("noticeably":1);
add ("northwards":1);
add ("nominally":1);
add ("noisily":1);
add ("negatively":1);
add ("nationwide":1);
add ("mysteriously":1);
add ("monthly":1);
add ("modestly":1);
add ("mistakenly":1);
add ("miserably":1);
add ("miraculously":1);
add ("mercifully":1);
add ("mechanically":1);
add ("materially":1);
add ("massively":1);
add ("manually":1);
add ("lower":1);
add ("lovingly":1);
add ("legitimately":1);
add ("left":1);
add ("lazily":1);
add ("latterly":1);
add ("lastly":1);
add ("knowingly":1);
add ("kind of":1);
add ("keenly":1);
add ("justly":1);
add ("justifiably":1);
add ("jolly":1);
add ("irritably":1);
add ("inwards":1);
add ("inwardly":1);
add ("involuntarily":1);
add ("intrinsically":1);
add ("intimately":1);
add ("intermittently":1);
add ("inter alia":1);
add ("intentionally":1);
add ("intellectually":1);
add ("innocently":1);
add ("inland":1);
add ("infrequently":1);
add ("informally":1);
add ("inextricably":1);
add ("inexorably":1);
add ("indignantly":1);
add ("incorrectly":1);
add ("inadvertently":1);
add ("in vitro":1);
add ("in the main":1);
add ("in situ":1);
add ("in brief":1);
add ("idly":1);
add ("ibid":1);
add ("ibid.":1);
add ("huskily":1);
add ("hugely":1);
add ("hotly":1);
add ("horribly":1);
add ("horizontally":1);
add ("hesitantly":1);
add ("hereby":1);
add ("henceforth":1);
add ("harshly":1);
add ("half-way":1);
add ("half way":1);
add ("habitually":1);
add ("grudgingly":1);
add ("grossly":1);
add ("gravely":1);
add ("gratefully":1);
add ("graphically":1);
add ("gracefully":1);
add ("gloomily":1);
add ("gladly":1);
add ("gingerly":1);
add ("geographically":1);
add ("genetically":1);
add ("full":1);
add ("full-time":1);
add ("forthwith":1);
add ("forcibly":1);
add ("forcefully":1);
add ("for sure":1);
add ("flatly":1);
add ("fine":1);
add ("farther":1);
add ("falsely":1);
add ("externally":1);
add ("expertly":1);
add ("experimentally":1);
add ("excitedly":1);
add ("excessively":1);
add ("exceedingly":1);
add ("ever so":1);
add ("endlessly":1);
add ("emphatically":1);
add ("eminently":1);
add ("elegantly":1);
add ("electronically":1);
add ("effortlessly":1);
add ("eastwards":1);
add ("earnestly":1);
add ("drastically":1);
add ("downstream":1);
add ("downhill":1);
add ("doubtfully":1);
add ("doubly":1);
add ("double":1);
add ("disproportionately":1);
add ("discreetly":1);
add ("dimly":1);
add ("densely":1);
add ("delicately":1);
add ("defiantly":1);
add ("defensively":1);
add ("decisively":1);
add ("dearly":1);
add ("darkly":1);
add ("culturally":1);
add ("cruelly":1);
add ("crudely":1);
add ("crucially":1);
add ("crossly":1);
add ("correspondingly":1);
add ("coolly":1);
add ("convincingly":1);
add ("conventionally":1);
add ("conspicuously":1);
add ("conceivably":1);
add ("closest":1);
add ("clinically":1);
add ("cleverly":1);
add ("circa":1);
add ("chemically":1);
add ("carelessly":1);
add ("by and large":1);
add ("busily":1);
add ("brutally":1);
add ("bravely":1);
add ("boldly":1);
add ("bluntly":1);
add ("blindly":1);
add ("blankly":1);
add ("beneath":1);
add ("belatedly":1);
add ("awkwardly":1);
add ("awfully":1);
add ("at worst":1);
add ("at most":1);
add ("astonishingly":1);
add ("as a matter of fact":1);
add ("artificially":1);
add ("arbitrarily":1);
add ("approx":1);
add ("apologetically":1);
add ("anymore":1);
add ("anyhow":1);
add ("and so forth":1);
add ("amazingly":1);
add ("alternately":1);
add ("aloft":1);
add ("all of a sudden":1);
add ("alarmingly":1);
add ("aggressively":1);
add ("afield":1);
add ("affectionately":1);
add ("adversely":1);
add ("admirably":1);
add ("absently":1);
add ("aboard":1);
add ("aback":1);
add ("a.d.":1);
add ("a lot":1);

--
-- articles
-- from the common word list
create articles;
set types = (varchar);
set weights = 1;
add ("the":163);
add ("a":504);
add ("an":9);
add ("no":3);
add ("every":1);

--
-- nouns 
-- from the common word list
create nouns;
set types = (varchar);
set weights = 1;
add ("years":91);
add ("children":47);
add ("women":41);
add ("men":39);
add ("things":34);
add ("eyes":32);
add ("days":32);
add ("members":30);
add ("others":29);
add ("police":28);
add ("problems":27);
add ("times":25);
add ("services":25);
add ("months":25);
add ("words":24);
add ("areas":24);
add ("groups":20);
add ("hands":19);
add ("cases":19);
add ("systems":18);
add ("patients":18);
add ("hours":18);
add ("companies":18);
add ("terms":17);
add ("minutes":17);
add ("countries":17);
add ("changes":17);
add ("parents":16);
add ("conditions":16);
add ("workers":15);
add ("ways":15);
add ("students":15);
add ("schools":15);
add ("friends":15);
add ("weeks":14);
add ("studies":14);
add ("feet":14);
add ("rights":13);
add ("questions":13);
add ("parties":13);
add ("books":13);
add ("authorities":13);
add ("results":12);
add ("relations":12);
add ("rates":12);
add ("parts":12);
add ("levels":12);
add ("details":12);
add ("activities":12);
add ("teachers":11);
add ("reasons":11);
add ("products":11);
add ("points":11);
add ("numbers":11);
add ("issues":11);
add ("ideas":11);
add ("forces":11);
add ("events":11);
add ("effects":11);
add ("arms":11);
add ("standards":10);
add ("sales":10);
add ("rules":10);
add ("resources":10);
add ("prices":10);
add ("lines":10);
add ("interests":10);
add ("goods":10);
add ("figures":10);
add ("costs":10);
add ("circumstances":10);
add ("windows":9);
add ("types":9);
add ("policies":9);
add ("plans":9);
add ("needs":9);
add ("miles":9);
add ("methods":9);
add ("jobs":9);
add ("girls":9);
add ("forms":9);
add ("factors":9);
add ("animals":9);
add ("trees":8);
add ("subjects":8);
add ("shares":8);
add ("pupils":8);
add ("players":8);
add ("places":8);
add ("officers":8);
add ("matters":8);
add ("letters":8);
add ("individuals":8);
add ("houses":8);
add ("firms":8);
add ("features":8);
add ("families":8);
add ("differences":8);
add ("courses":8);
add ("cells":8);
add ("boys":8);
add ("views":7);
add ("values":7);
add ("users":7);
add ("units":7);
add ("sources":7);
add ("skills":7);
add ("proposals":7);
add ("plants":7);
add ("names":7);
add ("ministers":7);
add ("materials":7);
add ("lives":7);
add ("leaders":7);
add ("items":7);
add ("institutions":7);
add ("facilities":7);
add ("examples":7);
add ("difficulties":7);
add ("decisions":7);
add ("customers":7);
add ("clothes":7);
add ("cars":7);
add ("bodies":7);
add ("benefits":7);
add ("banks":7);
add ("aspects":7);
add ("affairs":7);
add ("walls":6);
add ("techniques":6);
add ("steps":6);
add ("states":6);
add ("sides":6);
add ("requirements":6);
add ("reports":6);
add ("relationships":6);
add ("records":6);
add ("purposes":6);
add ("programmes":6);
add ("principles":6);
add ("pp.":6);
add ("powers":6);
add ("pounds":6);
add ("patterns":6);
add ("papers":6);
add ("opportunities":6);
add ("operations":6);
add ("officials":6);
add ("measures":6);
add ("managers":6);
add ("legs":6);
add ("homes":6);
add ("grounds":6);
add ("games":6);
add ("funds":6);
add ("fingers":6);
add ("employees":6);
add ("elements":6);
add ("elections":6);
add ("efforts":6);
add ("courts":6);
add ("classes":6);
add ("buildings":6);
add ("arrangements":6);
add ("applications":6);
add ("visitors":5);
add ("troops":5);
add ("thousands":5);
add ("streets":5);
add ("sites":5);
add ("rooms":5);
add ("readers":5);
add ("projects":5);
add ("profits":5);
add ("processes":5);
add ("procedures":5);
add ("pieces":5);
add ("pictures":5);
add ("organisations":5);
add ("orders":5);
add ("notes":5);
add ("models":5);
add ("meetings":5);
add ("lips":5);
add ("laws":5);
add ("heads":5);
add ("governments":5);
add ("functions":5);
add ("flowers":5);
add ("fields":5);
add ("feelings":5);
add ("facts":5);
add ("developments":5);
add ("demands":5);
add ("colleagues":5);
add ("clients":5);
add ("charges":5);
add ("centres":5);
add ("birds":5);
add ("attitudes":5);
add ("arts":5);
add ("actions":5);
add ("accounts":5);
add ("weapons":4);
add ("unions":4);
add ("towns":4);
add ("thoughts":4);
add ("theories":4);
add ("thanks":4);
add ("tests":4);
add ("teeth":4);
add ("tears":4);
add ("teams":4);
add ("talks":4);
add ("structures":4);
add ("stories":4);
add ("statements":4);
add ("stars":4);
add ("stages":4);
add ("sports":4);
add ("societies":4);
add ("situations":4);
add ("signs":4);
add ("shoulders":4);
add ("shops":4);
add ("sections":4);
add ("seconds":4);
add ("seats":4);
add ("schemes":4);
add ("regulations":4);
add ("regions":4);
add ("provisions":4);
add ("properties":4);
add ("proceedings":4);
add ("practices":4);
add ("positions":4);
add ("persons":4);
add ("periods":4);
add ("payments":4);
add ("pages":4);
add ("owners":4);
add ("offices":4);
add ("objects":4);
add ("objectives":4);
add ("nations":4);
add ("movements":4);
add ("markets":4);
add ("machines":4);
add ("losses":4);
add ("lights":4);
add ("kinds":4);
add ("industries":4);
add ("implications":4);
add ("hundreds":4);
add ("horses":4);
add ("goals":4);
add ("farmers":4);
add ("employers":4);
add ("duties":4);
add ("drugs":4);
add ("doors":4);
add ("dogs":4);
add ("documents":4);
add ("doctors":4);
add ("directors":4);
add ("departments":4);
add ("contracts":4);
add ("consequences":4);
add ("communities":4);
add ("colours":4);
add ("claims":4);
add ("cities":4);
add ("characters":4);
add ("characteristics":4);
add ("artists":4);
add ("arguments":4);
add ("yards":3);
add ("writers":3);
add ("wages":3);
add ("vehicles":3);
add ("tools":3);
add ("tasks":3);
add ("symptoms":3);
add ("supporters":3);
add ("stones":3);
add ("stations":3);
add ("stairs":3);
add ("sons":3);
add ("soldiers":3);
add ("shows":3);
add ("shoes":3);
add ("shareholders":3);
add ("servants":3);
add ("sentences":3);
add ("scientists":3);
add ("roads":3);
add ("residents":3);
add ("representatives":3);
add ("prisoners":3);
add ("premises":3);
add ("politicians":3);
add ("photographs":3);
add ("personnel":3);
add ("partners":3);
add ("paintings":3);
add ("organizations":3);
add ("options":3);
add ("occasions":3);
add ("newspapers":3);
add ("neighbours":3);
add ("negotiations":3);
add ("mothers":3);
add ("moments":3);
add ("metres":3);
add ("leaves":3);
add ("languages":3);
add ("kids":3);
add ("instructions":3);
add ("images":3);
add ("guests":3);
add ("findings":3);
add ("films":3);
add ("faces":3);
add ("experts":3);
add ("experiments":3);
add ("experiences":3);
add ("expectations":3);
add ("eggs":3);
add ("ears":3);
add ("earnings":3);
add ("discussions":3);
add ("degrees":3);
add ("criteria":3);
add ("councils":3);
add ("copies":3);
add ("contents":3);
add ("computers":3);
add ("components":3);
add ("communications":3);
add ("comments":3);
add ("clubs":3);
add ("citizens":3);
add ("churches":3);
add ("centuries":3);
add ("categories":3);
add ("cards":3);
add ("candidates":3);
add ("businesses":3);
add ("brothers":3);
add ("branches":3);
add ("attempts":3);
add ("assets":3);
add ("articles":3);
add ("ages":3);
add ("agents":3);
add ("agencies":3);
add ("advantages":3);
add ("adults":3);
add ("wives":2);
add ("winners":2);
add ("wings":2);
add ("waves":2);
add ("waters":2);
add ("votes":2);
add ("voters":2);
add ("voices":2);
add ("villages":2);
add ("victims":2);
add ("versions":2);
add ("variations":2);
add ("variables":2);
add ("universities":2);
add ("trousers":2);
add ("trials":2);
add ("trends":2);
add ("transactions":2);
add ("topics":2);
add ("tonnes":2);
add ("titles":2);
add ("tickets":2);
add ("texts":2);
add ("taxes":2);
add ("tables":2);
add ("suggestions":2);
add ("styles":2);
add ("strategies":2);
add ("stores":2);
add ("spirits":2);
add ("speakers":2);
add ("sounds":2);
add ("sorts":2);
add ("songs":2);
add ("solutions":2);
add ("solicitors":2);
add ("ships":2);
add ("sheets":2);
add ("sets":2);
add ("sessions":2);
add ("securities":2);
add ("sectors":2);
add ("sciences":2);
add ("scenes":2);
add ("savings":2);
add ("samples":2);
add ("roots":2);
add ("roles":2);
add ("rocks":2);
add ("rivers":2);
add ("risks":2);
add ("restrictions":2);
add ("responsibilities":2);
add ("responses":2);
add ("researchers":2);
add ("relatives":2);
add ("refugees":2);
add ("reforms":2);
add ("references":2);
add ("recommendations":2);
add ("reactions":2);
add ("quantities":2);
add ("qualities":2);
add ("qualifications":2);
add ("publications":2);
add ("professionals":2);
add ("priorities":2);
add ("pressures":2);
add ("practitioners":2);
add ("posts":2);
add ("possibilities":2);
add ("passengers":2);
add ("participants":2);
add ("pairs":2);
add ("origins":2);
add ("offences":2);
add ("observations":2);
add ("obligations":2);
add ("nurses":2);
add ("nights":2);
add ("muscles":2);
add ("mountains":2);
add ("modules":2);
add ("minds":2);
add ("millions":2);
add ("messages":2);
add ("memories":2);
add ("mechanisms":2);
add ("meals":2);
add ("marks":2);
add ("manufacturers":2);
add ("males":2);
add ("magistrates":2);
add ("loans":2);
add ("lists":2);
add ("links":2);
add ("limits":2);
add ("libraries":2);
add ("lessons":2);
add ("lawyers":2);
add ("ladies":2);
add ("knees":2);
add ("keys":2);
add ("judges":2);
add ("islands":2);
add ("investors":2);
add ("instruments":2);
add ("injuries":2);
add ("initiatives":2);
add ("inches":2);
add ("improvements":2);
add ("humans":2);
add ("hotels":2);
add ("hospitals":2);
add ("hopes":2);
add ("holidays":2);
add ("holes":2);
add ("hills":2);
add ("guns":2);
add ("guidelines":2);
add ("glasses":2);
add ("germans":2);
add ("genes":2);
add ("generations":2);
add ("gardens":2);
add ("forests":2);
add ("foods":2);
add ("files":2);
add ("females":2);
add ("fees":2);
add ("fears":2);
add ("fans":2);
add ("expenses":2);
add ("estates":2);
add ("errors":2);
add ("ends":2);
add ("emotions":2);
add ("drivers":2);
add ("dreams":2);
add ("drawings":2);
add ("doubts":2);
add ("divisions":2);
add ("directions":2);
add ("devices":2);
add ("designs":2);
add ("democrats":2);
add ("decades":2);
add ("deaths":2);
add ("daughters":2);
add ("dates":2);
add ("cuts":2);
add ("customs":2);
add ("critics":2);
add ("controls":2);
add ("contributions":2);
add ("consumers":2);
add ("considerations":2);
add ("conservatives":2);
add ("connections":2);
add ("conclusions":2);
add ("concerns":2);
add ("concepts":2);
add ("concentrations":2);
add ("complaints":2);
add ("committees":2);
add ("colleges":2);
add ("chemicals":2);
add ("chapters":2);
add ("causes":2);
add ("cattle":2);
add ("calls":2);
add ("boxes":2);
add ("boundaries":2);
add ("boots":2);
add ("bones":2);
add ("bonds":2);
add ("boats":2);
add ("boards":2);
add ("blocks":2);
add ("bits":2);
add ("bills":2);
add ("beliefs":2);
add ("beds":2);
add ("bars":2);
add ("bands":2);
add ("babies":2);
add ("awards":2);
add ("authors":2);
add ("attacks":2);
add ("assumptions":2);
add ("associations":2);
add ("approaches":2);
add ("answers":2);
add ("amounts":2);
add ("americans":2);
add ("aims":2);
add ("agreements":2);
add ("acts":2);
add ("accidents":2);
add ("youngsters":1);
add ("writings":1);
add ("wounds":1);
add ("worlds":1);
add ("workshops":1);
add ("woods":1);
add ("witnesses":1);
add ("wishes":1);
add ("wines":1);
add ("winds":1);
add ("wheels":1);
add ("weekends":1);
add ("weaknesses":1);
add ("wars":1);
add ("volunteers":1);
add ("volumes":1);
add ("visits":1);
add ("videos":1);
add ("vessels":1);
add ("vegetables":1);
add ("varieties":1);
add ("uses":1);
add ("twins":1);
add ("trusts":1);
add ("trustees":1);
add ("troubles":1);
add ("travellers":1);
add ("trains":1);
add ("traditions":1);
add ("trades":1);
add ("traders":1);
add ("tracks":1);
add ("toys":1);
add ("tourists":1);
add ("tories":1);
add ("tons":1);
add ("tiles":1);
add ("ties":1);
add ("threats":1);
add ("themes":1);
add ("territories":1);
add ("tensions":1);
add ("tenants":1);
add ("temperatures":1);
add ("telecommunications":1);
add ("technologies":1);
add ("targets":1);
add ("tanks":1);
add ("tales":1);
add ("tactics":1);
add ("symbols":1);
add ("surveys":1);
add ("surroundings":1);
add ("surfaces":1);
add ("supplies":1);
add ("suppliers":1);
add ("sums":1);
add ("successes":1);
add ("substances":1);
add ("subsidies":1);
add ("strings":1);
add ("strengths":1);
add ("strangers":1);
add ("stocks":1);
add ("stands":1);
add ("spots":1);
add ("speeches":1);
add ("specimens":1);
add ("specialists":1);
add ("spaces":1);
add ("sizes":1);
add ("sisters":1);
add ("signals":1);
add ("shots":1);
add ("shelves":1);
add ("shapes":1);
add ("shadows":1);
add ("settlements":1);
add ("settings":1);
add ("sequences":1);
add ("senses":1);
add ("seeds":1);
add ("secrets":1);
add ("seasons":1);
add ("scots":1);
add ("scores":1);
add ("scholars":1);
add ("scales":1);
add ("sanctions":1);
add ("russians":1);
add ("runs":1);
add ("rumours":1);
add ("rows":1);
add ("routes":1);
add ("rounds":1);
add ("roses":1);
add ("rivals":1);
add ("rises":1);
add ("rewards":1);
add ("revenues":1);
add ("restaurants":1);
add ("respondents":1);
add ("respects":1);
add ("reserves":1);
add ("reservations":1);
add ("requests":1);
add ("republics":1);
add ("representations":1);
add ("remarks":1);
add ("remains":1);
add ("reductions":1);
add ("recordings":1);
add ("rebels":1);
add ("rats":1);
add ("ranks":1);
add ("railways":1);
add ("races":1);
add ("quarters":1);
add ("pubs":1);
add ("publishers":1);
add ("provinces":1);
add ("protests":1);
add ("proteins":1);
add ("prospects":1);
add ("proportions":1);
add ("programs":1);
add ("producers":1);
add ("prizes":1);
add ("privileges":1);
add ("prisons":1);
add ("priests":1);
add ("presents":1);
add ("preparations":1);
add ("preferences":1);
add ("prayers":1);
add ("potatoes":1);
add ("ports":1);
add ("populations":1);
add ("pools":1);
add ("polls":1);
add ("policemen":1);
add ("poles":1);
add ("poets":1);
add ("poems":1);
add ("pockets":1);
add ("plates":1);
add ("planes":1);
add ("pilots":1);
add ("phrases":1);
add ("phenomena":1);
add ("phases":1);
add ("performances":1);
add ("perceptions":1);
add ("peoples":1);
add ("pensions":1);
add ("pensioners":1);
add ("penalties":1);
add ("peasants":1);
add ("paths":1);
add ("passages":1);
add ("particles":1);
add ("parameters":1);
add ("panels":1);
add ("pains":1);
add ("packages":1);
add ("outcomes":1);
add ("organs":1);
add ("organisms":1);
add ("organisers":1);
add ("opponents":1);
add ("opinions":1);
add ("operators":1);
add ("offers":1);
add ("offenders":1);
add ("odds":1);
add ("occupations":1);
add ("observers":1);
add ("objections":1);
add ("novels":1);
add ("notions":1);
add ("networks":1);
add ("nerves":1);
add ("musicians":1);
add ("museums":1);
add ("movies":1);
add ("moves":1);
add ("motives":1);
add ("molecules":1);
add ("modes":1);
add ("mistakes":1);
add ("missiles":1);
add ("mines":1);
add ("miners":1);
add ("minerals":1);
add ("mice":1);
add ("metals":1);
add ("merchants":1);
add ("measurements":1);
add ("meanings":1);
add ("matches":1);
add ("masters":1);
add ("masses":1);
add ("margins":1);
add ("maps":1);
add ("mammals":1);
add ("makers":1);
add ("magazines":1);
add ("lovers":1);
add ("looks":1);
add ("locations":1);
add ("limitations":1);
add ("liabilities":1);
add ("lengths":1);
add ("lectures":1);
add ("leads":1);
add ("layers":1);
add ("lands":1);
add ("lakes":1);
add ("lads":1);
add ("laboratories":1);
add ("kings":1);
add ("kilometres":1);
add ("journals":1);
add ("journalists":1);
add ("jews":1);
add ("jeans":1);
add ("investments":1);
add ("investigations":1);
add ("interviews":1);
add ("intervals":1);
add ("interpretations":1);
add ("interactions":1);
add ("intentions":1);
add ("instances":1);
add ("insects":1);
add ("inhabitants":1);
add ("ingredients":1);
add ("influences":1);
add ("indicators":1);
add ("indians":1);
add ("increases":1);
add ("incomes":1);
add ("incidents":1);
add ("incentives":1);
add ("imports":1);
add ("illustrations":1);
add ("husbands":1);
add ("households":1);
add ("honours":1);
add ("holders":1);
add ("historians":1);
add ("heroes":1);
add ("heels":1);
add ("hearts":1);
add ("habits":1);
add ("guards":1);
add ("grants":1);
add ("governors":1);
add ("gods":1);
add ("gifts":1);
add ("gentlemen":1);
add ("gates":1);
add ("gaps":1);
add ("galleries":1);
add ("gains":1);
add ("futures":1);
add ("fruits":1);
add ("frames":1);
add ("fragments":1);
add ("foundations":1);
add ("fortunes":1);
add ("foreigners":1);
add ("followers":1);
add ("folk":1);
add ("floors":1);
add ("flights":1);
add ("flats":1);
add ("flames":1);
add ("fires":1);
add ("fathers":1);
add ("farms":1);
add ("falls":1);
add ("failures":1);
add ("factories":1);
add ("eyebrows":1);
add ("expressions":1);
add ("exports":1);
add ("explanations":1);
add ("exhibitions":1);
add ("exercises":1);
add ("executives":1);
add ("exchanges":1);
add ("exceptions":1);
add ("examinations":1);
add ("evenings":1);
add ("estimates":1);
add ("equations":1);
add ("environments":1);
add ("entries":1);
add ("enterprises":1);
add ("enquiries":1);
add ("engines":1);
add ("engineers":1);
add ("enemies":1);
add ("emissions":1);
add ("edges":1);
add ("economies":1);
add ("drinks":1);
add ("dollars":1);
add ("districts":1);
add ("disputes":1);
add ("dishes":1);
add ("diseases":1);
add ("disciplines":1);
add ("dimensions":1);
add ("developers":1);
add ("detectives":1);
add ("designers":1);
add ("descriptions":1);
add ("deputies":1);
add ("depths":1);
add ("deposits":1);
add ("demonstrations":1);
add ("delegates":1);
add ("definitions":1);
add ("defendants":1);
add ("defences":1);
add ("debts":1);
add ("deals":1);
add ("dealers":1);
add ("databases":1);
add ("dangers":1);
add ("damages":1);
add ("curtains":1);
add ("cups":1);
add ("cultures":1);
add ("crowds":1);
add ("crops":1);
add ("criticisms":1);
add ("crimes":1);
add ("crews":1);
add ("creditors":1);
add ("creatures":1);
add ("couples":1);
add ("counties":1);
add ("councillors":1);
add ("corporations":1);
add ("corners":1);
add ("conventions":1);
add ("contexts":1);
add ("contacts":1);
add ("consultants":1);
add ("constraints":1);
add ("conflicts":1);
add ("conferences":1);
add ("concessions":1);
add ("competitors":1);
add ("comparisons":1);
add ("communists":1);
add ("commitments":1);
add ("commentators":1);
add ("combinations":1);
add ("columns":1);
add ("colonies":1);
add ("collections":1);
add ("coins":1);
add ("clouds":1);
add ("clergy":1);
add ("circles":1);
add ("cigarettes":1);
add ("christians":1);
add ("choices":1);
add ("chips":1);
add ("chiefs":1);
add ("cheeks":1);
add ("charts":1);
add ("channels":1);
add ("champions":1);
add ("championships":1);
add ("chairs":1);
add ("chains":1);
add ("cats":1);
add ("casualties":1);
add ("carers":1);
add ("careers":1);
add ("camps":1);
add ("campaigns":1);
add ("cameras":1);
add ("calculations":1);
add ("buyers":1);
add ("businessmen":1);
add ("buses":1);
add ("budgets":1);
add ("breasts":1);
add ("bottles":1);
add ("borders":1);
add ("bombs":1);
add ("blues":1);
add ("blacks":1);
add ("bishops":1);
add ("beings":1);
add ("bedrooms":1);
add ("beans":1);
add ("beaches":1);
add ("bases":1);
add ("barriers":1);
add ("balls":1);
add ("bags":1);
add ("bacteria":1);
add ("backs":1);
add ("auditors":1);
add ("audiences":1);
add ("assessments":1);
add ("aspirations":1);
add ("armies":1);
add ("architects":1);
add ("appointments":1);
add ("applicants":1);
add ("appearances":1);
add ("appeals":1);
add ("angles":1);
add ("ambitions":1);
add ("alternatives":1);
add ("allowances":1);
add ("allies":1);
add ("allegations":1);
add ("advisers":1);
add ("advertisements":1);
add ("advances":1);
add ("addresses":1);
add ("actors":1);
add ("acres":1);
add ("acids":1);
add ("achievements":1);
add ("accountants":1);
add ("abilities":1);

--
-- prepositions
-- from the common word list
create prepositions;
set types = (varchar);
set weights = 1;
add ("in":169586);
add ("to":84535);
add ("for":76889);
add ("with":60602);
add ("on":59030);
add ("by":49067);
add ("at":43557);
add ("from":39087);
add ("into":15239);
add ("about":11594);
add ("as":10556);
add ("like":8786);
add ("between":8703);
add ("through":6762);
add ("after":5897);
add ("against":5354);
add ("under":5319);
add ("over":5024);
add ("out of":4393);
add ("without":4351);
add ("within":4249);
add ("during":4243);
add ("before":3662);
add ("such as":3161);
add ("towards":2732);
add ("up":2445);
add ("including":2265);
add ("among":2259);
add ("upon":2257);
add ("up to":2236);
add ("across":2023);
add ("behind":1949);
add ("off":1819);
add ("rather than":1684);
add ("around":1650);
add ("because of":1635);
add ("according to":1534);
add ("despite":1436);
add ("since":1368);
add ("down":1305);
add ("per":1275);
add ("near":1245);
add ("above":1244);
add ("throughout":1132);
add ("away from":1096);
add ("outside":1066);
add ("beyond":1036);
add ("until":895);
add ("in terms of":856);
add ("due to":856);
add ("worth":817);
add ("along":787);
add ("on to":781);
add ("as to":738);
add ("inside":661);
add ("plus":619);
add ("apart from":592);
add ("in front of":589);
add ("together with":569);
add ("beside":563);
add ("below":549);
add ("onto":522);
add ("subject to":497);
add ("beneath":488);
add ("along with":475);
add ("past":472);
add ("via":456);
add ("in relation to":428);
add ("amongst":423);
add ("other than":401);
add ("in addition to":331);
add ("in favour of":327);
add ("unlike":309);
add ("aged":306);
add ("concerning":305);
add ("prior to":296);
add ("but for":294);
add ("in spite of":275);
add ("in respect of":273);
add ("alongside":266);
add ("next to":265);
add ("as for":260);
add ("ahead of":246);
add ("on behalf of":245);
add ("on top of":223);
add ("regarding":219);
add ("depending on":208);
add ("in response to":195);
add ("in accordance with":195);
add ("except for":190);
add ("in the light of":170);
add ("except":170);
add ("instead of":158);
add ("in charge of":152);
add ("with regard to":151);
add ("in connection with":151);
add ("by means of":149);
add ("on the part of":148);
add ("out":145);
add ("in view of":145);
add ("as opposed to":143);
add ("by way of":133);
add ("with respect to":128);
add ("contrary to":128);
add ("toward":125);
add ("in conjunction with":122);
add ("in line with":117);
add ("till":113);
add ("following":113);
add ("in touch with":110);
add ("amid":109);
add ("in support of":103);
add ("in search of":100);
add ("relative to":96);
add ("in return for":93);
add ("versus":92);
add ("let alone":82);
add ("irrespective of":82);
add ("besides":81);
add ("with a view to":80);
add ("considering":79);
add ("owing to":77);
add ("near to":77);
add ("in excess of":77);
add ("in place of":76);
add ("in need of":76);
add ("in common with":76);
add ("as of":69);
add ("underneath":65);
add ("outside of":65);
add ("excluding":64);
add ("up until":59);
add ("as regards":59);
add ("aboard":54);
add ("pending":53);
add ("on board":53);
add ("notwithstanding":53);
add ("adjacent to":51);
add ("on account of":49);
add ("amidst":48);
add ("in keeping with":44);
add ("opposite":43);
add ("in comparison with":43);
add ("pursuant to":42);
add ("as against":41);
add ("minus":40);
add ("in contact with":40);
add ("in association with":40);
add ("thanks to":38);
add ("unto":37);
add ("with reference to":36);
add ("for fear of":36);
add ("nearer to":34);
add ("in pursuit of":33);
add ("in possession of":32);
add ("in case of":32);
add ("by reason of":31);
add ("in between":29);
add ("aside from":29);
add ("in answer to":28);
add ("in aid of":27);
add ("in regard to":25);
add ("in proportion to":25);
add ("as from":25);
add ("as between":25);
add ("save":23);
add ("into line with":23);
add ("in defence of":21);
add ("save for":20);
add ("as well as":20);
add ("vice":19);
add ("in receipt of":19);
add ("pro":18);
add ("than":16);
add ("of":15);
add ("nearest to":15);
add ("in reply to":15);
add ("in consultation with":15);
add ("nearer":14);
add ("in lieu of":14);
add ("in accord with":14);
add ("atop":14);
add ("in defiance of":13);
add ("pertaining to":12);
add ("off of":12);
add ("given":12);
add ("astride":12);
add ("nearest":11);
add ("in light of":11);
add ("subsequent to":10);
add ("out of touch with":10);
add ("alias":10);
add ("respecting":9);
add ("a la":9);
add ("barring":7);
add ("vis-a-vis":6);
add ("times":6);
add ("in face of":6);
add ("but":6);
add ("out of line with":5);
add ("due":5);
add ("in cooperation with":4);
add ("excepting":4);
add ("thru":3);
add ("top of":2);
add ("prp":2);
add ("in-between":2);
add ("in favor of":2);
add ("ere":2);
add ("afore":2);
add ("terms of":1);
add ("saving":1);
add ("in quest of":1);
add ("apropos":1);
add ("addition to":1);
add ("according":1);

-- 
-- verbs
-- from the common word list
create verbs;
set types = (varchar);
set weights = 1;
add ("make":523);
add ("see":487);
add ("get":464);
add ("take":436);
add ("go":409);
add ("know":359);
add ("say":281);
add ("find":273);
add ("give":265);
add ("think":238);
add ("come":235);
add ("help":209);
add ("use":177);
add ("look":177);
add ("want":168);
add ("tell":167);
add ("keep":166);
add ("like":161);
add ("work":158);
add ("provide":157);
add ("become":129);
add ("put":123);
add ("pay":114);
add ("feel":112);
add ("need":107);
add ("leave":103);
add ("meet":102);
add ("bring":101);
add ("ask":100);
add ("understand":98);
add ("show":97);
add ("mean":94);
add ("play":93);
add ("try":91);
add ("believe":89);
add ("hear":87);
add ("ensure":87);
add ("stop":82);
add ("move":82);
add ("allow":82);
add ("seem":81);
add ("continue":80);
add ("produce":79);
add ("talk":77);
add ("change":77);
add ("stay":76);
add ("buy":74);
add ("live":73);
add ("let":73);
add ("run":72);
add ("start":71);
add ("consider":71);
add ("accept":69);
add ("turn":68);
add ("remember":66);
add ("develop":66);
add ("lead":64);
add ("include":64);
add ("hold":64);
add ("avoid":64);
add ("carry":63);
add ("offer":62);
add ("create":62);
add ("speak":61);
add ("expect":61);
add ("appear":61);
add ("follow":60);
add ("explain":60);
add ("prevent":59);
add ("happen":59);
add ("call":59);
add ("win":57);
add ("support":57);
add ("achieve":57);
add ("reduce":56);
add ("write":55);
add ("set":55);
add ("return":55);
add ("learn":55);
add ("increase":54);
add ("stand":53);
add ("join":53);
add ("read":51);
add ("improve":51);
add ("apply":51);
add ("sell":50);
add ("remain":50);
add ("reach":50);
add ("build":50);
add ("prove":49);
add ("receive":48);
add ("deal":47);
add ("begin":46);
add ("spend":45);
add ("raise":45);
add ("form":45);
add ("establish":45);
add ("decide":44);
add ("wait":43);
add ("protect":43);
add ("lose":43);
add ("fall":43);
add ("maintain":42);
add ("act":42);
add ("send":41);
add ("discuss":41);
add ("suggest":40);
add ("save":40);
add ("cover":40);
add ("cause":40);
add ("break":40);
add ("sit":39);
add ("identify":39);
add ("eat":39);
add ("obtain":38);
add ("face":38);
add ("choose":38);
add ("add":38);
add ("encourage":37);
add ("enable":37);
add ("agree":37);
add ("require":36);
add ("pass":36);
add ("enjoy":36);
add ("draw":36);
add ("afford":36);
add ("open":35);
add ("end":35);
add ("control":35);
add ("answer":35);
add ("walk":34);
add ("visit":34);
add ("seek":34);
add ("enter":34);
add ("cut":34);
add ("serve":33);
add ("occur":33);
add ("kill":33);
add ("imagine":33);
add ("grow":33);
add ("forget":33);
add ("cope":33);
add ("die":32);
add ("bear":32);
add ("worry":31);
add ("wish":31);
add ("watch":31);
add ("determine":31);
add ("care":31);
add ("affect":31);
add ("share":30);
add ("pick":30);
add ("catch":30);
add ("mind":29);
add ("love":29);
add ("gain":29);
add ("fight":29);
add ("exist":29);
add ("examine":29);
add ("clear":29);
add ("manage":28);
add ("fit":28);
add ("describe":28);
add ("attend":28);
add ("sleep":27);
add ("introduce":27);
add ("drive":27);
add ("check":27);
add ("result":26);
add ("involve":26);
add ("survive":25);
add ("replace":25);
add ("remove":25);
add ("promote":25);
add ("operate":25);
add ("claim":25);
add ("argue":25);
add ("admit":25);
add ("rise":24);
add ("respond":24);
add ("recognise":24);
add ("realise":24);
add ("escape":24);
add ("discover":24);
add ("wear":23);
add ("study":23);
add ("represent":23);
add ("reflect":23);
add ("perform":23);
add ("matter":23);
add ("listen":23);
add ("indicate":23);
add ("extend":23);
add ("contain":23);
add ("complete":23);
add ("assume":23);
add ("present":22);
add ("marry":22);
add ("last":22);
add ("fill":22);
add ("depend":22);
add ("assess":22);
add ("secure":21);
add ("pull":21);
add ("mention":21);
add ("concentrate":21);
add ("close":21);
add ("benefit":21);
add ("assist":21);
add ("travel":20);
add ("prepare":20);
add ("persuade":20);
add ("lie":20);
add ("investigate":20);
add ("hope":20);
add ("handle":20);
add ("express":20);
add ("cost":20);
add ("treat":19);
add ("teach":19);
add ("suffer":19);
add ("sound":19);
add ("miss":19);
add ("fly":19);
add ("attract":19);
add ("supply":18);
add ("settle":18);
add ("reveal":18);
add ("retain":18);
add ("report":18);
add ("place":18);
add ("notice":18);
add ("match":18);
add ("explore":18);
add ("collect":18);
add ("adopt":18);
add ("throw":17);
add ("succeed":17);
add ("rely":17);
add ("refer":17);
add ("recover":17);
add ("point":17);
add ("force":17);
add ("defend":17);
add ("contribute":17);
add ("confirm":17);
add ("beat":17);
add ("arrive":17);
add ("arise":17);
add ("appreciate":17);
add ("touch":16);
add ("test":16);
add ("solve":16);
add ("sign":16);
add ("satisfy":16);
add ("resist":16);
add ("justify":16);
add ("influence":16);
add ("ignore":16);
add ("hide":16);
add ("drop":16);
add ("distinguish":16);
add ("deny":16);
add ("demonstrate":16);
add ("deliver":16);
add ("define":16);
add ("contact":16);
add ("blame":16);
add ("attempt":16);
add ("suit":15);
add ("rest":15);
add ("pursue":15);
add ("proceed":15);
add ("note":15);
add ("finish":15);
add ("drink":15);
add ("destroy":15);
add ("compete":15);
add ("arrange":15);
add ("acquire":15);
add ("vote":14);
add ("vary":14);
add ("trust":14);
add ("recognize":14);
add ("press":14);
add ("prefer":14);
add ("overcome":14);
add ("lay":14);
add ("impose":14);
add ("hit":14);
add ("generate":14);
add ("focus":14);
add ("fail":14);
add ("expand":14);
add ("exercise":14);
add ("advise":14);
add ("address":14);
add ("account":14);
add ("undertake":13);
add ("suppose":13);
add ("restore":13);
add ("refuse":13);
add ("record":13);
add ("recall":13);
add ("participate":13);
add ("mark":13);
add ("feed":13);
add ("emerge":13);
add ("date":13);
add ("alter":13);
add ("withdraw":12);
add ("tackle":12);
add ("ring":12);
add ("regard":12);
add ("preserve":12);
add ("plan":12);
add ("order":12);
add ("measure":12);
add ("laugh":12);
add ("judge":12);
add ("implement":12);
add ("earn":12);
add ("comply":12);
add ("compare":12);
add ("communicate":12);
add ("bother":12);
add ("attack":12);
add ("appeal":12);
add ("accommodate":12);
add ("wonder":11);
add ("strike":11);
add ("resolve":11);
add ("release":11);
add ("relate":11);
add ("realize":11);
add ("observe":11);
add ("limit":11);
add ("lift":11);
add ("invest":11);
add ("inform":11);
add ("hurt":11);
add ("hang":11);
add ("experience":11);
add ("enhance":11);
add ("detect":11);
add ("cry":11);
add ("cross":11);
add ("count":11);
add ("comment":11);
add ("challenge":11);
add ("back":11);
add ("welcome":10);
add ("view":10);
add ("transfer":10);
add ("sustain":10);
add ("strengthen":10);
add ("stick":10);
add ("state":10);
add ("sort":10);
add ("sing":10);
add ("select":10);
add ("ride":10);
add ("review":10);
add ("repeat":10);
add ("remind":10);
add ("relax":10);
add ("question":10);
add ("purchase":10);
add ("publish":10);
add ("predict":10);
add ("own":10);
add ("negotiate":10);
add ("monitor":10);
add ("launch":10);
add ("interpret":10);
add ("guarantee":10);
add ("exclude":10);
add ("convince":10);
add ("climb":10);
add ("celebrate":10);
add ("behave":10);
add ("analyse":10);
add ("acknowledge":10);
add ("thank":9);
add ("tend":9);
add ("submit":9);
add ("step":9);
add ("spread":9);
add ("specify":9);
add ("search":9);
add ("score":9);
add ("risk":9);
add ("reply":9);
add ("reject":9);
add ("recommend":9);
add ("qualify":9);
add ("push":9);
add ("permit":9);
add ("organise":9);
add ("light":9);
add ("land":9);
add ("issue":9);
add ("imply":9);
add ("illustrate":9);
add ("fulfil":9);
add ("finance":9);
add ("exploit":9);
add ("employ":9);
add ("display":9);
add ("demand":9);
add ("convey":9);
add ("construct":9);
add ("conclude":9);
add ("commit":9);
add ("approach":9);
add ("announce":9);
add ("abandon":9);
add ("train":8);
add ("switch":8);
add ("stimulate":8);
add ("slip":8);
add ("shoot":8);
add ("shake":8);
add ("separate":8);
add ("rule":8);
add ("resign":8);
add ("react":8);
add ("pretend":8);
add ("please":8);
add ("name":8);
add ("market":8);
add ("link":8);
add ("lend":8);
add ("jump":8);
add ("intervene":8);
add ("interfere":8);
add ("insist":8);
add ("guess":8);
add ("grant":8);
add ("free":8);
add ("facilitate":8);
add ("evaluate":8);
add ("engage":8);
add ("enforce":8);
add ("eliminate":8);
add ("ease":8);
add ("disappear":8);
add ("direct":8);
add ("convert":8);
add ("consult":8);
add ("conduct":8);
add ("complain":8);
add ("combine":8);
add ("clean":8);
add ("borrow":8);
add ("boost":8);
add ("belong":8);
add ("wash":7);
add ("warn":7);
add ("wake":7);
add ("trace":7);
add ("sue":7);
add ("spare":7);
add ("smile":7);
add ("shift":7);
add ("retire":7);
add ("restrict":7);
add ("register":7);
add ("practise":7);
add ("possess":7);
add ("kiss":7);
add ("incorporate":7);
add ("head":7);
add ("gather":7);
add ("forgive":7);
add ("fix":7);
add ("fetch":7);
add ("exceed":7);
add ("dry":7);
add ("differ":7);
add ("design":7);
add ("damage":7);
add ("constitute":7);
add ("compensate":7);
add ("charge":7);
add ("cease":7);
add ("capture":7);
add ("burn":7);
add ("breathe":7);
add ("approve":7);
add ("appoint":7);
add ("adjust":7);
add ("adapt":7);
add ("accompany":7);
add ("yield":6);
add ("waste":6);
add ("undermine":6);
add ("transform":6);
add ("trade":6);
add ("swim":6);
add ("stress":6);
add ("steal":6);
add ("smell":6);
add ("shut":6);
add ("sense":6);
add ("resume":6);
add ("relieve":6);
add ("reinforce":6);
add ("propose":6);
add ("paint":6);
add ("lower":6);
add ("locate":6);
add ("knock":6);
add ("invite":6);
add ("intend":6);
add ("hate":6);
add ("guide":6);
add ("function":6);
add ("fear":6);
add ("emphasise":6);
add ("doubt":6);
add ("dominate":6);
add ("dismiss":6);
add ("delay":6);
add ("declare":6);
add ("dare":6);
add ("dance":6);
add ("correct":6);
add ("cook":6);
add ("consist":6);
add ("conform":6);
add ("combat":6);
add ("clarify":6);
add ("calculate":6);
add ("blow":6);
add ("balance":6);
add ("aid":6);
add ("upset":5);
add ("tolerate":5);
add ("threaten":5);
add ("stretch":5);
add ("store":5);
add ("spot":5);
add ("speed":5);
add ("slow":5);
add ("sink":5);
add ("shout":5);
add ("rush":5);
add ("roll":5);
add ("reverse":5);
add ("respect":5);
add ("rescue":5);
add ("repay":5);
add ("repair":5);
add ("render":5);
add ("regain":5);
add ("recruit":5);
add ("reassure":5);
add ("protest":5);
add ("print":5);
add ("pray":5);
add ("phone":5);
add ("organize":5);
add ("oppose":5);
add ("occupy":5);
add ("obey":5);
add ("modify":5);
add ("minimise":5);
add ("list":5);
add ("kick":5);
add ("integrate":5);
add ("install":5);
add ("induce":5);
add ("impress":5);
add ("highlight":5);
add ("harm":5);
add ("halt":5);
add ("grasp":5);
add ("flow":5);
add ("fire":5);
add ("favour":5);
add ("extract":5);
add ("estimate":5);
add ("drag":5);
add ("divide":5);
add ("disturb":5);
add ("disclose":5);
add ("derive":5);
add ("counter":5);
add ("confront":5);
add ("conceal":5);
add ("coincide":5);
add ("co-operate":5);
add ("cancel":5);
add ("block":5);
add ("assure":5);
add ("assert":5);
add ("amount":5);
add ("aim":5);
add ("advance":5);
add ("absorb":5);
add ("wipe":4);
add ("undergo":4);
add ("translate":4);
add ("tie":4);
add ("tear":4);
add ("taste":4);
add ("swallow":4);
add ("suspect":4);
add ("surrender":4);
add ("surprise":4);
add ("suppress":4);
add ("supplement":4);
add ("struggle":4);
add ("stare":4);
add ("split":4);
add ("spell":4);
add ("smoke":4);
add ("shed":4);
add ("shape":4);
add ("seize":4);
add ("sail":4);
add ("safeguard":4);
add ("revive":4);
add ("reproduce":4);
add ("renew":4);
add ("regulate":4);
add ("regret":4);
add ("reform":4);
add ("reconcile":4);
add ("quote":4);
add ("quit":4);
add ("provoke":4);
add ("promise":4);
add ("progress":4);
add ("pose":4);
add ("plant":4);
add ("perceive":4);
add ("penetrate":4);
add ("opt":4);
add ("object":4);
add ("murder":4);
add ("mount":4);
add ("mix":4);
add ("maximise":4);
add ("manipulate":4);
add ("lack":4);
add ("knit":4);
add ("inspect":4);
add ("initiate":4);
add ("house":4);
add ("hire":4);
add ("hesitate":4);
add ("hand":4);
add ("greet":4);
add ("grab":4);
add ("foster":4);
add ("formulate":4);
add ("figure":4);
add ("feature":4);
add ("expose":4);
add ("exert":4);
add ("exchange":4);
add ("entertain":4);
add ("endure":4);
add ("emphasize":4);
add ("embrace":4);
add ("effect":4);
add ("dress":4);
add ("dream":4);
add ("double":4);
add ("distribute":4);
add ("dispose":4);
add ("discourage":4);
add ("dig":4);
add ("devote":4);
add ("devise":4);
add ("deter":4);
add ("defeat":4);
add ("decline":4);
add ("cure":4);
add ("curb":4);
add ("cool":4);
add ("contemplate":4);
add ("connect":4);
add ("concern":4);
add ("command":4);
add ("collapse":4);
add ("co-ordinate":4);
add ("cast":4);
add ("calm":4);
add ("breed":4);
add ("bind":4);
add ("ban":4);
add ("ascertain":4);
add ("arrest":4);
add ("anticipate":4);
add ("admire":4);
add ("administer":4);
add ("abolish":4);
add ("witness":3);
add ("withstand":3);
add ("widen":3);
add ("weigh":3);
add ("weaken":3);
add ("warm":3);
add ("wander":3);
add ("upgrade":3);
add ("update":3);
add ("unite":3);
add ("transport":3);
add ("track":3);
add ("top":3);
add ("terminate":3);
add ("telephone":3);
add ("tax":3);
add ("swing":3);
add ("sweep":3);
add ("suspend":3);
add ("supervise":3);
add ("summon":3);
add ("suffice":3);
add ("stir":3);
add ("stem":3);
add ("steer":3);
add ("squeeze":3);
add ("spoil":3);
add ("speculate":3);
add ("slide":3);
add ("signal":3);
add ("ship":3);
add ("service":3);
add ("scream":3);
add ("ruin":3);
add ("round":3);
add ("retrieve":3);
add ("restrain":3);
add ("request":3);
add ("reconsider":3);
add ("rebuild":3);
add ("race":3);
add ("punish":3);
add ("project":3);
add ("profit":3);
add ("prevail":3);
add ("pour":3);
add ("postpone":3);
add ("pack":3);
add ("offset":3);
add ("merge":3);
add ("master":3);
add ("manufacture":3);
add ("lock":3);
add ("isolate":3);
add ("interview":3);
add ("inspire":3);
add ("inhibit":3);
add ("indulge":3);
add ("hurry":3);
add ("hunt":3);
add ("honour":3);
add ("heal":3);
add ("guard":3);
add ("govern":3);
add ("frighten":3);
add ("flourish":3);
add ("flee":3);
add ("fish":3);
add ("fancy":3);
add ("fade":3);
add ("experiment":3);
add ("exhibit":3);
add ("execute":3);
add ("excuse":3);
add ("evolve":3);
add ("entail":3);
add ("endorse":3);
add ("encounter":3);
add ("elect":3);
add ("drift":3);
add ("divert":3);
add ("disrupt":3);
add ("disguise":3);
add ("discriminate":3);
add ("diminish":3);
add ("deserve":3);
add ("decrease":3);
add ("criticise":3);
add ("crack":3);
add ("correspond":3);
add ("copy":3);
add ("contract":3);
add ("contend":3);
add ("consolidate":3);
add ("confuse":3);
add ("confess":3);
add ("condemn":3);
add ("conceive":3);
add ("concede":3);
add ("compromise":3);
add ("comprehend":3);
add ("complement":3);
add ("commence":3);
add ("comfort":3);
add ("cheer":3);
add ("cater":3);
add ("bury":3);
add ("burst":3);
add ("bite":3);
add ("bet":3);
add ("bend":3);
add ("base":3);
add ("attain":3);
add ("attach":3);
add ("associate":3);
add ("assemble":3);
add ("apologise":3);
add ("allocate":3);
add ("alleviate":3);
add ("advertise":3);
add ("accelerate":3);
add ("zero":2);
add ("withhold":2);
add ("wind":2);
add ("weep":2);
add ("wave":2);
add ("warrant":2);
add ("verify":2);
add ("venture":2);
add ("urge":2);
add ("uphold":2);
add ("underline":2);
add ("underestimate":2);
add ("trouble":2);
add ("trigger":2);
add ("trap":2);
add ("transmit":2);
add ("tour":2);
add ("total":2);
add ("tighten":2);
add ("thrive":2);
add ("tempt":2);
add ("target":2);
add ("tap":2);
add ("swear":2);
add ("sum":2);
add ("substitute":2);
add ("subscribe":2);
add ("strip":2);
add ("spring":2);
add ("soften":2);
add ("snap":2);
add ("smooth":2);
add ("simplify":2);
add ("silence":2);
add ("signify":2);
add ("shrink":2);
add ("shop":2);
add ("shine":2);
add ("seal":2);
add ("sample":2);
add ("sacrifice":2);
add ("rub":2);
add ("rid":2);
add ("reward":2);
add ("revise":2);
add ("revert":2);
add ("retreat":2);
add ("reserve":2);
add ("resemble":2);
add ("research":2);
add ("rent":2);
add ("remedy":2);
add ("refrain":2);
add ("rectify":2);
add ("reconstruct":2);
add ("reclaim":2);
add ("rate":2);
add ("range":2);
add ("rally":2);
add ("rain":2);
add ("quantify":2);
add ("prosecute":2);
add ("prescribe":2);
add ("preclude":2);
add ("preach":2);
add ("practice":2);
add ("post":2);
add ("portray":2);
add ("pop":2);
add ("plead":2);
add ("pin":2);
add ("persist":2);
add ("pause":2);
add ("park":2);
add ("panic":2);
add ("owe":2);
add ("overthrow":2);
add ("override":2);
add ("overlook":2);
add ("outline":2);
add ("offend":2);
add ("obscure":2);
add ("oblige":2);
add ("no":2);
add ("notify":2);
add ("neglect":2);
add ("muster":2);
add ("minimize":2);
add ("melt":2);
add ("march":2);
add ("lunch":2);
add ("long":2);
add ("load":2);
add ("lessen":2);
add ("leap":2);
add ("lean":2);
add ("invoke":2);
add ("invent":2);
add ("invade":2);
add ("interrupt":2);
add ("interact":2);
add ("insure":2);
add ("instruct":2);
add ("insert":2);
add ("inherit":2);
add ("inflict":2);
add ("infer":2);
add ("incur":2);
add ("import":2);
add ("imitate":2);
add ("host":2);
add ("heat":2);
add ("glance":2);
add ("gauge":2);
add ("freeze":2);
add ("foresee":2);
add ("float":2);
add ("file":2);
add ("export":2);
add ("explode":2);
add ("evoke":2);
add ("evade":2);
add ("erect":2);
add ("eradicate":2);
add ("equip":2);
add ("envisage":2);
add ("enquire":2);
add ("encompass":2);
add ("emulate":2);
add ("embark":2);
add ("elicit":2);
add ("educate":2);
add ("edit":2);
add ("dwell":2);
add ("drown":2);
add ("drain":2);
add ("draft":2);
add ("divorce":2);
add ("distract":2);
add ("distort":2);
add ("dissolve":2);
add ("dispense":2);
add ("discharge":2);
add ("discern":2);
add ("disagree":2);
add ("differentiate":2);
add ("dictate":2);
add ("detail":2);
add ("descend":2);
add ("deprive":2);
add ("depart":2);
add ("deceive":2);
add ("debate":2);
add ("cultivate":2);
add ("criticize":2);
add ("creep":2);
add ("credit":2);
add ("counteract":2);
add ("cooperate":2);
add ("contest":2);
add ("consume":2);
add ("conserve":2);
add ("consent":2);
add ("conquer":2);
add ("congratulate":2);
add ("conflict":2);
add ("confine":2);
add ("confer":2);
add ("comprise":2);
add ("commemorate":2);
add ("cling":2);
add ("classify":2);
add ("chat":2);
add ("chase":2);
add ("campaign":2);
add ("brush":2);
add ("broaden":2);
add ("bolster":2);
add ("boil":2);
add ("boast":2);
add ("bid":2);
add ("betray":2);
add ("beg":2);
add ("award":2);
add ("await":2);
add ("avert":2);
add ("attribute":2);
add ("assign":2);
add ("articulate":2);
add ("arouse":2);
add ("amend":2);
add ("alert":2);
add ("adhere":2);
add ("activate":2);
add ("accuse":2);
add ("accumulate":2);
add ("accomplish":2);
add ("access":2);
add ("abide":2);
add ("wriggle":1);
add ("wreck":1);
add ("wrap":1);
add ("worship":1);
add ("worsen":1);
add ("wield":1);
add ("whisper":1);
add ("weave":1);
add ("waive":1);
add ("volunteer":1);
add ("visualize":1);
add ("visualise":1);
add ("violate":1);
add ("veto":1);
add ("vanish":1);
add ("validate":1);
add ("utter":1);
add ("utilise":1);
add ("unveil":1);
add ("unravel":1);
add ("unlock":1);
add ("unload":1);
add ("undo":1);
add ("underpin":1);
add ("uncover":1);
add ("twist":1);
add ("tune":1);
add ("tuck":1);
add ("trip":1);
add ("trim":1);
add ("tremble":1);
add ("tread":1);
add ("transcend":1);
add ("topple":1);
add ("tip":1);
add ("tidy":1);
add ("tick":1);
add ("thwart":1);
add ("testify":1);
add ("tender":1);
add ("tease":1);
add ("swell":1);
add ("sweat":1);
add ("swap":1);
add ("survey":1);
add ("summarize":1);
add ("summarise":1);
add ("suck":1);
add ("succumb":1);
add ("substantiate":1);
add ("subsidise":1);
add ("subdue":1);
add ("stumble":1);
add ("stuff":1);
add ("stroke":1);
add ("strive":1);
add ("stray":1);
add ("strain":1);
add ("straighten":1);
add ("stifle":1);
add ("steady":1);
add ("star":1);
add ("starve":1);
add ("stamp":1);
add ("staff":1);
add ("stabilize":1);
add ("stabilise":1);
add ("square":1);
add ("sponsor":1);
add ("spin":1);
add ("spill":1);
add ("specialise":1);
add ("spawn":1);
add ("soothe":1);
add ("soar":1);
add ("soak":1);
add ("sniff":1);
add ("sneak":1);
add ("snatch":1);
add ("smash":1);
add ("skip":1);
add ("simulate":1);
add ("shrug":1);
add ("shoulder":1);
add ("shorten":1);
add ("shore":1);
add ("shock":1);
add ("shield":1);
add ("shelter":1);
add ("shave":1);
add ("sharpen":1);
add ("seduce":1);
add ("screw":1);
add ("screen":1);
add ("scratch":1);
add ("scrap":1);
add ("scrape":1);
add ("scramble":1);
add ("scare":1);
add ("scan":1);
add ("scale":1);
add ("savour":1);
add ("sanction":1);
add ("salvage":1);
add ("sack":1);
add ("rouse":1);
add ("rot":1);
add ("rotate":1);
add ("root":1);
add ("rock":1);
add ("rob":1);
add ("roam":1);
add ("rival":1);
add ("rip":1);
add ("rewrite":1);
add ("revoke":1);
add ("rethink":1);
add ("retaliate":1);
add ("restructure":1);
add ("restart":1);
add ("resort":1);
add ("reside":1);
add ("resent":1);
add ("replicate":1);
add ("reopen":1);
add ("renounce":1);
add ("remark":1);
add ("relocate":1);
add ("relish":1);
add ("relinquish":1);
add ("rejoin":1);
add ("reinstate":1);
add ("rehearse":1);
add ("refute":1);
add ("refine":1);
add ("redress":1);
add ("redeem":1);
add ("recycle":1);
add ("recur":1);
add ("recreate":1);
add ("recoup":1);
add ("reckon":1);
add ("recite":1);
add ("recapture":1);
add ("reassess":1);
add ("reassert":1);
add ("rear":1);
add ("reap":1);
add ("reappear":1);
add ("re-open":1);
add ("re-establish":1);
add ("ratify":1);
add ("rape":1);
add ("rank":1);
add ("quell":1);
add ("quarrel":1);
add ("punch":1);
add ("pump":1);
add ("publicise":1);
add ("prosper":1);
add ("prop":1);
add ("pronounce":1);
add ("prompt":1);
add ("prolong":1);
add ("prohibit":1);
add ("procure":1);
add ("proclaim":1);
add ("probe":1);
add ("privatise":1);
add ("prise":1);
add ("presume":1);
add ("prejudice":1);
add ("precipitate":1);
add ("precede":1);
add ("praise":1);
add ("position":1);
add ("poison":1);
add ("plunge":1);
add ("plug":1);
add ("plough":1);
add ("plot":1);
add ("placate":1);
add ("pitch":1);
add ("pinpoint":1);
add ("picture":1);
add ("photograph":1);
add ("perpetuate":1);
add ("perfect":1);
add ("peer":1);
add ("overwhelm":1);
add ("overturn":1);
add ("overtake":1);
add ("overlap":1);
add ("outweigh":1);
add ("oust":1);
add ("omit":1);
add ("obstruct":1);
add ("nominate":1);
add ("nod":1);
add ("necessitate":1);
add ("near":1);
add ("navigate":1);
add ("narrow":1);
add ("multiply":1);
add ("mould":1);
add ("motivate":1);
add ("modernise":1);
add ("moderate":1);
add ("model":1);
add ("mobilize":1);
add ("mitigate":1);
add ("mislead":1);
add ("mimic":1);
add ("migrate":1);
add ("mess":1);
add ("merit":1);
add ("mend":1);
add ("mediate":1);
add ("maximize":1);
add ("mature":1);
add ("mate":1);
add ("materialise":1);
add ("mask":1);
add ("map":1);
add ("manoeuvre":1);
add ("manifest":1);
add ("lure":1);
add ("loose":1);
add ("loosen":1);
add ("lodge":1);
add ("lobby":1);
add ("linger":1);
add ("lighten":1);
add ("lick":1);
add ("license":1);
add ("liberate":1);
add ("liaise":1);
add ("levy":1);
add ("level":1);
add ("legislate":1);
add ("lecture":1);
add ("labour":1);
add ("label":1);
add ("jeopardise":1);
add ("jail":1);
add ("iron":1);
add ("intrude":1);
add ("intimidate":1);
add ("intercept":1);
add ("intensify":1);
add ("instil":1);
add ("inquire":1);
add ("injure":1);
add ("inject":1);
add ("impede":1);
add ("impart":1);
add ("impair":1);
add ("illuminate":1);
add ("hook":1);
add ("heed":1);
add ("haunt":1);
add ("haul":1);
add ("hasten":1);
add ("harness":1);
add ("harden":1);
add ("harbour":1);
add ("hammer":1);
add ("grip":1);
add ("grind":1);
add ("grieve":1);
add ("glimpse":1);
add ("generalize":1);
add ("generalise":1);
add ("gaze":1);
add ("furnish":1);
add ("fuck":1);
add ("frustrate":1);
add ("frame":1);
add ("forgo":1);
add ("forge":1);
add ("forestall":1);
add ("forecast":1);
add ("fool":1);
add ("fold":1);
add ("flush":1);
add ("flower":1);
add ("flood":1);
add ("flash":1);
add ("filter":1);
add ("fend":1);
add ("fathom":1);
add ("fast":1);
add ("fashion":1);
add ("faint":1);
add ("expire":1);
add ("expel":1);
add ("excite":1);
add ("exaggerate":1);
add ("evacuate":1);
add ("escort":1);
add ("equate":1);
add ("envy":1);
add ("entitle":1);
add ("entice":1);
add ("ensue":1);
add ("enrich":1);
add ("enlist":1);
add ("enlarge":1);
add ("endeavour":1);
add ("endanger":1);
add ("enclose":1);
add ("enact":1);
add ("empty":1);
add ("emigrate":1);
add ("embody":1);
add ("embarrass":1);
add ("elucidate":1);
add ("elaborate":1);
add ("echo":1);
add ("dust":1);
add ("dump":1);
add ("duck":1);
add ("drill":1);
add ("donate":1);
add ("dodge":1);
add ("document":1);
add ("dive":1);
add ("diversify":1);
add ("dissuade":1);
add ("disregard":1);
add ("dispute":1);
add ("displace":1);
add ("disperse":1);
add ("dispel":1);
add ("dismantle":1);
add ("dislodge":1);
add ("dislike":1);
add ("disentangle":1);
add ("discredit":1);
add ("discount":1);
add ("discipline":1);
add ("discard":1);
add ("disarm":1);
add ("disappoint":1);
add ("dip":1);
add ("dine":1);
add ("digest":1);
add ("diagnose":1);
add ("detract":1);
add ("deteriorate":1);
add ("detain":1);
add ("detach":1);
add ("despise":1);
add ("despair":1);
add ("desire":1);
add ("designate":1);
add ("desert":1);
add ("depress":1);
add ("deposit":1);
add ("deploy":1);
add ("depict":1);
add ("denounce":1);
add ("denote":1);
add ("demolish":1);
add ("delight":1);
add ("delete":1);
add ("delegate":1);
add ("defy":1);
add ("defuse":1);
add ("defraud":1);
add ("deflect":1);
add ("defer":1);
add ("deepen":1);
add ("deduce":1);
add ("decorate":1);
add ("decipher":1);
add ("decay":1);
add ("dash":1);
add ("curtail":1);
add ("curl":1);
add ("crush":1);
add ("crumble":1);
add ("crawl":1);
add ("crash":1);
add ("correlate":1);
add ("coordinate":1);
add ("convict":1);
add ("convene":1);
add ("contrast":1);
add ("contradict":1);
add ("constrain":1);
add ("conjure":1);
add ("confide":1);
add ("compose":1);
add ("complicate":1);
add ("compile":1);
add ("compel":1);
add ("commission":1);
add ("commend":1);
add ("colour":1);
add ("collaborate":1);
add ("clinch":1);
add ("cite":1);
add ("circumvent":1);
add ("circulate":1);
add ("choke":1);
add ("chew":1);
add ("cheat":1);
add ("chart":1);
add ("characterize":1);
add ("characterise":1);
add ("channel":1);
add ("chair":1);
add ("carve":1);
add ("capitalise":1);
add ("camp":1);
add ("bypass":1);
add ("bump":1);
add ("budge":1);
add ("browse":1);
add ("broadcast":1);
add ("brighten":1);
add ("breach":1);
add ("boycott":1);
add ("bow":1);
add ("bowl":1);
add ("bounce":1);
add ("boot":1);
add ("bomb":1);
add ("board":1);
add ("blind":1);
add ("blend":1);
add ("beware":1);
add ("bat":1);
add ("battle":1);
add ("bar":1);
add ("bargain":1);
add ("banish":1);
add ("average":1);
add ("avail":1);
add ("authorise":1);
add ("augment":1);
add ("assimilate":1);
add ("aspire":1);
add ("appropriate":1);
add ("applaud":1);
add ("appease":1);
add ("apologize":1);
add ("annoy":1);
add ("anger":1);
add ("amuse":1);
add ("allay":1);
add ("alienate":1);
add ("affirm":1);
add ("advocate":1);
add ("accrue":1);
add ("accord":1);
add ("abuse":1);


-- 
-- auxiliary.dst sentence forms 
create auxiliaries;
set types = (varchar);
set weights = 1;
add ("will": 2);
add ("will not": 1);
add ("shall": 2);
add ("shall not": 1);
add ("may": 2);
add ("may not": 1);
add ("might": 2);
add ("might not": 1);
add ("can": 2);
add ("cannot": 1);
add ("could": 2);
add ("could not": 1);
add ("must": 2);
add ("must not": 1);
add ("ought to": 2);
add ("used to": 2);
add ("should": 2);
add ("should not": 1);
add ("would": 2);
add ("would not": 1);


-- 
-- terminator.dst 
create terminators;
set types = (varchar);
set weights = 1;
add (".": 10);
add ("\;": 2);
add (" --": 1);
add (":": 1);


-- 
-- sentences.dst sentence forms 
create sentences;
set types = (varchar);
set weights = 1;
add ("N VT": 1);
add ("J N VT": 1);
add ("J, J N VT": 1);
add ("D J N VT": 1);
add ("N X VT": 1);
add ("J N X VT": 1);
add ("J, J N X VT": 1);
add ("D J N X VT": 1);
add ("N V DT": 1);
add ("J N V DT": 1);
add ("J, J N V DT": 1);
add ("D J N V DT": 1);
add ("N X V DT": 1);
add ("J N X V DT": 1);
add ("J, J N X V DT": 1);
add ("D J N X V DT": 1);
add ("N V P A NT": 1);
add ("J N V P A NT": 1);
add ("J, J N V P A NT": 1);
add ("D J N V P A NT": 1);
add ("N X V P A NT": 1);
add ("J N X V P A NT": 1);
add ("J, J N X V P A NT": 1);
add ("D J N X V P A NT": 1);
add ("N V D P A NT": 1);
add ("J N V D P A NT": 1);
add ("J, J N V D P A NT": 1);
add ("D J N V D P A NT": 1);
add ("N X V D P A NT": 1);
add ("J N X V D P A NT": 1);
add ("J, J N X V D P A NT": 1);
add ("D J N X V D P A NT": 1);
add ("N V NT": 1);
add ("J N V NT": 1);
add ("J, J N V NT": 1);
add ("D J N V NT": 1);
add ("N X V NT": 1);
add ("J N X V NT": 1);
add ("J, J N X V NT": 1);
add ("D J N X V NT": 1);
add ("N V D NT": 1);
add ("J N V D NT": 1);
add ("J, J N V D NT": 1);
add ("D J N V D NT": 1);
add ("N X V D NT": 1);
add ("J N X V D NT": 1);
add ("J, J N X V D NT": 1);
add ("D J N X V D NT": 1);
add ("N V J NT": 1);
add ("J N V J NT": 1);
add ("J, J N V J NT": 1);
add ("D J N V J NT": 1);
add ("N X V J NT": 1);
add ("J N X V J NT": 1);
add ("J, J N X V J NT": 1);
add ("D J N X V J NT": 1);
add ("N V D J NT": 1);
add ("J N V D J NT": 1);
add ("J, J N V D J NT": 1);
add ("D J N V D J NT": 1);
add ("N X V D J NT": 1);
add ("J N X V D J NT": 1);
add ("J, J N X V D J NT": 1);
add ("D J N X V D J J NT": 1);
add ("N V J, J NT": 1);
add ("J N V J, J NT": 1);
add ("J, J N V J, J NT": 1);
add ("D J N V J, J NT": 1);
add ("N X V J, J NT": 1);
add ("J N X V J, J NT": 1);
add ("J, J N X V J, J NT": 1);
add ("D J N X V J, J NT": 1);
add ("N V D J, J NT": 1);
add ("J N V D J, J NT": 1);
add ("J, J N V D J, J NT": 1);
add ("D J N V D J, J NT": 1);
add ("N X V D J, J NT": 1);
add ("J N X V D J, J NT": 1);
add ("J, J N X V D J, J NT": 1);
add ("D J N X V D J, J NT": 1);
add ("N V D J NT": 1);
add ("J N V D J NT": 1);
add ("J, J N V D J NT": 1);
add ("D J N V D J NT": 1);
add ("N X V D J NT": 1);
add ("J N X V D J NT": 1);
add ("J, J N X V D J NT": 1);
add ("D J N X V D J NT": 1);
add ("N V D D J NT": 1);
add ("J N V D D J NT": 1);
add ("J, J N V D D J NT": 1);
add ("D J N V D D J NT": 1);
add ("N X V D D J NT": 1);
add ("J N X V D D J NT": 1);
add ("J, J N X V D D J NT": 1);
add ("D J N X V D D J NT": 1);



-- 
-- syllables.dst 
create syllables;
set types = (varchar);
set weights = 1;
add ("bar":1);
add ("ought":1);
add ("able":1);
add ("pri":1);
add ("ese":1);
add ("anti":1);
add ("cally":1);
add ("ation":1);
add ("eing":1);
add ("n st":1);

