-- 
-- Legal Notice 
--
-- This document and associated source code (the "Work") is a part of a 
-- benchmark specification maintained by the TPC. 
-- 
-- The TPC reserves all right, title, and interest to the Work as provided 
-- under U.S. and international laws, including without limitation all patent 
-- and trademark rights therein. 
-- 
-- No Warranty 
-- 
-- 1.1 TO THE MAXIMUM EXTENT PERMITTED BY APPLICABLE LAW, THE INFORMATION 
--     CONTAINED HEREIN IS PROVIDED "AS IS" AND WITH ALL FAULTS, AND THE 
--     AUTHORS AND DEVELOPERS OF THE WORK HEREBY DISCLAIM ALL OTHER 
--     WARRANTIES AND CONDITIONS, EITHER EXPRESS, IMPLIED OR STATUTORY, 
--     INCLUDING, BUT NOT LIMITED TO, ANY (IF ANY) IMPLIED WARRANTIES, 
--     DUTIES OR CONDITIONS OF MERCHANTABILITY, OF FITNESS FOR A PARTICULAR 
--     PURPOSE, OF ACCURACY OR COMPLETENESS OF RESPONSES, OF RESULTS, OF 
--     W<PERSON><PERSON><PERSON><PERSON>IKE EFFORT, OF LACK OF VIRUSES, AND OF LACK OF NEGLIGENCE. 
--     ALSO, THERE IS NO WARRANTY OR CONDITION OF TITLE, QUIET ENJOYMENT, 
--     QUIET POSSESSION, CORRESPONDENCE TO DESCRIPTION OR NON-INFRINGEMENT 
--     WITH REGARD TO THE WORK. 
-- 1.2 IN NO EVENT WILL ANY AUTHOR OR DEVELOPER OF THE WORK BE LIABLE TO 
--     ANY OTHER PARTY FOR ANY DAMAGES, INCLUDING BUT NOT LIMITED TO THE 
--     COST OF PROCURING SUBSTITUTE GOODS OR SERVICES, LOST PROFITS, LOSS 
--     OF USE, LOSS OF DATA, OR ANY INCIDENTAL, CONSEQUENTIAL, DIRECT, 
--     INDIRECT, OR SPECIAL DAMAGES WHETHER UNDER CONTRACT, TORT, WARRANTY,
--     OR OTHERWISE, ARISING IN ANY WAY OUT OF THIS OR ANY OTHER AGREEMENT 
--     RELATING TO THE WORK, WHETHER OR NOT SUCH AUTHOR OR DEVELOPER HAD 
--     ADVANCE NOTICE OF THE POSSIBILITY OF SUCH DAMAGES. 
-- 
-- Contributors:
-- Gradient Systems
--
-- 
--
-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=
-- Begin distribution definitions
-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=
------
-- calendar
--      value			weight
--      =====			======
--  1   day number		uniform, non-leap year
--  2   month name		uniform, leap year
--  3   day of month	sales liklihood (skewed)
--  4   season			"" "" (leap year)
--  5   month number	returns liklihood (skewed)
--  6   quarter			"" "" (leap year)
--  7   first of month  combined skew (low+medium_high)
--  8   is_holiday      low
--  9                   medium
-- 10                   high
------
create calendar;
set types = (int, varchar, int, varchar, int, int, int, int);
set weights = 10;
set names = (day_seq, month_name, date, season, month_num, quarter, fom, holiday:uniform, uniform_leap, sales, sales_leap, returns, returns_leap, skewed, low, medium, high);
add (1,   "January",  1, "Winter",  1, 1, 1, 1:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (2,   "January",  2, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (3,   "January",  3, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (4,   "January",  4, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (5,   "January",  5, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (6,   "January",  6, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (7,   "January",  7, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (8,   "January",  8, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (9,   "January",  9, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (10,  "January", 10, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (11,  "January", 11, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (12,  "January", 12, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (13,  "January", 13, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (14,  "January", 14, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (15,  "January", 15, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (16,  "January", 16, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (17,  "January", 17, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (18,  "January", 18, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (19,  "January", 19, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (20,  "January", 20, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (21,  "January", 21, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (22,  "January", 22, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (23,  "January", 23, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (24,  "January", 24, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (25,  "January", 25, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (26,  "January", 26, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (27,  "January", 27, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (28,  "January", 28, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (29,  "January", 29, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (30,  "January", 30, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (31,  "January", 31, "Winter",  1, 1, 1, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (32,  "February",  1, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (33,  "February",  2, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (34,  "February",  3, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (35,  "February",  4, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (36,  "February",  5, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (37,  "February",  6, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (38,  "February",  7, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (39,  "February",  8, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (40,  "February",  9, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (41,  "February", 10, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (42,  "February", 11, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (43,  "February", 12, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (44,  "February", 13, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (45,  "February", 14, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (46,  "February", 15, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (47,  "February", 16, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (48,  "February", 17, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (49,  "February", 18, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (50,  "February", 19, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (51,  "February", 20, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (52,  "February", 21, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (53,  "February", 22, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (54,  "February", 23, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (55,  "February", 24, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (56,  "February", 25, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (57,  "February", 26, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (58,  "February", 27, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (59,  "February", 28, "Winter",  2, 1, 32, 0:1, 1, 29, 29, 100, 100, 29, 1, 0, 0);
add (60,  "February", 29, "Winter",  2, 1, 32, 0:0, 1, 0, 29, 0, 100, 0, 0, 0, 0);
add (61,  "March",  1, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (62,  "March",  2, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (63,  "March",  3, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (64,  "March",  4, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (65,  "March",  5, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (66,  "March",  6, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (67,  "March",  7, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (68,  "March",  8, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (69,  "March",  9, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (70,  "March", 10, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (71,  "March", 11, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (72,  "March", 12, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (73,  "March", 13, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (74,  "March", 14, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (75,  "March", 15, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (76,  "March", 16, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (77,  "March", 17, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (78,  "March", 18, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (79,  "March", 19, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (80,  "March", 20, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (81,  "March", 21, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (82,  "March", 22, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (83,  "March", 23, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (84,  "March", 24, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (85,  "March", 25, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (86,  "March", 26, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (87,  "March", 27, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (88,  "March", 28, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (89,  "March", 29, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (90,  "March", 30, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (91,  "March", 31, "Spring",  3, 1, 61, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (92,  "April",  1, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (93,  "April",  2, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (94,  "April",  3, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (95,  "April",  4, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (96,  "April",  5, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (97,  "April",  6, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (98,  "April",  7, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (99,  "April",  8, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (100,  "April",  9, "Spring", 4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (101,  "April", 10, "Spring", 4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (102,  "April", 11, "Spring", 4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (103,  "April", 12, "Spring", 4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (104,  "April", 13, "Spring", 4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (105,  "April", 14, "Spring", 4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (106,  "April", 15, "Spring", 4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (107,  "April", 16, "Spring", 4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (108,  "April", 17, "Spring", 4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (109,  "April", 18, "Spring", 4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (110,  "April", 19, "Spring", 4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (111,  "April", 20, "Spring", 4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (112,  "April", 21, "Spring", 4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (113,  "April", 22, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (114,  "April", 23, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (115,  "April", 24, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (116,  "April", 25, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (117,  "April", 26, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (118,  "April", 27, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (119,  "April", 28, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (120,  "April", 29, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (121,  "April", 30, "Spring",  4, 2, 92, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (122,  "May",  1, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (123,  "May",  2, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (124,  "May",  3, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (125,  "May",  4, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (126,  "May",  5, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (127,  "May",  6, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (128,  "May",  7, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (129,  "May",  8, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (130,  "May",  9, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (131,  "May", 10, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (132,  "May", 11, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (133,  "May", 12, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (134,  "May", 13, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (135,  "May", 14, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (136,  "May", 15, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (137,  "May", 16, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (138,  "May", 17, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (139,  "May", 18, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (140,  "May", 19, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (141,  "May", 20, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (142,  "May", 21, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (143,  "May", 22, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (144,  "May", 23, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (145,  "May", 24, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (146,  "May", 25, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (147,  "May", 26, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (148,  "May", 27, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (149,  "May", 28, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (150,  "May", 29, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (151,  "May", 30, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (152,  "May", 31, "Spring",  5, 2, 122, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (153,  "June",  1, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (154,  "June",  2, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (155,  "June",  3, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (156,  "June",  4, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (157,  "June",  5, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (158,  "June",  6, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (159,  "June",  7, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (160,  "June",  8, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (161,  "June",  9, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (162,  "June", 10, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (163,  "June", 11, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (164,  "June", 12, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (165,  "June", 13, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (166,  "June", 14, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (167,  "June", 15, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (168,  "June", 16, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (169,  "June", 17, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (170,  "June", 18, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (171,  "June", 19, "Summer", 6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (172,  "June", 20, "Summer",  6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (173,  "June", 21, "Summer",  6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (174,  "June", 22, "Summer",  6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (175,  "June", 23, "Summer",  6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (176,  "June", 24, "Summer",  6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (177,  "June", 25, "Summer",  6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (178,  "June", 26, "Summer",  6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (179,  "June", 27, "Summer",  6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (180,  "June", 28, "Summer",  6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (181,  "June", 29, "Summer",  6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (182,  "June", 30, "Summer",  6, 2, 153, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (183,  "July",  1, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (184,  "July",  2, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (185,  "July",  3, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (186,  "July",  4, "Summer",  7, 3, 183, 1:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (187,  "July",  5, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (188,  "July",  6, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (189,  "July",  7, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (190,  "July",  8, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (191,  "July",  9, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (192,  "July", 10, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (193,  "July", 11, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (194,  "July", 12, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (195,  "July", 13, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (196,  "July", 14, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (197,  "July", 15, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (198,  "July", 16, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (199,  "July", 17, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (200,  "July", 18, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (201,  "July", 19, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (202,  "July", 20, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (203,  "July", 21, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (204,  "July", 22, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (205,  "July", 23, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (206,  "July", 24, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (207,  "July", 25, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (208,  "July", 26, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (209,  "July", 27, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (210,  "July", 28, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (211,  "July", 29, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (212,  "July", 30, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (213,  "July", 31, "Summer",  7, 3, 183, 0:1, 1, 29, 29, 29, 29, 29, 1, 0, 0);
add (214,  "August",  1, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (215,  "August",  2, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (216,  "August",  3, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (217,  "August",  4, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (218,  "August",  5, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (219,  "August",  6, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (220,  "August",  7, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (221,  "August",  8, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (222,  "August",  9, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (223,  "August", 10, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (224,  "August", 11, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (225,  "August", 12, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (226,  "August", 13, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (227,  "August", 14, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (228,  "August", 15, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (229,  "August", 16, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (230,  "August", 17, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (231,  "August", 18, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (232,  "August", 19, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (233,  "August", 20, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (234,  "August", 21, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (235,  "August", 22, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (236,  "August", 23, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (237,  "August", 24, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (238,  "August", 25, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (239,  "August", 26, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (240,  "August", 27, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (241,  "August", 28, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (242,  "August", 29, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (243,  "August", 30, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (244,  "August", 31, "Summer",  8, 3, 214, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (245,  "September",  1, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (246,  "September",  2, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (247,  "September",  3, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (248,  "September",  4, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (249,  "September",  5, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (250,  "September",  6, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (251,  "September",  7, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (252,  "September",  8, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (253,  "September",  9, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (254,  "September", 10, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (255,  "September", 11, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (256,  "September", 12, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (257,  "September", 13, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (258,  "September", 14, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (259,  "September", 15, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (260,  "September", 16, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (261,  "September", 17, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (262,  "September", 18, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (263,  "September", 19, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (264,  "September", 20, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (265,  "September", 21, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (266,  "September", 22, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (267,  "September", 23, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (268,  "September", 24, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (269,  "September", 25, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (270,  "September", 26, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (271,  "September", 27, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (272,  "September", 28, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (273,  "September", 29, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (274,  "September", 30, "Fall",  9, 3, 245, 0:1, 1, 66, 66, 29, 29, 66, 0, 1, 0);
add (275,  "October",  1, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (276,  "October",  2, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (277,  "October",  3, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (278,  "October",  4, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (279,  "October",  5, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (280,  "October",  6, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (281,  "October",  7, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (282,  "October",  8, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (283,  "October",  9, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (284,  "October", 10, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (285,  "October", 11, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (286,  "October", 12, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (287,  "October", 13, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (288,  "October", 14, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (289,  "October", 15, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (290,  "October", 16, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (291,  "October", 17, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (292,  "October", 18, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (293,  "October", 19, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (294,  "October", 20, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (295,  "October", 21, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (296,  "October", 22, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (297,  "October", 23, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (298,  "October", 24, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (299,  "October", 25, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (300,  "October", 26, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (301,  "October", 27, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (302,  "October", 28, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (303,  "October", 29, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (304,  "October", 30, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (305,  "October", 31, "Fall",  10, 4, 275, 0: 1, 1, 66, 66, 66, 66, 66, 0, 1, 0); 
add (306,  "November",  1, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (307,  "November",  2, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (308,  "November",  3, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (309,  "November",  4, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (310,  "November",  5, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (311,  "November",  6, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (312,  "November",  7, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (313,  "November",  8, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (314,  "November",  9, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (315,  "November", 10, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (316,  "November", 11, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (317,  "November", 12, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (318,  "November", 13, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (319,  "November", 14, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (320,  "November", 15, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (321,  "November", 16, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (322,  "November", 17, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (323,  "November", 18, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (324,  "November", 19, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (325,  "November", 20, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (326,  "November", 21, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (327,  "November", 22, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (328,  "November", 23, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (329,  "November", 24, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (330,  "November", 25, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (331,  "November", 26, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (332,  "November", 27, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (333,  "November", 28, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (334,  "November", 29, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (335,  "November", 30, "Fall",  11, 4, 306, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (336,  "December",  1, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (337,  "December",  2, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (338,  "December",  3, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (339,  "December",  4, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (340,  "December",  5, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (341,  "December",  6, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (342,  "December",  7, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (343,  "December",  8, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (344,  "December",  9, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (345,  "December", 10, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (346,  "December", 11, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (347,  "December", 12, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (348,  "December", 13, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (349,  "December", 14, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (350,  "December", 15, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (351,  "December", 16, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (352,  "December", 17, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (353,  "December", 18, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (354,  "December", 19, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (355,  "December", 20, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (356,  "December", 21, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (357,  "December", 22, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (358,  "December", 23, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (359,  "December", 24, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (360,  "December", 25, "Winter",  12, 4, 336, 1: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (361,  "December", 26, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (362,  "December", 27, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (363,  "December", 28, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (364,  "December", 29, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (365,  "December", 30, "Winter",  12, 4, 336, 0: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 
add (366,  "December", 31, "Winter",  12, 4, 336, 1: 1, 1, 100, 100, 66, 66, 100, 0, 0, 1); 

------
-- week_info
-- values: ("name", is_weekday, is_weekend)
-- weights: (uniform, skewed)
------
create week_info;
set types = (varchar, int, int);
set weights = 1;
add ("Sunday", 0, 1: 1);
add ("Monday", 1, 0: 1);
add ("Tuesday", 1, 0: 1);
add ("Wednesday", 1, 0: 1);
add ("Thursday", 1, 0: 1);
add ("Friday", 1, 0: 1);
add ("Saturday", 0, 1: 1);
