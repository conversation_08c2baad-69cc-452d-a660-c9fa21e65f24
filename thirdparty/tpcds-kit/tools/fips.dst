--
-- Legal Notice
--
-- This document and associated source code (the "Work") is a part of a
-- benchmark specification maintained by the TPC.
--
-- The TPC reserves all right, title, and interest to the Work as provided
-- under U.S. and international laws, including without limitation all patent
-- and trademark rights therein.
--
-- No Warranty
--
-- 1.1 TO THE MAXIMUM EXTENT PERMITTED BY APPLICABLE LAW, THE INFORMATION
--     CONTAINED HEREIN IS PROVIDED "AS IS" AND WITH ALL FAULTS, AND THE
--     AUTHORS AND DEVELOPERS OF THE WORK HEREBY DISCLAIM ALL OTHER
--     WARRANTIES AND CONDITIONS, EITHER EXPRESS, IMPLIED OR STATUTORY,
--     INCLUDING, BUT NOT LIMITED TO, ANY (IF ANY) IMPLIED WARRANTIES,
--     DUTIES OR CONDITIONS OF MERCHANTABILITY, OF FITNESS FOR A PARTICULAR
--     PURPOSE, OF ACCURACY OR COMPLETENESS OF RESPONSES, OF RESULTS, OF
--     W<PERSON><PERSON><PERSON><PERSON>IKE EFFORT, OF LACK OF VIRUSES, AND OF LACK OF NEGLIGENCE.
--     ALSO, THERE IS NO WARRANTY OR CONDITION OF TITLE, QUIET ENJOYMENT,
--     QUIET POSSESSION, CORRESPONDENCE TO DESCRIPTION OR NON-INFRINGEMENT
--     WITH REGARD TO THE WORK.
-- 1.2 IN NO EVENT WILL ANY AUTHOR OR DEVELOPER OF THE WORK BE LIABLE TO
--     ANY OTHER PARTY FOR ANY DAMAGES, INCLUDING BUT NOT LIMITED TO THE
--     COST OF PROCURING SUBSTITUTE GOODS OR SERVICES, LOST PROFITS, LOSS
--     OF USE, LOSS OF DATA, OR ANY INCIDENTAL, CONSEQUENTIAL, DIRECT,
--     INDIRECT, OR SPECIAL DAMAGES WHETHER UNDER CONTRACT, TORT, WARRANTY,
--     OR OTHERWISE, ARISING IN ANY WAY OUT OF THIS OR ANY OTHER AGREEMENT
--     RELATING TO THE WORK, WHETHER OR NOT SUCH AUTHOR OR DEVELOPER HAD
--     ADVANCE NOTICE OF THE POSSIBILITY OF SUCH DAMAGES.
--
-- Contributors:
-- Gradient Systems
--
--
--
--

-- fields						weights (aliases in parens)
-- ======						=======
-- 1: FIPS code	(fips)			1: uniform (uniform)
-- 2: county name (name) 		2: population (population)
-- 3: state abreviation (st)	3: timezone weighting (tz)
-- 4: full state name (state)	4: in zone1 (tz90)
-- 5: ZIP prefix (zone)			5: in zone2 (tz9)
-- 6: gmt offset (gmt)			6 in zone3 (tz1)
--
-- NOTE: missing data for St. Clair County, AL
-- NOTE: missing data for St. Francis County, AR
-- NOTE: missing data for St. Johns County, FL
-- NOTE: missing data for St. Lucie County, FL
-- NOTE: missing data for St. Clair County, IL
-- NOTE: missing data for St. Joseph County, IN
-- NOTE: missing data for St. Bernard Parish, LA
-- NOTE: missing data for St. Charles Parish, LA
-- NOTE: missing data for St. Helena Parish, LA
-- NOTE: missing data for St. John the Baptist Parish, LA
-- NOTE: missing data for St. Landry Parish, LA
-- NOTE: missing data for St. Martin Parish, LA
-- NOTE: missing data for St. Mary Parish, LA
-- NOTE: missing data for St. Tammany Parish, LA
-- NOTE: missing data for St. Mary's County, MD
-- NOTE: missing data for St. Clair County, MI
-- NOTE: missing data for St. Joseph County, MI
-- NOTE: missing data for St. Louis County, MN
-- NOTE: missing data for St. Charles County, MO
-- NOTE: missing data for St. Clair County, MO
-- NOTE: missing data for Ste. Genevieve County, MO
-- NOTE: missing data for St. Francois County, MO
-- NOTE: missing data for St. Louis County, MO
-- NOTE: missing data for St. Lawrence County, NY
-- NOTE: missing data for St. Croix County, WI
-- NOTE: outer ketchikan AK and some of the census areas from AK
------
create fips_county;
set types = (int, varchar, varchar, varchar, varchar, int);
set weights = 6; 
set names = (fips, county, st, state, zone, gmt:uniform, population, tz, tz90, tz9, tz1);
add (47187,"Williamson County", "TN", "Tennesee", "3", -5:1, 117569, 1387, 1, 0, 0);
add (46137,"Ziebach County", "SD", "South Dakota", "5", -6:1, 2176, 1148, 1, 0, 0);
add (01127,"Walker County", "AL", "Alabama", "3", -6:1, 71027, 1148, 1, 0, 0);
add (45039,"Fairfield County", "SC", "South Carolina", "2", -5:1, 22394, 1387, 1, 0, 0);
add (39139,"Richland County", "OH", "Ohio", "4", -5:1, 127342, 1387, 1, 0, 0);
add (22041,"Franklin Parish", "LA", "Louisiana", "7", -6:1, 22163, 1148, 1, 0, 0);
add (29061,"Daviess County", "MO", "Mosourri", "6", -6:1, 7842, 1148, 1, 0, 0);
add (13013,"Barrow County", "GA", "Georgia", "3", -5:1, 40344, 1387, 1, 0, 0);
add (26095,"Luce County", "MI", "Michigan", "4", -5:1, 6640, 1387, 1, 0, 0);
add (22053,"Jefferson Davis Parish", "LA", "Louisiana", "7", -6:1, 31607, 1148, 1, 0, 0);
add (35047,"San Miguel County", "NM", "New Mexico", "8", -7:1, 28996, 4647, 1, 0, 0);
add (26063,"Huron County", "MI", "Michigan", "4", -5:1, 35303, 1387, 1, 0, 0);
add (42043,"Dauphin County", "PA", "Pennsylvania", "1", -5:1, 245579, 1387, 1, 0, 0);
add (36005,"Bronx County", "NY", "New York", "1", -5:1, 1195599, 1387, 1, 0, 0);
add (53037,"Kittitas County", "WA", "Washiington", "9", -8:1, 31714, 3240, 0, 1, 0);
add (01097,"Mobile County", "AL", "Alabama", "3", -6:1, 399429, 1148, 1, 0, 0);
add (13221,"Oglethorpe County", "GA", "Georgia", "3", -5:1, 11418, 1387, 1, 0, 0);
add (27159,"Wadena County", "MN", "Minnesota", "5", -6:1, 13145, 1148, 1, 0, 0);
add (18099,"Marshall County", "IN", "Indiana", "4", -5:1, 45444, 1387, 1, 0, 0);
add (27113,"Pennington County", "MN", "Minnesota", "5", -6:1, 13562, 1148, 1, 0, 0);
add (12075,"Levy County", "FL", "Florida", "3", -5:1, 31796, 1387, 1, 0, 0);
add (54081,"Raleigh County", "WV", "West Virginia", "2", -5:1, 79066, 1387, 1, 0, 0);
add (37099,"Jackson County", "NC", "North Carolina", "2", -5:1, 30210, 1387, 1, 0, 0);
add (08077,"Mesa County", "CO", "Colorado", "8", -7:1, 112891, 4647, 1, 0, 0);
add (31067,"Gage County", "NE", "Nebraska", "6", -6:1, 22666, 1148, 1, 0, 0);
add (48323,"Maverick County", "TX", "Texas", "7", -6:1, 48131, 1148, 1, 0, 0);
add (26053,"Gogebic County", "MI", "Michigan", "4", -5:1, 17097, 1387, 1, 0, 0);
add (17145,"Perry County", "IL", "Illinois", "6", -6:1, 21048, 1148, 1, 0, 0);
add (47165,"Sumner County", "TN", "Tennesee", "3", -6:1, 124056, 1148, 1, 0, 0);
add (35051,"Sierra County", "NM", "New Mexico", "8", -7:1, 11025, 4647, 1, 0, 0);
add (20077,"Harper County", "KS", "Kansas", "6", -6:1, 6430, 1148, 1, 0, 0);
add (50009,"Essex County", "VT", "Vermont", "0", -5:1, 6580, 1387, 1, 0, 0);
add (18001,"Adams County", "IN", "Indiana", "4", -5:1, 33083, 1387, 1, 0, 0);
add (40057,"Harmon County", "OK", "Oklahoma", "7", -6:1, 3479, 1148, 1, 0, 0);
add (06013,"Contra Costa County", "CA", "California", "9", -8:1, 918200, 3240, 0, 1, 0);
add (21005,"Anderson County", "KY", "Kentucky", "4", -6:1, 18587, 1148, 1, 0, 0);
add (31005,"Arthur County", "NE", "Nebraska", "6", -6:1, 428, 1148, 1, 0, 0);
add (26145,"Saginaw County", "MI", "Michigan", "4", -5:1, 210101, 1387, 1, 0, 0);
add (48361,"Orange County", "TX", "Texas", "7", -6:1, 84905, 1148, 1, 0, 0);
add (13273,"Terrell County", "GA", "Georgia", "3", -5:1, 11146, 1387, 1, 0, 0);
add (40029,"Coal County", "OK", "Oklahoma", "7", -6:1, 6009, 1148, 1, 0, 0);
add (27057,"Hubbard County", "MN", "Minnesota", "5", -6:1, 16935, 1148, 1, 0, 0);
add (19007,"Appanoose County", "IA", "Iowa", "5", -6:1, 13595, 1148, 1, 0, 0);
add (48445,"Terry County", "TX", "Texas", "7", -6:1, 12896, 1148, 1, 0, 0);
add (34033,"Salem County", "NJ", "New Jersey", "0", -5:1, 64912, 1387, 1, 0, 0);
add (20125,"Montgomery County", "KS", "Kansas", "6", -6:1, 37089, 1148, 1, 0, 0);
add (35037,"Quay County", "NM", "New Mexico", "8", -7:1, 10024, 4647, 1, 0, 0);
add (13083,"Dade County", "GA", "Georgia", "3", -5:1, 15058, 1387, 1, 0, 0);
add (48041,"Brazos County", "TX", "Texas", "7", -6:1, 133407, 1148, 1, 0, 0);
add (48255,"Karnes County", "TX", "Texas", "7", -6:1, 12358, 1148, 1, 0, 0);
add (30095,"Stillwater County", "MT", "Montana", "6", -7:1, 8069, 4647, 1, 0, 0);
add (21199,"Pulaski County", "KY", "Kentucky", "4", -5:1, 56294, 1387, 1, 0, 0);
add (06043,"Mariposa County", "CA", "California", "9", -8:1, 15877, 3240, 0, 1, 0);
add (55047,"Green Lake County", "WI", "Wisconnsin", "5", -6:1, 19438, 1148, 1, 0, 0);
add (22125,"West Feliciana Parish", "LA", "Louisiana", "7", -6:1, 13446, 1148, 1, 0, 0);
add (24005,"Baltimore County", "MD", "Maryland", "2", -5:1, 721874, 1387, 1, 0, 0);
add (47029,"Cocke County", "TN", "Tennesee", "3", -5:1, 31968, 1387, 1, 0, 0);
add (48383,"Reagan County", "TX", "Texas", "7", -6:1, 4203, 1148, 1, 0, 0);
add (37083,"Halifax County", "NC", "North Carolina", "2", -5:1, 56433, 1387, 1, 0, 0);
add (45001,"Abbeville County", "SC", "South Carolina", "2", -5:1, 24632, 1387, 1, 0, 0);
add (51181,"Surry County", "VA", "Virginia", "2", -5:1, 6471, 1387, 1, 0, 0);
add (51111,"Lunenburg County", "VA", "Virginia", "2", -5:1, 12043, 1387, 1, 0, 0);
add (27117,"Pipestone County", "MN", "Minnesota", "5", -6:1, 10092, 1148, 1, 0, 0);
add (06103,"Tehama County", "CA", "California", "9", -8:1, 54073, 3240, 0, 1, 0);
add (48071,"Chambers County", "TX", "Texas", "7", -6:1, 23743, 1148, 1, 0, 0);
add (35025,"Lea County", "NM", "New Mexico", "8", -7:1, 56091, 4647, 1, 0, 0);
add (13319,"Wilkinson County", "GA", "Georgia", "3", -5:1, 10838, 1387, 1, 0, 0);
add (13201,"Miller County", "GA", "Georgia", "3", -5:1, 6409, 1387, 1, 0, 0);
add (26159,"Van Buren County", "MI", "Michigan", "4", -5:1, 75666, 1387, 1, 0, 0);
add (18171,"Warren County", "IN", "Indiana", "4", -5:1, 8251, 1387, 1, 0, 0);
add (40019,"Carter County", "OK", "Oklahoma", "7", -6:1, 44503, 1148, 1, 0, 0);
add (31129,"Nuckolls County", "NE", "Nebraska", "6", -7:1, 5226, 4647, 1, 0, 0);
add (41035,"Klamath County", "OR", "Oregon", "9", -8:1, 63185, 3240, 0, 1, 0);
add (24039,"Somerset County", "MD", "Maryland", "2", -5:1, 24296, 1387, 1, 0, 0);
add (13039,"Camden County", "GA", "Georgia", "3", -5:1, 47443, 1387, 1, 0, 0);
add (22081,"Red River Parish", "LA", "Louisiana", "7", -6:1, 9599, 1148, 1, 0, 0);
add (35021,"Harding County", "NM", "New Mexico", "8", -7:1, 899, 4647, 1, 0, 0);
add (20067,"Grant County", "KS", "Kansas", "6", -6:1, 8012, 1148, 1, 0, 0);
add (51089,"Henry County", "VA", "Virginia", "2", -5:1, 55627, 1387, 1, 0, 0);
add (40105,"Nowata County", "OK", "Oklahoma", "7", -6:1, 9969, 1148, 1, 0, 0);
add (31065,"Furnas County", "NE", "Nebraska", "6", -6:1, 5381, 1148, 1, 0, 0);
add (13213,"Murray County", "GA", "Georgia", "3", -5:1, 32682, 1387, 1, 0, 0);
add (55103,"Richland County", "WI", "Wisconnsin", "5", -6:1, 17891, 1148, 1, 0, 0);
add (38049,"McHenry County", "ND", "North Dakota", "5", -6:1, 6076, 1148, 1, 0, 0);
add (55063,"La Crosse County", "WI", "Wisconnsin", "5", -6:1, 102565, 1148, 1, 0, 0);
add (55099,"Price County", "WI", "Wisconnsin", "5", -6:1, 15813, 1148, 1, 0, 0);
add (49031,"Piute County", "UT", "Utah", "8", -7:1, 1402, 4647, 1, 0, 0);
add (20057,"Ford County", "KS", "Kansas", "6", -6:1, 29382, 1148, 1, 0, 0);
add (28057,"Itawamba County", "MS", "Missippi", "5", -6:1, 21072, 1148, 1, 0, 0);
add (16051,"Jefferson County", "ID", "Idaho", "8", -7:1, 19118, 4647, 1, 0, 0);
add (01035,"Conecuh County", "AL", "Alabama", "3", -6:1, 13976, 1148, 1, 0, 0);
add (31111,"Lincoln County", "NE", "Nebraska", "6", -7:1, 33515, 4647, 1, 0, 0);
add (51187,"Warren County", "VA", "Virginia", "2", -5:1, 30126, 1387, 1, 0, 0);
add (28049,"Hinds County", "MS", "Missippi", "5", -6:1, 247144, 1148, 1, 0, 0);
add (48237,"Jack County", "TX", "Texas", "7", -6:1, 7430, 1148, 1, 0, 0);
add (31013,"Box Butte County", "NE", "Nebraska", "6", -6:1, 12832, 1148, 1, 0, 0);
add (08003,"Alamosa County", "CO", "Colorado", "8", -7:1, 14448, 4647, 1, 0, 0);
add (54055,"Mercer County", "WV", "West Virginia", "2", -5:1, 63794, 1387, 1, 0, 0);
add (55013,"Burnett County", "WI", "Wisconnsin", "5", -6:1, 14646, 1148, 1, 0, 0);
add (27143,"Sibley County", "MN", "Minnesota", "5", -6:1, 14573, 1148, 1, 0, 0);
add (18127,"Porter County", "IN", "Indiana", "4", -5:1, 145726, 1387, 1, 0, 0);
add (02068,"Denali Borough", "AK", "Alaska", "9", -9:1, 1970, 1174, 0, 0, 1);
add (45075,"Orangeburg County", "SC", "South Carolina", "2", -5:1, 87865, 1387, 1, 0, 0);
add (21003,"Allen County", "KY", "Kentucky", "4", -6:1, 16555, 1148, 1, 0, 0);
add (30003,"Big Horn County", "MT", "Montana", "6", -7:1, 12631, 4647, 1, 0, 0);
add (08029,"Delta County", "CO", "Colorado", "8", -7:1, 26619, 4647, 1, 0, 0);
add (18109,"Morgan County", "IN", "Indiana", "4", -5:1, 65500, 1387, 1, 0, 0);
add (21043,"Carter County", "KY", "Kentucky", "4", -6:1, 26848, 1148, 1, 0, 0);
add (01123,"Tallapoosa County", "AL", "Alabama", "3", -6:1, 40606, 1148, 1, 0, 0);
add (09013,"Tolland County", "CT", "Connecticut", "0", -5:1, 131831, 1387, 1, 0, 0);
add (39127,"Perry County", "OH", "Ohio", "4", -5:1, 34290, 1387, 1, 0, 0);
add (42059,"Greene County", "PA", "Pennsylvania", "1", -5:1, 40742, 1387, 1, 0, 0);
add (20165,"Rush County", "KS", "Kansas", "6", -6:1, 3413, 1148, 1, 0, 0);
add (30027,"Fergus County", "MT", "Montana", "6", -7:1, 12271, 4647, 1, 0, 0);
add (46021,"Campbell County", "SD", "South Dakota", "5", -6:1, 1917, 1148, 1, 0, 0);
add (13249,"Schley County", "GA", "Georgia", "3", -5:1, 3945, 1387, 1, 0, 0);
add (51179,"Stafford County", "VA", "Virginia", "2", -5:1, 87055, 1387, 1, 0, 0);
add (01107,"Pickens County", "AL", "Alabama", "3", -6:1, 21089, 1148, 1, 0, 0);
add (36027,"Dutchess County", "NY", "New York", "1", -5:1, 265317, 1387, 1, 0, 0);
add (31017,"Brown County", "NE", "Nebraska", "6", -6:1, 3553, 1148, 1, 0, 0);
add (36109,"Tompkins County", "NY", "New York", "1", -5:1, 96020, 1387, 1, 0, 0);
add (39143,"Sandusky County", "OH", "Ohio", "4", -5:1, 62216, 1387, 1, 0, 0);
add (13117,"Forsyth County", "GA", "Georgia", "3", -5:1, 86130, 1387, 1, 0, 0);
add (02070,"Dillingham Census Area", "AK", "Alaska", "9", -9:1, 4534, 1174, 0, 0, 1);
add (51011,"Appomattox County", "VA", "Virginia", "2", -5:1, 13134, 1387, 1, 0, 0);
add (51640,"Galax city", "VA", "Virginia", "2", -5:1, 6864, 1387, 1, 0, 0);
add (17097,"Lake County", "IL", "Illinois", "6", -6:1, 605116, 1148, 1, 0, 0);
add (17093,"Kendall County", "IL", "Illinois", "6", -6:1, 51817, 1148, 1, 0, 0);
add (37093,"Hoke County", "NC", "North Carolina", "2", -5:1, 30424, 1387, 1, 0, 0);
add (53031,"Jefferson County", "WA", "Washiington", "9", -8:1, 26232, 3240, 0, 1, 0);
add (33001,"Belknap County", "NH", "New Hampshire", "0", -5:1, 52481, 1387, 1, 0, 0);
add (48465,"Val Verde County", "TX", "Texas", "7", -6:1, 43831, 1148, 1, 0, 0);
add (16055,"Kootenai County", "ID", "Idaho", "8", -7:1, 101390, 4647, 1, 0, 0);
add (05003,"Ashley County", "AR", "Arkansas", "7", -6:1, 24448, 1148, 1, 0, 0);
add (39063,"Hancock County", "OH", "Ohio", "4", -5:1, 68922, 1387, 1, 0, 0);
add (39165,"Warren County", "OH", "Ohio", "4", -5:1, 146033, 1387, 1, 0, 0);
add (42091,"Montgomery County", "PA", "Pennsylvania", "1", -5:1, 719718, 1387, 1, 0, 0);
add (41065,"Wasco County", "OR", "Oregon", "9", -8:1, 23059, 3240, 0, 1, 0);
add (28101,"Newton County", "MS", "Missippi", "5", -6:1, 21516, 1148, 1, 0, 0);
add (48169,"Garza County", "TX", "Texas", "7", -6:1, 4608, 1148, 1, 0, 0);
add (17153,"Pulaski County", "IL", "Illinois", "6", -6:1, 7203, 1148, 1, 0, 0);
add (13153,"Houston County", "GA", "Georgia", "3", -5:1, 105808, 1387, 1, 0, 0);
add (13063,"Clayton County", "GA", "Georgia", "3", -5:1, 208999, 1387, 1, 0, 0);
add (30065,"Musselshell County", "MT", "Montana", "6", -7:1, 4605, 4647, 1, 0, 0);
add (06035,"Lassen County", "CA", "California", "9", -8:1, 33285, 3240, 0, 1, 0);
add (24043,"Washington County", "MD", "Maryland", "2", -5:1, 127352, 1387, 1, 0, 0);
add (46009,"Bon Homme County", "SD", "South Dakota", "5", -6:1, 7696, 1148, 1, 0, 0);
add (55059,"Kenosha County", "WI", "Wisconnsin", "5", -6:1, 144339, 1148, 1, 0, 0);
add (01003,"Baldwin County", "AL", "Alabama", "3", -6:1, 132828, 1148, 1, 0, 0);
add (23013,"Knox County", "ME", "Maine", "0", -5:1, 37847, 1387, 1, 0, 0);
add (31147,"Richardson County", "NE", "Nebraska", "6", -7:1, 9420, 4647, 1, 0, 0);
add (08047,"Gilpin County", "CO", "Colorado", "8", -7:1, 4188, 4647, 1, 0, 0);
add (28119,"Quitman County", "MS", "Missippi", "5", -6:1, 9914, 1148, 1, 0, 0);
add (47033,"Crockett County", "TN", "Tennesee", "3", -5:1, 13959, 1387, 1, 0, 0);
add (26117,"Montcalm County", "MI", "Michigan", "4", -5:1, 60559, 1387, 1, 0, 0);
add (25023,"Plymouth County", "MA", "Massachusetts", "0", -5:1, 467588, 1387, 1, 0, 0);
add (04015,"Mohave County", "AZ", "Arizona", "8", -7:1, 130618, 4647, 1, 0, 0);
add (54059,"Mingo County", "WV", "West Virginia", "2", -5:1, 31926, 1387, 1, 0, 0);
add (17033,"Crawford County", "IL", "Illinois", "6", -6:1, 20954, 1148, 1, 0, 0);
add (37069,"Franklin County", "NC", "North Carolina", "2", -5:1, 44743, 1387, 1, 0, 0);
add (40129,"Roger Mills County", "OK", "Oklahoma", "7", -6:1, 3580, 1148, 1, 0, 0);
add (27125,"Red Lake County", "MN", "Minnesota", "5", -6:1, 4270, 1148, 1, 0, 0);
add (17035,"Cumberland County", "IL", "Illinois", "6", -6:1, 11124, 1148, 1, 0, 0);
add (27051,"Grant County", "MN", "Minnesota", "5", -6:1, 6178, 1148, 1, 0, 0);
add (13053,"Chattahoochee County", "GA", "Georgia", "3", -5:1, 16679, 1387, 1, 0, 0);
add (27149,"Stevens County", "MN", "Minnesota", "5", -6:1, 10136, 1148, 1, 0, 0);
add (17079,"Jasper County", "IL", "Illinois", "6", -6:1, 10647, 1148, 1, 0, 0);
add (31159,"Seward County", "NE", "Nebraska", "6", -7:1, 16299, 4647, 1, 0, 0);
add (37159,"Rowan County", "NC", "North Carolina", "2", -5:1, 125505, 1387, 1, 0, 0);
add (34021,"Mercer County", "NJ", "New Jersey", "0", -5:1, 331629, 1387, 1, 0, 0);
add (47081,"Hickman County", "TN", "Tennesee", "3", -5:1, 20553, 1387, 1, 0, 0);
add (55115,"Shawano County", "WI", "Wisconnsin", "5", -6:1, 38756, 1148, 1, 0, 0);
add (46097,"Miner County", "SD", "South Dakota", "5", -7:1, 2799, 4647, 1, 0, 0);
add (13075,"Cook County", "GA", "Georgia", "3", -5:1, 15011, 1387, 1, 0, 0);
add (37021,"Buncombe County", "NC", "North Carolina", "2", -5:1, 194873, 1387, 1, 0, 0);
add (21159,"Martin County", "KY", "Kentucky", "4", -5:1, 12120, 1387, 1, 0, 0);
add (36007,"Broome County", "NY", "New York", "1", -5:1, 196545, 1387, 1, 0, 0);
add (04021,"Pinal County", "AZ", "Arizona", "8", -7:1, 146929, 4647, 1, 0, 0);
add (29165,"Platte County", "MO", "Mosourri", "6", -6:1, 70068, 1148, 1, 0, 0);
add (12105,"Polk County", "FL", "Florida", "3", -5:1, 452584, 1387, 1, 0, 0);
add (37151,"Randolph County", "NC", "North Carolina", "2", -5:1, 121289, 1387, 1, 0, 0);
add (51133,"Northumberland County", "VA", "Virginia", "2", -5:1, 11513, 1387, 1, 0, 0);
add (19091,"Humboldt County", "IA", "Iowa", "5", -6:1, 10323, 1148, 1, 0, 0);
add (20003,"Anderson County", "KS", "Kansas", "6", -6:1, 8060, 1148, 1, 0, 0);
add (37179,"Union County", "NC", "North Carolina", "2", -5:1, 110017, 1387, 1, 0, 0);
add (05035,"Crittenden County", "AR", "Arkansas", "7", -6:1, 49905, 1148, 1, 0, 0);
add (20059,"Franklin County", "KS", "Kansas", "6", -6:1, 24768, 1148, 1, 0, 0);
add (02100,"Haines Borough", "AK", "Alaska", "9", -9:1, 2225, 1174, 0, 0, 1);
add (18181,"White County", "IN", "Indiana", "4", -5:1, 25338, 1387, 1, 0, 0);
add (13207,"Monroe County", "GA", "Georgia", "3", -5:1, 19645, 1387, 1, 0, 0);
add (42021,"Cambria County", "PA", "Pennsylvania", "1", -5:1, 156080, 1387, 1, 0, 0);
add (29035,"Carter County", "MO", "Mosourri", "6", -6:1, 6387, 1148, 1, 0, 0);
add (17149,"Pike County", "IL", "Illinois", "6", -6:1, 17341, 1148, 1, 0, 0);
add (48015,"Austin County", "TX", "Texas", "7", -6:1, 23439, 1148, 1, 0, 0);
add (22071,"Orleans Parish", "LA", "Louisiana", "7", -6:1, 465538, 1148, 1, 0, 0);
add (40099,"Murray County", "OK", "Oklahoma", "7", -6:1, 12335, 1148, 1, 0, 0);
add (21015,"Boone County", "KY", "Kentucky", "4", -6:1, 79671, 1148, 1, 0, 0);
add (19061,"Dubuque County", "IA", "Iowa", "5", -6:1, 87806, 1148, 1, 0, 0);
add (29125,"Maries County", "MO", "Mosourri", "6", -6:1, 8473, 1148, 1, 0, 0);
add (51009,"Amherst County", "VA", "Virginia", "2", -5:1, 30042, 1387, 1, 0, 0);
add (38045,"LaMoure County", "ND", "North Dakota", "5", -6:1, 4759, 1148, 1, 0, 0);
add (16069,"Nez Perce County", "ID", "Idaho", "8", -7:1, 36852, 4647, 1, 0, 0);
add (26025,"Calhoun County", "MI", "Michigan", "4", -5:1, 141005, 1387, 1, 0, 0);
add (37027,"Caldwell County", "NC", "North Carolina", "2", -5:1, 76096, 1387, 1, 0, 0);
add (02164,"Lake and Peninsula Borough", "AK", "Alaska", "9", -9:1, 1699, 1174, 0, 0, 1);
add (16039,"Elmore County", "ID", "Idaho", "8", -7:1, 25173, 4647, 1, 0, 0);
add (30085,"Roosevelt County", "MT", "Montana", "6", -7:1, 10987, 4647, 1, 0, 0);
add (26011,"Arenac County", "MI", "Michigan", "4", -5:1, 16413, 1387, 1, 0, 0);
add (13121,"Fulton County", "GA", "Georgia", "3", -5:1, 739367, 1387, 1, 0, 0);
add (17051,"Fayette County", "IL", "Illinois", "6", -6:1, 21972, 1148, 1, 0, 0);
add (29049,"Clinton County", "MO", "Mosourri", "6", -6:1, 19070, 1148, 1, 0, 0);
add (21237,"Wolfe County", "KY", "Kentucky", "4", -5:1, 7366, 1387, 1, 0, 0);
add (39103,"Medina County", "OH", "Ohio", "4", -5:1, 144019, 1387, 1, 0, 0);
add (39043,"Erie County", "OH", "Ohio", "4", -5:1, 78279, 1387, 1, 0, 0);
add (17087,"Johnson County", "IL", "Illinois", "6", -6:1, 13283, 1148, 1, 0, 0);
add (18157,"Tippecanoe County", "IN", "Indiana", "4", -5:1, 139005, 1387, 1, 0, 0);
add (16083,"Twin Falls County", "ID", "Idaho", "8", -7:1, 62265, 4647, 1, 0, 0);
add (40149,"Washita County", "OK", "Oklahoma", "7", -6:1, 11796, 1148, 1, 0, 0);
add (13233,"Polk County", "GA", "Georgia", "3", -5:1, 36308, 1387, 1, 0, 0);
add (19015,"Boone County", "IA", "Iowa", "5", -6:1, 26233, 1148, 1, 0, 0);
add (19125,"Marion County", "IA", "Iowa", "5", -6:1, 31357, 1148, 1, 0, 0);
add (20171,"Scott County", "KS", "Kansas", "6", -6:1, 5018, 1148, 1, 0, 0);
add (42053,"Forest County", "PA", "Pennsylvania", "1", -5:1, 5002, 1387, 1, 0, 0);
add (48205,"Hartley County", "TX", "Texas", "7", -6:1, 5102, 1148, 1, 0, 0);
add (20199,"Wallace County", "KS", "Kansas", "6", -6:1, 1802, 1148, 1, 0, 0);
add (47145,"Roane County", "TN", "Tennesee", "3", -6:1, 50026, 1148, 1, 0, 0);
add (29113,"Lincoln County", "MO", "Mosourri", "6", -6:1, 36556, 1148, 1, 0, 0);
add (30009,"Carbon County", "MT", "Montana", "6", -7:1, 9444, 4647, 1, 0, 0);
add (53049,"Pacific County", "WA", "Washiington", "9", -8:1, 20802, 3240, 0, 1, 0);
add (01045,"Dale County", "AL", "Alabama", "3", -6:1, 48872, 1148, 1, 0, 0);
add (51071,"Giles County", "VA", "Virginia", "2", -5:1, 16242, 1387, 1, 0, 0);
add (06099,"Stanislaus County", "CA", "California", "9", -8:1, 426460, 3240, 0, 1, 0);
add (28093,"Marshall County", "MS", "Missippi", "5", -6:1, 32296, 1148, 1, 0, 0);
add (47049,"Fentress County", "TN", "Tennesee", "3", -5:1, 16184, 1387, 1, 0, 0);
add (01095,"Marshall County", "AL", "Alabama", "3", -6:1, 80346, 1148, 1, 0, 0);
add (25025,"Suffolk County", "MA", "Massachusetts", "0", -5:1, 641715, 1387, 1, 0, 0);
add (13287,"Turner County", "GA", "Georgia", "3", -5:1, 9160, 1387, 1, 0, 0);
add (51049,"Cumberland County", "VA", "Virginia", "2", -5:1, 7851, 1387, 1, 0, 0);
add (11001,"District of Columbia", "DC", "District of Columbia", "2", -5:1, 523124, 1387, 1, 0, 0);
add (20021,"Cherokee County", "KS", "Kansas", "6", -6:1, 22552, 1148, 1, 0, 0);
add (54107,"Wood County", "WV", "West Virginia", "2", -5:1, 86768, 1387, 1, 0, 0);
add (48103,"Crane County", "TX", "Texas", "7", -6:1, 4510, 1148, 1, 0, 0);
add (21075,"Fulton County", "KY", "Kentucky", "4", -6:1, 7542, 1148, 1, 0, 0);
add (42013,"Blair County", "PA", "Pennsylvania", "1", -5:1, 130615, 1387, 1, 0, 0);
add (44005,"Newport County", "RI", "Rhode Island", "0", -5:1, 82868, 1387, 1, 0, 0);
add (19105,"Jones County", "IA", "Iowa", "5", -6:1, 20349, 1148, 1, 0, 0);
add (39135,"Preble County", "OH", "Ohio", "4", -5:1, 43226, 1387, 1, 0, 0);
add (26031,"Cheboygan County", "MI", "Michigan", "4", -5:1, 23738, 1387, 1, 0, 0);
add (18107,"Montgomery County", "IN", "Indiana", "4", -5:1, 36337, 1387, 1, 0, 0);
add (37005,"Alleghany County", "NC", "North Carolina", "2", -5:1, 9842, 1387, 1, 0, 0);
add (46129,"Walworth County", "SD", "South Dakota", "5", -7:1, 5582, 4647, 1, 0, 0);
add (37165,"Scotland County", "NC", "North Carolina", "2", -5:1, 35802, 1387, 1, 0, 0);
add (05149,"Yell County", "AR", "Arkansas", "7", -6:1, 19110, 1148, 1, 0, 0);
add (21085,"Grayson County", "KY", "Kentucky", "4", -6:1, 23763, 1148, 1, 0, 0);
add (55139,"Winnebago County", "WI", "Wisconnsin", "5", -6:1, 149818, 1148, 1, 0, 0);
add (26057,"Gratiot County", "MI", "Michigan", "4", -5:1, 40126, 1387, 1, 0, 0);
add (37141,"Pender County", "NC", "North Carolina", "2", -5:1, 39510, 1387, 1, 0, 0);
add (41037,"Lake County", "OR", "Oregon", "9", -8:1, 7152, 3240, 0, 1, 0);
add (48125,"Dickens County", "TX", "Texas", "7", -6:1, 2242, 1148, 1, 0, 0);
add (38009,"Bottineau County", "ND", "North Dakota", "5", -6:1, 7226, 1148, 1, 0, 0);
add (36067,"Onondaga County", "NY", "New York", "1", -5:1, 458301, 1387, 1, 0, 0);
add (08061,"Kiowa County", "CO", "Colorado", "8", -7:1, 1633, 4647, 1, 0, 0);
add (27067,"Kandiyohi County", "MN", "Minnesota", "5", -6:1, 41086, 1148, 1, 0, 0);
add (53001,"Adams County", "WA", "Washiington", "9", -8:1, 15324, 3240, 0, 1, 0);
add (35027,"Lincoln County", "NM", "New Mexico", "8", -7:1, 16400, 4647, 1, 0, 0);
add (20051,"Ellis County", "KS", "Kansas", "6", -6:1, 26309, 1148, 1, 0, 0);
add (31051,"Dixon County", "NE", "Nebraska", "6", -6:1, 6300, 1148, 1, 0, 0);
add (21131,"Leslie County", "KY", "Kentucky", "4", -5:1, 13582, 1387, 1, 0, 0);
add (13297,"Walton County", "GA", "Georgia", "3", -5:1, 54485, 1387, 1, 0, 0);
add (28099,"Neshoba County", "MS", "Missippi", "5", -6:1, 27653, 1148, 1, 0, 0);
add (31087,"Hitchcock County", "NE", "Nebraska", "6", -6:1, 3442, 1148, 1, 0, 0);
add (20085,"Jackson County", "KS", "Kansas", "6", -6:1, 12130, 1148, 1, 0, 0);
add (39131,"Pike County", "OH", "Ohio", "4", -5:1, 27775, 1387, 1, 0, 0);
add (27005,"Becker County", "MN", "Minnesota", "5", -6:1, 29381, 1148, 1, 0, 0);
add (20145,"Pawnee County", "KS", "Kansas", "6", -6:1, 7437, 1148, 1, 0, 0);
add (17189,"Washington County", "IL", "Illinois", "6", -6:1, 15367, 1148, 1, 0, 0);
add (01025,"Clarke County", "AL", "Alabama", "3", -6:1, 28499, 1148, 1, 0, 0);
add (37101,"Johnston County", "NC", "North Carolina", "2", -5:1, 106582, 1387, 1, 0, 0);
add (13171,"Lamar County", "GA", "Georgia", "3", -5:1, 14706, 1387, 1, 0, 0);
add (18131,"Pulaski County", "IN", "Indiana", "4", -5:1, 13257, 1387, 1, 0, 0);
add (17183,"Vermilion County", "IL", "Illinois", "6", -6:1, 84204, 1148, 1, 0, 0);
add (45067,"Marion County", "SC", "South Carolina", "2", -5:1, 34610, 1387, 1, 0, 0);
add (38021,"Dickey County", "ND", "North Dakota", "5", -6:1, 5644, 1148, 1, 0, 0);
add (30063,"Missoula County", "MT", "Montana", "6", -7:1, 88989, 4647, 1, 0, 0);
add (45091,"York County", "SC", "South Carolina", "2", -5:1, 154313, 1387, 1, 0, 0);
add (20169,"Saline County", "KS", "Kansas", "6", -6:1, 51617, 1148, 1, 0, 0);
add (22111,"Union Parish", "LA", "Louisiana", "7", -6:1, 21989, 1148, 1, 0, 0);
add (48291,"Liberty County", "TX", "Texas", "7", -6:1, 65078, 1148, 1, 0, 0);
add (47097,"Lauderdale County", "TN", "Tennesee", "3", -6:1, 24206, 1148, 1, 0, 0);
add (42019,"Butler County", "PA", "Pennsylvania", "1", -5:1, 170785, 1387, 1, 0, 0);
add (46041,"Dewey County", "SD", "South Dakota", "5", -6:1, 5821, 1148, 1, 0, 0);
add (39067,"Harrison County", "OH", "Ohio", "4", -5:1, 16097, 1387, 1, 0, 0);
add (48453,"Travis County", "TX", "Texas", "7", -6:1, 710626, 1148, 1, 0, 0);
add (48443,"Terrell County", "TX", "Texas", "7", -6:1, 1181, 1148, 1, 0, 0);
add (51540,"Charlottesville city", "VA", "Virginia", "2", -5:1, 38223, 1387, 1, 0, 0);
add (30041,"Hill County", "MT", "Montana", "6", -7:1, 17373, 4647, 1, 0, 0);
add (16045,"Gem County", "ID", "Idaho", "8", -7:1, 14816, 4647, 1, 0, 0);
add (22047,"Iberville Parish", "LA", "Louisiana", "7", -6:1, 31173, 1148, 1, 0, 0);
add (17095,"Knox County", "IL", "Illinois", "6", -6:1, 55526, 1148, 1, 0, 0);
add (26061,"Houghton County", "MI", "Michigan", "4", -5:1, 35719, 1387, 1, 0, 0);
add (37061,"Duplin County", "NC", "North Carolina", "2", -5:1, 42993, 1387, 1, 0, 0);
add (23025,"Somerset County", "ME", "Maine", "0", -5:1, 52380, 1387, 1, 0, 0);
add (20061,"Geary County", "KS", "Kansas", "6", -6:1, 25370, 1148, 1, 0, 0);
add (36105,"Sullivan County", "NY", "New York", "1", -5:1, 69111, 1387, 1, 0, 0);
add (21203,"Rockcastle County", "KY", "Kentucky", "4", -5:1, 15951, 1387, 1, 0, 0);
add (20183,"Smith County", "KS", "Kansas", "6", -6:1, 4588, 1148, 1, 0, 0);
add (42117,"Tioga County", "PA", "Pennsylvania", "1", -5:1, 41606, 1387, 1, 0, 0);
add (29137,"Monroe County", "MO", "Mosourri", "6", -6:1, 9021, 1148, 1, 0, 0);
add (42071,"Lancaster County", "PA", "Pennsylvania", "1", -5:1, 456414, 1387, 1, 0, 0);
add (48395,"Robertson County", "TX", "Texas", "7", -6:1, 15527, 1148, 1, 0, 0);
add (18089,"Lake County", "IN", "Indiana", "4", -5:1, 478323, 1387, 1, 0, 0);
add (12089,"Nassau County", "FL", "Florida", "3", -5:1, 55349, 1387, 1, 0, 0);
add (55069,"Lincoln County", "WI", "Wisconnsin", "5", -6:1, 29727, 1148, 1, 0, 0);
add (16077,"Power County", "ID", "Idaho", "8", -7:1, 8309, 4647, 1, 0, 0);
add (13149,"Heard County", "GA", "Georgia", "3", -5:1, 10082, 1387, 1, 0, 0);
add (25011,"Franklin County", "MA", "Massachusetts", "0", -5:1, 70597, 1387, 1, 0, 0);
add (31153,"Sarpy County", "NE", "Nebraska", "6", -7:1, 120785, 4647, 1, 0, 0);
add (42041,"Cumberland County", "PA", "Pennsylvania", "1", -5:1, 208634, 1387, 1, 0, 0);
add (28059,"Jackson County", "MS", "Missippi", "5", -6:1, 130910, 1148, 1, 0, 0);
add (37145,"Person County", "NC", "North Carolina", "2", -5:1, 33647, 1387, 1, 0, 0);
add (21169,"Metcalfe County", "KY", "Kentucky", "4", -5:1, 9561, 1387, 1, 0, 0);
add (51087,"Henrico County", "VA", "Virginia", "2", -5:1, 246052, 1387, 1, 0, 0);
add (20151,"Pratt County", "KS", "Kansas", "6", -6:1, 9700, 1148, 1, 0, 0);
add (06109,"Tuolumne County", "CA", "California", "9", -8:1, 53248, 3240, 0, 1, 0);
add (05111,"Poinsett County", "AR", "Arkansas", "7", -6:1, 24750, 1148, 1, 0, 0);
add (30019,"Daniels County", "MT", "Montana", "6", -7:1, 2001, 4647, 1, 0, 0);
add (39039,"Defiance County", "OH", "Ohio", "4", -5:1, 39824, 1387, 1, 0, 0);
add (02282,"Yakutat Borough", "AK", "Alaska", "9", -9:1, 799, 1174, 0, 0, 1);
add (22015,"Bossier Parish", "LA", "Louisiana", "7", -6:1, 93463, 1148, 1, 0, 0);
add (29105,"Laclede County", "MO", "Mosourri", "6", -6:1, 31029, 1148, 1, 0, 0);
add (12103,"Pinellas County", "FL", "Florida", "3", -5:1, 878231, 1387, 1, 0, 0);
add (36011,"Cayuga County", "NY", "New York", "1", -5:1, 81264, 1387, 1, 0, 0);
add (45073,"Oconee County", "SC", "South Carolina", "2", -5:1, 64059, 1387, 1, 0, 0);
add (55041,"Forest County", "WI", "Wisconnsin", "5", -6:1, 9645, 1148, 1, 0, 0);
add (29219,"Warren County", "MO", "Mosourri", "6", -6:1, 24600, 1148, 1, 0, 0);
add (18129,"Posey County", "IN", "Indiana", "4", -5:1, 26512, 1387, 1, 0, 0);
add (23003,"Aroostook County", "ME", "Maine", "0", -5:1, 76085, 1387, 1, 0, 0);
add (48409,"San Patricio County", "TX", "Texas", "7", -6:1, 71393, 1148, 1, 0, 0);
add (13057,"Cherokee County", "GA", "Georgia", "3", -5:1, 134498, 1387, 1, 0, 0);
add (01131,"Wilcox County", "AL", "Alabama", "3", -6:1, 13468, 1148, 1, 0, 0);
add (26013,"Baraga County", "MI", "Michigan", "4", -5:1, 8413, 1387, 1, 0, 0);
add (05139,"Union County", "AR", "Arkansas", "7", -6:1, 45304, 1148, 1, 0, 0);
add (28055,"Issaquena County", "MS", "Missippi", "5", -6:1, 1629, 1148, 1, 0, 0);
add (29205,"Shelby County", "MO", "Mosourri", "6", -6:1, 6802, 1148, 1, 0, 0);
add (31173,"Thurston County", "NE", "Nebraska", "6", -7:1, 7181, 4647, 1, 0, 0);
add (51740,"Portsmouth city", "VA", "Virginia", "2", -5:1, 98936, 1387, 1, 0, 0);
add (28145,"Union County", "MS", "Missippi", "5", -6:1, 23828, 1148, 1, 0, 0);
add (22105,"Tangipahoa Parish", "LA", "Louisiana", "7", -6:1, 96983, 1148, 1, 0, 0);
add (36029,"Erie County", "NY", "New York", "1", -5:1, 934471, 1387, 1, 0, 0);
add (29211,"Sullivan County", "MO", "Mosourri", "6", -6:1, 7040, 1148, 1, 0, 0);
add (31169,"Thayer County", "NE", "Nebraska", "6", -7:1, 6277, 4647, 1, 0, 0);
add (19175,"Union County", "IA", "Iowa", "5", -6:1, 12554, 1148, 1, 0, 0);
add (06101,"Sutter County", "CA", "California", "9", -8:1, 76976, 3240, 0, 1, 0);
add (46057,"Hamlin County", "SD", "South Dakota", "5", -7:1, 5335, 4647, 1, 0, 0);
add (47103,"Lincoln County", "TN", "Tennesee", "3", -6:1, 29761, 1148, 1, 0, 0);
add (30011,"Carter County", "MT", "Montana", "6", -7:1, 1537, 4647, 1, 0, 0);
add (20043,"Doniphan County", "KS", "Kansas", "6", -6:1, 7856, 1148, 1, 0, 0);
add (32003,"Clark County", "NV", "Nevada", "8", -8:1, 1162129, 3240, 0, 1, 0);
add (06075,"San Francisco County", "CA", "California", "9", -8:1, 745774, 3240, 0, 1, 0);
add (18045,"Fountain County", "IN", "Indiana", "4", -5:1, 18348, 1387, 1, 0, 0);
add (47127,"Moore County", "TN", "Tennesee", "3", -6:1, 5196, 1148, 1, 0, 0);
add (21113,"Jessamine County", "KY", "Kentucky", "4", -6:1, 36533, 1148, 1, 0, 0);
add (40139,"Texas County", "OK", "Oklahoma", "7", -6:1, 18640, 1148, 1, 0, 0);
add (37177,"Tyrrell County", "NC", "North Carolina", "2", -5:1, 3734, 1387, 1, 0, 0);
add (05069,"Jefferson County", "AR", "Arkansas", "7", -6:1, 81556, 1148, 1, 0, 0);
add (55093,"Pierce County", "WI", "Wisconnsin", "5", -6:1, 35606, 1148, 1, 0, 0);
add (53009,"Clallam County", "WA", "Washiington", "9", -8:1, 64169, 3240, 0, 1, 0);
add (51015,"Augusta County", "VA", "Virginia", "2", -5:1, 61775, 1387, 1, 0, 0);
add (06027,"Inyo County", "CA", "California", "9", -8:1, 18125, 3240, 0, 1, 0);
add (18165,"Vermillion County", "IN", "Indiana", "4", -5:1, 16908, 1387, 1, 0, 0);
add (19153,"Polk County", "IA", "Iowa", "5", -6:1, 359826, 1148, 1, 0, 0);
add (19009,"Audubon County", "IA", "Iowa", "5", -6:1, 6784, 1148, 1, 0, 0);
add (40051,"Grady County", "OK", "Oklahoma", "7", -6:1, 45934, 1148, 1, 0, 0);
add (21025,"Breathitt County", "KY", "Kentucky", "4", -6:1, 15686, 1148, 1, 0, 0);
add (17193,"White County", "IL", "Illinois", "6", -6:1, 15646, 1148, 1, 0, 0);
add (08057,"Jackson County", "CO", "Colorado", "8", -7:1, 1535, 4647, 1, 0, 0);
add (24015,"Cecil County", "MD", "Maryland", "2", -5:1, 82522, 1387, 1, 0, 0);
add (48293,"Limestone County", "TX", "Texas", "7", -6:1, 20930, 1148, 1, 0, 0);
add (19133,"Monona County", "IA", "Iowa", "5", -6:1, 10110, 1148, 1, 0, 0);
add (48381,"Randall County", "TX", "Texas", "7", -6:1, 99664, 1148, 1, 0, 0);
add (18021,"Clay County", "IN", "Indiana", "4", -5:1, 26637, 1387, 1, 0, 0);
add (16003,"Adams County", "ID", "Idaho", "8", -7:1, 3804, 4647, 1, 0, 0);
add (21209,"Scott County", "KY", "Kentucky", "4", -5:1, 30685, 1387, 1, 0, 0);
add (51019,"Bedford County", "VA", "Virginia", "2", -5:1, 55872, 1387, 1, 0, 0);
add (46063,"Harding County", "SD", "South Dakota", "5", -7:1, 1476, 4647, 1, 0, 0);
add (55131,"Washington County", "WI", "Wisconnsin", "5", -6:1, 113906, 1148, 1, 0, 0);
add (19067,"Floyd County", "IA", "Iowa", "5", -6:1, 16353, 1148, 1, 0, 0);
add (18179,"Wells County", "IN", "Indiana", "4", -5:1, 26842, 1387, 1, 0, 0);
add (38003,"Barnes County", "ND", "North Dakota", "5", -6:1, 11958, 1148, 1, 0, 0);
add (13195,"Madison County", "GA", "Georgia", "3", -5:1, 24312, 1387, 1, 0, 0);
add (05049,"Fulton County", "AR", "Arkansas", "7", -6:1, 10901, 1148, 1, 0, 0);
add (47117,"Marshall County", "TN", "Tennesee", "3", -6:1, 26302, 1148, 1, 0, 0);
add (47037,"Davidson County", "TN", "Tennesee", "3", -5:1, 533967, 1387, 1, 0, 0);
add (18067,"Howard County", "IN", "Indiana", "4", -5:1, 83452, 1387, 1, 0, 0);
add (06017,"El Dorado County", "CA", "California", "9", -8:1, 158502, 3240, 0, 1, 0);
add (05067,"Jackson County", "AR", "Arkansas", "7", -6:1, 17783, 1148, 1, 0, 0);
add (12117,"Seminole County", "FL", "Florida", "3", -5:1, 350859, 1387, 1, 0, 0);
add (16071,"Oneida County", "ID", "Idaho", "8", -7:1, 4051, 4647, 1, 0, 0);
add (26085,"Lake County", "MI", "Michigan", "4", -5:1, 10475, 1387, 1, 0, 0);
add (41059,"Umatilla County", "OR", "Oregon", "9", -8:1, 65495, 3240, 0, 1, 0);
add (13173,"Lanier County", "GA", "Georgia", "3", -5:1, 6986, 1387, 1, 0, 0);
add (27141,"Sherburne County", "MN", "Minnesota", "5", -6:1, 60391, 1148, 1, 0, 0);
add (13237,"Putnam County", "GA", "Georgia", "3", -5:1, 17559, 1387, 1, 0, 0);
add (17073,"Henry County", "IL", "Illinois", "6", -6:1, 51580, 1148, 1, 0, 0);
add (30075,"Powder River County", "MT", "Montana", "6", -7:1, 1826, 4647, 1, 0, 0);
add (31121,"Merrick County", "NE", "Nebraska", "6", -7:1, 8052, 4647, 1, 0, 0);
add (42017,"Bucks County", "PA", "Pennsylvania", "1", -5:1, 587942, 1387, 1, 0, 0);
add (40131,"Rogers County", "OK", "Oklahoma", "7", -6:1, 68128, 1148, 1, 0, 0);
add (19043,"Clayton County", "IA", "Iowa", "5", -6:1, 18722, 1148, 1, 0, 0);
add (53075,"Whitman County", "WA", "Washiington", "9", -8:1, 39487, 3240, 0, 1, 0);
add (05021,"Clay County", "AR", "Arkansas", "7", -6:1, 17223, 1148, 1, 0, 0);
add (31119,"Madison County", "NE", "Nebraska", "6", -7:1, 34585, 4647, 1, 0, 0);
add (16031,"Cassia County", "ID", "Idaho", "8", -7:1, 21359, 4647, 1, 0, 0);
add (12113,"Santa Rosa County", "FL", "Florida", "3", -5:1, 117322, 1387, 1, 0, 0);
add (48135,"Ector County", "TX", "Texas", "7", -6:1, 125729, 1148, 1, 0, 0);
add (48461,"Upton County", "TX", "Texas", "7", -6:1, 3749, 1148, 1, 0, 0);
add (36103,"Suffolk County", "NY", "New York", "1", -5:1, 1371269, 1387, 1, 0, 0);
add (26163,"Wayne County", "MI", "Michigan", "4", -5:1, 2118129, 1387, 1, 0, 0);
add (01071,"Jackson County", "AL", "Alabama", "3", -6:1, 51329, 1148, 1, 0, 0);
add (21121,"Knox County", "KY", "Kentucky", "4", -5:1, 31890, 1387, 1, 0, 0);
add (48467,"Van Zandt County", "TX", "Texas", "7", -6:1, 44037, 1148, 1, 0, 0);
add (34019,"Hunterdon County", "NJ", "New Jersey", "0", -5:1, 122428, 1387, 1, 0, 0);
add (27103,"Nicollet County", "MN", "Minnesota", "5", -6:1, 29600, 1148, 1, 0, 0);
add (29103,"Knox County", "MO", "Mosourri", "6", -6:1, 4355, 1148, 1, 0, 0);
add (41019,"Douglas County", "OR", "Oregon", "9", -8:1, 101837, 3240, 0, 1, 0);
add (41069,"Wheeler County", "OR", "Oregon", "9", -8:1, 1566, 3240, 0, 1, 0);
add (28025,"Clay County", "MS", "Missippi", "5", -6:1, 21637, 1148, 1, 0, 0);
add (21163,"Meade County", "KY", "Kentucky", "4", -5:1, 28809, 1387, 1, 0, 0);
add (21017,"Bourbon County", "KY", "Kentucky", "4", -6:1, 19368, 1148, 1, 0, 0);
add (19189,"Winnebago County", "IA", "Iowa", "5", -6:1, 11931, 1148, 1, 0, 0);
add (26071,"Iron County", "MI", "Michigan", "4", -5:1, 12883, 1387, 1, 0, 0);
add (13181,"Lincoln County", "GA", "Georgia", "3", -5:1, 8276, 1387, 1, 0, 0);
add (29003,"Andrew County", "MO", "Mosourri", "6", -6:1, 15562, 1148, 1, 0, 0);
add (13113,"Fayette County", "GA", "Georgia", "3", -5:1, 88609, 1387, 1, 0, 0);
add (45007,"Anderson County", "SC", "South Carolina", "2", -5:1, 160791, 1387, 1, 0, 0);
add (25003,"Berkshire County", "MA", "Massachusetts", "0", -5:1, 133038, 1387, 1, 0, 0);
add (12049,"Hardee County", "FL", "Florida", "3", -5:1, 21046, 1387, 1, 0, 0);
add (02290,"Yukon-Koyukuk Census Area", "AK", "Alaska", "9", -9:1, 5952, 1174, 0, 0, 1);
add (06031,"Kings County", "CA", "California", "9", -8:1, 118866, 3240, 0, 1, 0);
add (27107,"Norman County", "MN", "Minnesota", "5", -6:1, 7535, 1148, 1, 0, 0);
add (36065,"Oneida County", "NY", "New York", "1", -5:1, 230628, 1387, 1, 0, 0);
add (30047,"Lake County", "MT", "Montana", "6", -7:1, 25648, 4647, 1, 0, 0);
add (02122,"Kenai Peninsula Borough", "AK", "Alaska", "9", -9:1, 48008, 1174, 0, 0, 1);
add (48289,"Leon County", "TX", "Texas", "7", -6:1, 14489, 1148, 1, 0, 0);
add (40001,"Adair County", "OK", "Oklahoma", "7", -6:1, 20349, 1148, 1, 0, 0);
add (21221,"Trigg County", "KY", "Kentucky", "4", -5:1, 12399, 1387, 1, 0, 0);
add (45061,"Lee County", "SC", "South Carolina", "2", -5:1, 20399, 1387, 1, 0, 0);
add (22029,"Concordia Parish", "LA", "Louisiana", "7", -6:1, 20749, 1148, 1, 0, 0);
add (12073,"Leon County", "FL", "Florida", "3", -5:1, 216978, 1387, 1, 0, 0);
add (26021,"Berrien County", "MI", "Michigan", "4", -5:1, 160245, 1387, 1, 0, 0);
add (20103,"Leavenworth County", "KS", "Kansas", "6", -6:1, 71299, 1148, 1, 0, 0);
add (22109,"Terrebonne Parish", "LA", "Louisiana", "7", -6:1, 104534, 1148, 1, 0, 0);
add (19097,"Jackson County", "IA", "Iowa", "5", -6:1, 20078, 1148, 1, 0, 0);
add (49045,"Tooele County", "UT", "Utah", "8", -7:1, 33351, 4647, 1, 0, 0);
add (48387,"Red River County", "TX", "Texas", "7", -6:1, 13731, 1148, 1, 0, 0);
add (13103,"Effingham County", "GA", "Georgia", "3", -5:1, 36483, 1387, 1, 0, 0);
add (13125,"Glascock County", "GA", "Georgia", "3", -5:1, 2512, 1387, 1, 0, 0);
add (18075,"Jay County", "IN", "Indiana", "4", -5:1, 21729, 1387, 1, 0, 0);
add (45025,"Chesterfield County", "SC", "South Carolina", "2", -5:1, 41080, 1387, 1, 0, 0);
add (19145,"Page County", "IA", "Iowa", "5", -6:1, 17269, 1148, 1, 0, 0);
add (21053,"Clinton County", "KY", "Kentucky", "4", -6:1, 9346, 1148, 1, 0, 0);
add (29065,"Dent County", "MO", "Mosourri", "6", -6:1, 14103, 1148, 1, 0, 0);
add (05141,"Van Buren County", "AR", "Arkansas", "7", -6:1, 15550, 1148, 1, 0, 0);
add (02188,"Northwest Arctic Borough", "AK", "Alaska", "9", -9:1, 6758, 1174, 0, 0, 1);
add (01057,"Fayette County", "AL", "Alabama", "3", -6:1, 18133, 1148, 1, 0, 0);
add (54091,"Taylor County", "WV", "West Virginia", "2", -5:1, 15326, 1387, 1, 0, 0);
add (13037,"Calhoun County", "GA", "Georgia", "3", -5:1, 5053, 1387, 1, 0, 0);
add (39085,"Lake County", "OH", "Ohio", "4", -5:1, 223779, 1387, 1, 0, 0);
add (19077,"Guthrie County", "IA", "Iowa", "5", -6:1, 11571, 1148, 1, 0, 0);
add (17041,"Douglas County", "IL", "Illinois", "6", -6:1, 19915, 1148, 1, 0, 0);
add (27041,"Douglas County", "MN", "Minnesota", "5", -6:1, 31045, 1148, 1, 0, 0);
add (31131,"Otoe County", "NE", "Nebraska", "6", -7:1, 14787, 4647, 1, 0, 0);
add (47045,"Dyer County", "TN", "Tennesee", "3", -5:1, 36782, 1387, 1, 0, 0);
add (28007,"Attala County", "MS", "Missippi", "5", -6:1, 18404, 1148, 1, 0, 0);
add (39083,"Knox County", "OH", "Ohio", "4", -5:1, 53309, 1387, 1, 0, 0);
add (29225,"Webster County", "MO", "Mosourri", "6", -6:1, 29108, 1148, 1, 0, 0);
add (54079,"Putnam County", "WV", "West Virginia", "2", -5:1, 51164, 1387, 1, 0, 0);
add (01119,"Sumter County", "AL", "Alabama", "3", -6:1, 15766, 1148, 1, 0, 0);
add (24041,"Talbot County", "MD", "Maryland", "2", -5:1, 33065, 1387, 1, 0, 0);
add (54019,"Fayette County", "WV", "West Virginia", "2", -5:1, 47930, 1387, 1, 0, 0);
add (54069,"Ohio County", "WV", "West Virginia", "2", -5:1, 48287, 1387, 1, 0, 0);
add (12029,"Dixie County", "FL", "Florida", "3", -5:1, 12959, 1387, 1, 0, 0);
add (13315,"Wilcox County", "GA", "Georgia", "3", -5:1, 7365, 1387, 1, 0, 0);
add (13193,"Macon County", "GA", "Georgia", "3", -5:1, 13244, 1387, 1, 0, 0);
add (26135,"Oscoda County", "MI", "Michigan", "4", -5:1, 8882, 1387, 1, 0, 0);
add (50021,"Rutland County", "VT", "Vermont", "0", -5:1, 62524, 1387, 1, 0, 0);
add (26137,"Otsego County", "MI", "Michigan", "4", -5:1, 22129, 1387, 1, 0, 0);
add (38051,"McIntosh County", "ND", "North Dakota", "5", -6:1, 3442, 1148, 1, 0, 0);
add (27165,"Watonwan County", "MN", "Minnesota", "5", -6:1, 11470, 1148, 1, 0, 0);
add (47137,"Pickett County", "TN", "Tennesee", "3", -6:1, 4629, 1148, 1, 0, 0);
add (18121,"Parke County", "IN", "Indiana", "4", -5:1, 16720, 1387, 1, 0, 0);
add (49001,"Beaver County", "UT", "Utah", "8", -7:1, 5896, 4647, 1, 0, 0);
add (48417,"Shackelford County", "TX", "Texas", "7", -6:1, 3303, 1148, 1, 0, 0);
add (36073,"Orleans County", "NY", "New York", "1", -5:1, 44518, 1387, 1, 0, 0);
add (01083,"Limestone County", "AL", "Alabama", "3", -6:1, 62241, 1148, 1, 0, 0);
add (47083,"Houston County", "TN", "Tennesee", "3", -5:1, 7853, 1387, 1, 0, 0);
add (37191,"Wayne County", "NC", "North Carolina", "2", -5:1, 112227, 1387, 1, 0, 0);
add (29069,"Dunklin County", "MO", "Mosourri", "6", -6:1, 32700, 1148, 1, 0, 0);
add (22051,"Jefferson Parish", "LA", "Louisiana", "7", -6:1, 450933, 1148, 1, 0, 0);
add (08019,"Clear Creek County", "CO", "Colorado", "8", -7:1, 9001, 4647, 1, 0, 0);
add (26127,"Oceana County", "MI", "Michigan", "4", -5:1, 24833, 1387, 1, 0, 0);
add (46135,"Yankton County", "SD", "South Dakota", "5", -6:1, 21051, 1148, 1, 0, 0);
add (20023,"Cheyenne County", "KS", "Kansas", "6", -6:1, 3174, 1148, 1, 0, 0);
add (32029,"Storey County", "NV", "Nevada", "8", -8:1, 3053, 3240, 0, 1, 0);
add (36115,"Washington County", "NY", "New York", "1", -5:1, 60481, 1387, 1, 0, 0);
add (05125,"Saline County", "AR", "Arkansas", "7", -6:1, 77412, 1148, 1, 0, 0);
add (51021,"Bland County", "VA", "Virginia", "2", -5:1, 6748, 1387, 1, 0, 0);
add (48475,"Ward County", "TX", "Texas", "7", -6:1, 11801, 1148, 1, 0, 0);
add (28015,"Carroll County", "MS", "Missippi", "5", -6:1, 9995, 1148, 1, 0, 0);
add (48439,"Tarrant County", "TX", "Texas", "7", -6:1, 1355273, 1148, 1, 0, 0);
add (25027,"Worcester County", "MA", "Massachusetts", "0", -5:1, 731881, 1387, 1, 0, 0);
add (13285,"Troup County", "GA", "Georgia", "3", -5:1, 58783, 1387, 1, 0, 0);
add (17177,"Stephenson County", "IL", "Illinois", "6", -6:1, 48951, 1148, 1, 0, 0);
add (36083,"Rensselaer County", "NY", "New York", "1", -5:1, 152689, 1387, 1, 0, 0);
add (29127,"Marion County", "MO", "Mosourri", "6", -6:1, 27771, 1148, 1, 0, 0);
add (05013,"Calhoun County", "AR", "Arkansas", "7", -6:1, 5729, 1148, 1, 0, 0);
add (29163,"Pike County", "MO", "Mosourri", "6", -6:1, 16347, 1148, 1, 0, 0);
add (46035,"Davison County", "SD", "South Dakota", "5", -6:1, 18006, 1148, 1, 0, 0);
add (48311,"McMullen County", "TX", "Texas", "7", -6:1, 790, 1148, 1, 0, 0);
add (28151,"Washington County", "MS", "Missippi", "5", -6:1, 65264, 1148, 1, 0, 0);
add (20063,"Gove County", "KS", "Kansas", "6", -6:1, 3054, 1148, 1, 0, 0);
add (55135,"Waupaca County", "WI", "Wisconnsin", "5", -6:1, 50545, 1148, 1, 0, 0);
add (05087,"Madison County", "AR", "Arkansas", "7", -6:1, 13224, 1148, 1, 0, 0);
add (48363,"Palo Pinto County", "TX", "Texas", "7", -6:1, 25756, 1148, 1, 0, 0);
add (25017,"Middlesex County", "MA", "Massachusetts", "0", -5:1, 1424116, 1387, 1, 0, 0);
add (51131,"Northampton County", "VA", "Virginia", "2", -5:1, 12709, 1387, 1, 0, 0);
add (13239,"Quitman County", "GA", "Georgia", "3", -5:1, 2486, 1387, 1, 0, 0);
add (38101,"Ward County", "ND", "North Dakota", "5", -6:1, 58678, 1148, 1, 0, 0);
add (47151,"Scott County", "TN", "Tennesee", "3", -6:1, 20044, 1148, 1, 0, 0);
add (08105,"Rio Grande County", "CO", "Colorado", "8", -7:1, 11453, 4647, 1, 0, 0);
add (21223,"Trimble County", "KY", "Kentucky", "4", -5:1, 7621, 1387, 1, 0, 0);
add (48393,"Roberts County", "TX", "Texas", "7", -6:1, 939, 1148, 1, 0, 0);
add (41063,"Wallowa County", "OR", "Oregon", "9", -8:1, 7368, 3240, 0, 1, 0);
add (27123,"Ramsey County", "MN", "Minnesota", "5", -6:1, 485636, 1148, 1, 0, 0);
add (21031,"Butler County", "KY", "Kentucky", "4", -6:1, 11926, 1148, 1, 0, 0);
add (29171,"Putnam County", "MO", "Mosourri", "6", -6:1, 4912, 1148, 1, 0, 0);
add (34011,"Cumberland County", "NJ", "New Jersey", "0", -5:1, 140341, 1387, 1, 0, 0);
add (18125,"Pike County", "IN", "Indiana", "4", -5:1, 12882, 1387, 1, 0, 0);
add (13279,"Toombs County", "GA", "Georgia", "3", -5:1, 25828, 1387, 1, 0, 0);
add (01021,"Chilton County", "AL", "Alabama", "3", -6:1, 36918, 1148, 1, 0, 0);
add (16041,"Franklin County", "ID", "Idaho", "8", -7:1, 11106, 4647, 1, 0, 0);
add (49033,"Rich County", "UT", "Utah", "8", -7:1, 1834, 4647, 1, 0, 0);
add (46125,"Turner County", "SD", "South Dakota", "5", -7:1, 8631, 4647, 1, 0, 0);
add (04019,"Pima County", "AZ", "Arizona", "8", -7:1, 790755, 4647, 1, 0, 0);
add (36091,"Saratoga County", "NY", "New York", "1", -5:1, 197606, 1387, 1, 0, 0);
add (46081,"Lawrence County", "SD", "South Dakota", "5", -7:1, 22509, 4647, 1, 0, 0);
add (10003,"New Castle County", "DE", "Delaware", "1", -5:1, 482807, 1387, 1, 0, 0);
add (05061,"Howard County", "AR", "Arkansas", "7", -6:1, 13724, 1148, 1, 0, 0);
add (21087,"Green County", "KY", "Kentucky", "4", -6:1, 10650, 1148, 1, 0, 0);
add (13071,"Colquitt County", "GA", "Georgia", "3", -5:1, 40156, 1387, 1, 0, 0);
add (12033,"Escambia County", "FL", "Florida", "3", -5:1, 282303, 1387, 1, 0, 0);
add (51139,"Page County", "VA", "Virginia", "2", -5:1, 22989, 1387, 1, 0, 0);
add (17167,"Sangamon County", "IL", "Illinois", "6", -6:1, 191378, 1148, 1, 0, 0);
add (17133,"Monroe County", "IL", "Illinois", "6", -6:1, 26586, 1148, 1, 0, 0);
add (06061,"Placer County", "CA", "California", "9", -8:1, 229259, 3240, 0, 1, 0);
add (30071,"Phillips County", "MT", "Montana", "6", -7:1, 4821, 4647, 1, 0, 0);
add (04007,"Gila County", "AZ", "Arizona", "8", -7:1, 48974, 4647, 1, 0, 0);
add (01133,"Winston County", "AL", "Alabama", "3", -6:1, 24157, 1148, 1, 0, 0);
add (13073,"Columbia County", "GA", "Georgia", "3", -5:1, 91118, 1387, 1, 0, 0);
add (27109,"Olmsted County", "MN", "Minnesota", "5", -6:1, 116702, 1148, 1, 0, 0);
add (40083,"Logan County", "OK", "Oklahoma", "7", -6:1, 30970, 1148, 1, 0, 0);
add (55031,"Douglas County", "WI", "Wisconnsin", "5", -6:1, 43033, 1148, 1, 0, 0);
add (34025,"Monmouth County", "NJ", "New Jersey", "0", -5:1, 603434, 1387, 1, 0, 0);
add (17091,"Kankakee County", "IL", "Illinois", "6", -6:1, 102107, 1148, 1, 0, 0);
add (48347,"Nacogdoches County", "TX", "Texas", "7", -6:1, 56220, 1148, 1, 0, 0);
add (23005,"Cumberland County", "ME", "Maine", "0", -5:1, 253582, 1387, 1, 0, 0);
add (18047,"Franklin County", "IN", "Indiana", "4", -5:1, 21808, 1387, 1, 0, 0);
add (01075,"Lamar County", "AL", "Alabama", "3", -6:1, 15731, 1148, 1, 0, 0);
add (22063,"Livingston Parish", "LA", "Louisiana", "7", -6:1, 88104, 1148, 1, 0, 0);
add (18177,"Wayne County", "IN", "Indiana", "4", -5:1, 71313, 1387, 1, 0, 0);
add (28157,"Wilkinson County", "MS", "Missippi", "5", -6:1, 9174, 1148, 1, 0, 0);
add (48413,"Schleicher County", "TX", "Texas", "7", -6:1, 2984, 1148, 1, 0, 0);
add (22123,"West Carroll Parish", "LA", "Louisiana", "7", -6:1, 12213, 1148, 1, 0, 0);
add (08091,"Ouray County", "CO", "Colorado", "8", -7:1, 3313, 4647, 1, 0, 0);
add (51830,"Williamsburg city", "VA", "Virginia", "2", -5:1, 11971, 1387, 1, 0, 0);
add (51153,"Prince William County", "VA", "Virginia", "2", -5:1, 259827, 1387, 1, 0, 0);
add (47183,"Weakley County", "TN", "Tennesee", "3", -6:1, 32942, 1148, 1, 0, 0);
add (48207,"Haskell County", "TX", "Texas", "7", -6:1, 6158, 1148, 1, 0, 0);
add (38031,"Foster County", "ND", "North Dakota", "5", -6:1, 3802, 1148, 1, 0, 0);
add (37073,"Gates County", "NC", "North Carolina", "2", -5:1, 10070, 1387, 1, 0, 0);
add (01113,"Russell County", "AL", "Alabama", "3", -6:1, 50387, 1148, 1, 0, 0);
add (27037,"Dakota County", "MN", "Minnesota", "5", -6:1, 342528, 1148, 1, 0, 0);
add (37077,"Granville County", "NC", "North Carolina", "2", -5:1, 42908, 1387, 1, 0, 0);
add (47023,"Chester County", "TN", "Tennesee", "3", -5:1, 14700, 1387, 1, 0, 0);
add (28041,"Greene County", "MS", "Missippi", "5", -6:1, 11766, 1148, 1, 0, 0);
add (17015,"Carroll County", "IL", "Illinois", "6", -6:1, 16941, 1148, 1, 0, 0);
add (39133,"Portage County", "OH", "Ohio", "4", -5:1, 151222, 1387, 1, 0, 0);
add (06047,"Merced County", "CA", "California", "9", -8:1, 197730, 3240, 0, 1, 0);
add (20073,"Greenwood County", "KS", "Kansas", "6", -6:1, 8139, 1148, 1, 0, 0);
add (17057,"Fulton County", "IL", "Illinois", "6", -6:1, 38746, 1148, 1, 0, 0);
add (36095,"Schoharie County", "NY", "New York", "1", -5:1, 32438, 1387, 1, 0, 0);
add (40013,"Bryan County", "OK", "Oklahoma", "7", -6:1, 34690, 1148, 1, 0, 0);
add (17065,"Hamilton County", "IL", "Illinois", "6", -6:1, 8611, 1148, 1, 0, 0);
add (28095,"Monroe County", "MS", "Missippi", "5", -6:1, 38263, 1148, 1, 0, 0);
add (48083,"Coleman County", "TX", "Texas", "7", -6:1, 9541, 1148, 1, 0, 0);
add (26039,"Crawford County", "MI", "Michigan", "4", -5:1, 14150, 1387, 1, 0, 0);
add (27089,"Marshall County", "MN", "Minnesota", "5", -6:1, 10313, 1148, 1, 0, 0);
add (51033,"Caroline County", "VA", "Virginia", "2", -5:1, 22053, 1387, 1, 0, 0);
add (21239,"Woodford County", "KY", "Kentucky", "4", -6:1, 22830, 1148, 1, 0, 0);
add (29123,"Madison County", "MO", "Mosourri", "6", -6:1, 11481, 1148, 1, 0, 0);
add (48141,"El Paso County", "TX", "Texas", "7", -6:1, 703127, 1148, 1, 0, 0);
add (29115,"Linn County", "MO", "Mosourri", "6", -6:1, 13808, 1148, 1, 0, 0);
add (13191,"McIntosh County", "GA", "Georgia", "3", -5:1, 10018, 1387, 1, 0, 0);
add (21091,"Hancock County", "KY", "Kentucky", "4", -6:1, 8941, 1148, 1, 0, 0);
add (28051,"Holmes County", "MS", "Missippi", "5", -6:1, 21522, 1148, 1, 0, 0);
add (19179,"Wapello County", "IA", "Iowa", "5", -6:1, 35440, 1148, 1, 0, 0);
add (37153,"Richmond County", "NC", "North Carolina", "2", -5:1, 46221, 1387, 1, 0, 0);
add (37127,"Nash County", "NC", "North Carolina", "2", -5:1, 90968, 1387, 1, 0, 0);
add (19027,"Carroll County", "IA", "Iowa", "5", -6:1, 21706, 1148, 1, 0, 0);
add (21211,"Shelby County", "KY", "Kentucky", "4", -5:1, 29583, 1387, 1, 0, 0);
add (22009,"Avoyelles Parish", "LA", "Louisiana", "7", -6:1, 40846, 1148, 1, 0, 0);
add (48107,"Crosby County", "TX", "Texas", "7", -6:1, 7215, 1148, 1, 0, 0);
add (40023,"Choctaw County", "OK", "Oklahoma", "7", -6:1, 15077, 1148, 1, 0, 0);
add (28153,"Wayne County", "MS", "Missippi", "5", -6:1, 20368, 1148, 1, 0, 0);
add (16043,"Fremont County", "ID", "Idaho", "8", -7:1, 11897, 4647, 1, 0, 0);
add (39045,"Fairfield County", "OH", "Ohio", "4", -5:1, 123998, 1387, 1, 0, 0);
add (30037,"Golden Valley County", "MT", "Montana", "6", -7:1, 1041, 4647, 1, 0, 0);
add (36023,"Cortland County", "NY", "New York", "1", -5:1, 48033, 1387, 1, 0, 0);
add (56013,"Fremont County", "WY", "Wyoming", "8", -7:1, 36044, 4647, 1, 0, 0);
add (29179,"Reynolds County", "MO", "Mosourri", "6", -6:1, 6624, 1148, 1, 0, 0);
add (13197,"Marion County", "GA", "Georgia", "3", -5:1, 6712, 1387, 1, 0, 0);
add (13031,"Bulloch County", "GA", "Georgia", "3", -5:1, 50614, 1387, 1, 0, 0);
add (49011,"Davis County", "UT", "Utah", "8", -7:1, 233013, 4647, 1, 0, 0);
add (16085,"Valley County", "ID", "Idaho", "8", -7:1, 8005, 4647, 1, 0, 0);
add (15001,"Hawaii County", "HI", "Hawaii", "9", -10:1, 143135, 6750, 0, 0, 1);
add (01089,"Madison County", "AL", "Alabama", "3", -6:1, 278187, 1148, 1, 0, 0);
add (27061,"Itasca County", "MN", "Minnesota", "5", -6:1, 43857, 1148, 1, 0, 0);
add (16073,"Owyhee County", "ID", "Idaho", "8", -7:1, 10277, 4647, 1, 0, 0);
add (51167,"Russell County", "VA", "Virginia", "2", -5:1, 29049, 1387, 1, 0, 0);
add (36069,"Ontario County", "NY", "New York", "1", -5:1, 99662, 1387, 1, 0, 0);
add (13005,"Bacon County", "GA", "Georgia", "3", -5:1, 10375, 1387, 1, 0, 0);
add (13069,"Coffee County", "GA", "Georgia", "3", -5:1, 34298, 1387, 1, 0, 0);
add (12045,"Gulf County", "FL", "Florida", "3", -5:1, 13476, 1387, 1, 0, 0);
add (19019,"Buchanan County", "IA", "Iowa", "5", -6:1, 21190, 1148, 1, 0, 0);
add (47101,"Lewis County", "TN", "Tennesee", "3", -6:1, 10868, 1148, 1, 0, 0);
add (08069,"Larimer County", "CO", "Colorado", "8", -7:1, 231221, 4647, 1, 0, 0);
add (29157,"Perry County", "MO", "Mosourri", "6", -6:1, 17410, 1148, 1, 0, 0);
add (51690,"Martinsville city", "VA", "Virginia", "2", -5:1, 15668, 1387, 1, 0, 0);
add (48321,"Matagorda County", "TX", "Texas", "7", -6:1, 37965, 1148, 1, 0, 0);
add (13027,"Brooks County", "GA", "Georgia", "3", -5:1, 16000, 1387, 1, 0, 0);
add (39095,"Lucas County", "OH", "Ohio", "4", -5:1, 448542, 1387, 1, 0, 0);
add (31091,"Hooker County", "NE", "Nebraska", "6", -7:1, 702, 4647, 1, 0, 0);
add (56043,"Washakie County", "WY", "Wyoming", "8", -7:1, 8669, 4647, 1, 0, 0);
add (12133,"Washington County", "FL", "Florida", "3", -5:1, 20292, 1387, 1, 0, 0);
add (16047,"Gooding County", "ID", "Idaho", "8", -7:1, 13626, 4647, 1, 0, 0);
add (41021,"Gilliam County", "OR", "Oregon", "9", -8:1, 2023, 3240, 0, 1, 0);
add (46029,"Codington County", "SD", "South Dakota", "5", -6:1, 25456, 1148, 1, 0, 0);
add (40145,"Wagoner County", "OK", "Oklahoma", "7", -6:1, 55259, 1148, 1, 0, 0);
add (55113,"Sawyer County", "WI", "Wisconnsin", "5", -6:1, 16110, 1148, 1, 0, 0);
add (41049,"Morrow County", "OR", "Oregon", "9", -8:1, 9985, 3240, 0, 1, 0);
add (48233,"Hutchinson County", "TX", "Texas", "7", -6:1, 24077, 1148, 1, 0, 0);
add (21179,"Nelson County", "KY", "Kentucky", "4", -5:1, 35884, 1387, 1, 0, 0);
add (30067,"Park County", "MT", "Montana", "6", -7:1, 15829, 4647, 1, 0, 0);
add (20095,"Kingman County", "KS", "Kansas", "6", -6:1, 8543, 1148, 1, 0, 0);
add (21039,"Carlisle County", "KY", "Kentucky", "4", -6:1, 5320, 1148, 1, 0, 0);
add (51660,"Harrisonburg city", "VA", "Virginia", "2", -5:1, 33434, 1387, 1, 0, 0);
add (46043,"Douglas County", "SD", "South Dakota", "5", -6:1, 3553, 1148, 1, 0, 0);
add (21145,"McCracken County", "KY", "Kentucky", "4", -5:1, 64460, 1387, 1, 0, 0);
add (17161,"Rock Island County", "IL", "Illinois", "6", -6:1, 147642, 1148, 1, 0, 0);
add (48419,"Shelby County", "TX", "Texas", "7", -6:1, 22748, 1148, 1, 0, 0);
add (28019,"Choctaw County", "MS", "Missippi", "5", -6:1, 9385, 1148, 1, 0, 0);
add (28137,"Tate County", "MS", "Missippi", "5", -6:1, 23923, 1148, 1, 0, 0);
add (45077,"Pickens County", "SC", "South Carolina", "2", -5:1, 107087, 1387, 1, 0, 0);
add (37129,"New Hanover County", "NC", "North Carolina", "2", -5:1, 149832, 1387, 1, 0, 0);
add (13007,"Baker County", "GA", "Georgia", "3", -5:1, 3673, 1387, 1, 0, 0);
add (29129,"Mercer County", "MO", "Mosourri", "6", -6:1, 4003, 1148, 1, 0, 0);
add (19115,"Louisa County", "IA", "Iowa", "5", -6:1, 11938, 1148, 1, 0, 0);
add (46071,"Jackson County", "SD", "South Dakota", "5", -7:1, 2904, 4647, 1, 0, 0);
add (31089,"Holt County", "NE", "Nebraska", "6", -7:1, 12042, 4647, 1, 0, 0);
add (51109,"Louisa County", "VA", "Virginia", "2", -5:1, 24675, 1387, 1, 0, 0);
add (49021,"Iron County", "UT", "Utah", "8", -7:1, 28659, 4647, 1, 0, 0);
add (21107,"Hopkins County", "KY", "Kentucky", "4", -6:1, 46364, 1148, 1, 0, 0);
add (22075,"Plaquemines Parish", "LA", "Louisiana", "7", -6:1, 26293, 1148, 1, 0, 0);
add (19103,"Johnson County", "IA", "Iowa", "5", -6:1, 102724, 1148, 1, 0, 0);
add (39023,"Clark County", "OH", "Ohio", "4", -5:1, 145341, 1387, 1, 0, 0);
add (19111,"Lee County", "IA", "Iowa", "5", -6:1, 38471, 1148, 1, 0, 0);
add (06059,"Orange County", "CA", "California", "9", -8:1, 2721701, 3240, 0, 1, 0);
add (22003,"Allen Parish", "LA", "Louisiana", "7", -6:1, 23888, 1148, 1, 0, 0);
add (19045,"Clinton County", "IA", "Iowa", "5", -6:1, 49897, 1148, 1, 0, 0);
add (12043,"Glades County", "FL", "Florida", "3", -5:1, 8492, 1387, 1, 0, 0);
add (51155,"Pulaski County", "VA", "Virginia", "2", -5:1, 34539, 1387, 1, 0, 0);
add (56001,"Albany County", "WY", "Wyoming", "8", -7:1, 29185, 4647, 1, 0, 0);
add (29227,"Worth County", "MO", "Mosourri", "6", -6:1, 2295, 1148, 1, 0, 0);
add (08073,"Lincoln County", "CO", "Colorado", "8", -7:1, 5729, 4647, 1, 0, 0);
add (51005,"Alleghany County", "VA", "Virginia", "2", -5:1, 12146, 1387, 1, 0, 0);
add (36049,"Lewis County", "NY", "New York", "1", -5:1, 27494, 1387, 1, 0, 0);
add (48221,"Hood County", "TX", "Texas", "7", -6:1, 37194, 1148, 1, 0, 0);
add (51125,"Nelson County", "VA", "Virginia", "2", -5:1, 13917, 1387, 1, 0, 0);
add (12007,"Bradford County", "FL", "Florida", "3", -5:1, 24777, 1387, 1, 0, 0);
add (31099,"Kearney County", "NE", "Nebraska", "6", -7:1, 6853, 4647, 1, 0, 0);
add (48421,"Sherman County", "TX", "Texas", "7", -6:1, 2864, 1148, 1, 0, 0);
add (48085,"Collin County", "TX", "Texas", "7", -6:1, 428803, 1148, 1, 0, 0);
add (08083,"Montezuma County", "CO", "Colorado", "8", -7:1, 22465, 4647, 1, 0, 0);
add (51165,"Rockingham County", "VA", "Virginia", "2", -5:1, 63214, 1387, 1, 0, 0);
add (47003,"Bedford County", "TN", "Tennesee", "3", -5:1, 34533, 1387, 1, 0, 0);
add (12125,"Union County", "FL", "Florida", "3", -5:1, 12423, 1387, 1, 0, 0);
add (51590,"Danville city", "VA", "Virginia", "2", -5:1, 50868, 1387, 1, 0, 0);
add (21103,"Henry County", "KY", "Kentucky", "4", -6:1, 14765, 1148, 1, 0, 0);
add (37067,"Forsyth County", "NC", "North Carolina", "2", -5:1, 287701, 1387, 1, 0, 0);
add (06085,"Santa Clara County", "CA", "California", "9", -8:1, 1641215, 3240, 0, 1, 0);
add (48423,"Smith County", "TX", "Texas", "7", -6:1, 168783, 1148, 1, 0, 0);
add (49003,"Box Elder County", "UT", "Utah", "8", -7:1, 41949, 4647, 1, 0, 0);
add (48407,"San Jacinto County", "TX", "Texas", "7", -6:1, 21768, 1148, 1, 0, 0);
add (42055,"Franklin County", "PA", "Pennsylvania", "1", -5:1, 128002, 1387, 1, 0, 0);
add (47099,"Lawrence County", "TN", "Tennesee", "3", -6:1, 39358, 1148, 1, 0, 0);
add (41067,"Washington County", "OR", "Oregon", "9", -8:1, 399697, 3240, 0, 1, 0);
add (40137,"Stephens County", "OK", "Oklahoma", "7", -6:1, 43410, 1148, 1, 0, 0);
add (17029,"Coles County", "IL", "Illinois", "6", -6:1, 51103, 1148, 1, 0, 0);
add (24009,"Calvert County", "MD", "Maryland", "2", -5:1, 71877, 1387, 1, 0, 0);
add (26121,"Muskegon County", "MI", "Michigan", "4", -5:1, 166748, 1387, 1, 0, 0);
add (50001,"Addison County", "VT", "Vermont", "0", -5:1, 35168, 1387, 1, 0, 0);
add (55141,"Wood County", "WI", "Wisconnsin", "5", -6:1, 76036, 1148, 1, 0, 0);
add (49055,"Wayne County", "UT", "Utah", "8", -7:1, 2379, 4647, 1, 0, 0);
add (17067,"Hancock County", "IL", "Illinois", "6", -6:1, 21088, 1148, 1, 0, 0);
add (46011,"Brookings County", "SD", "South Dakota", "5", -6:1, 25989, 1148, 1, 0, 0);
add (37183,"Wake County", "NC", "North Carolina", "2", -5:1, 570615, 1387, 1, 0, 0);
add (37035,"Catawba County", "NC", "North Carolina", "2", -5:1, 132545, 1387, 1, 0, 0);
add (37131,"Northampton County", "NC", "North Carolina", "2", -5:1, 21184, 1387, 1, 0, 0);
add (55137,"Waushara County", "WI", "Wisconnsin", "5", -6:1, 21609, 1148, 1, 0, 0);
add (05083,"Logan County", "AR", "Arkansas", "7", -6:1, 21173, 1148, 1, 0, 0);
add (34035,"Somerset County", "NJ", "New Jersey", "0", -5:1, 282900, 1387, 1, 0, 0);
add (06115,"Yuba County", "CA", "California", "9", -8:1, 60067, 3240, 0, 1, 0);
add (05031,"Craighead County", "AR", "Arkansas", "7", -6:1, 77500, 1148, 1, 0, 0);
add (48033,"Borden County", "TX", "Texas", "7", -6:1, 758, 1148, 1, 0, 0);
add (51069,"Frederick County", "VA", "Virginia", "2", -5:1, 55229, 1387, 1, 0, 0);
add (06069,"San Benito County", "CA", "California", "9", -8:1, 48774, 3240, 0, 1, 0);
add (55033,"Dunn County", "WI", "Wisconnsin", "5", -6:1, 38977, 1148, 1, 0, 0);
add (19183,"Washington County", "IA", "Iowa", "5", -6:1, 20967, 1148, 1, 0, 0);
add (49027,"Millard County", "UT", "Utah", "8", -7:1, 12249, 4647, 1, 0, 0);
add (54095,"Tyler County", "WV", "West Virginia", "2", -5:1, 9835, 1387, 1, 0, 0);
add (05091,"Miller County", "AR", "Arkansas", "7", -6:1, 39857, 1148, 1, 0, 0);
add (48051,"Burleson County", "TX", "Texas", "7", -6:1, 15652, 1148, 1, 0, 0);
add (13111,"Fannin County", "GA", "Georgia", "3", -5:1, 18622, 1387, 1, 0, 0);
add (48249,"Jim Wells County", "TX", "Texas", "7", -6:1, 40028, 1148, 1, 0, 0);
add (16025,"Camas County", "ID", "Idaho", "8", -7:1, 846, 4647, 1, 0, 0);
add (31021,"Burt County", "NE", "Nebraska", "6", -6:1, 7998, 1148, 1, 0, 0);
add (51147,"Prince Edward County", "VA", "Virginia", "2", -5:1, 19028, 1387, 1, 0, 0);
add (53015,"Cowlitz County", "WA", "Washiington", "9", -8:1, 91574, 3240, 0, 1, 0);
add (23007,"Franklin County", "ME", "Maine", "0", -5:1, 28933, 1387, 1, 0, 0);
add (19177,"Van Buren County", "IA", "Iowa", "5", -6:1, 7886, 1148, 1, 0, 0);
add (02090,"Fairbanks North Star Borough", "AK", "Alaska", "9", -9:1, 84217, 1174, 0, 0, 1);
add (39161,"Van Wert County", "OH", "Ohio", "4", -5:1, 30200, 1387, 1, 0, 0);
add (54063,"Monroe County", "WV", "West Virginia", "2", -5:1, 13205, 1387, 1, 0, 0);
add (53005,"Benton County", "WA", "Washiington", "9", -8:1, 136250, 3240, 0, 1, 0);
add (34023,"Middlesex County", "NJ", "New Jersey", "0", -5:1, 716176, 1387, 1, 0, 0);
add (25009,"Essex County", "MA", "Massachusetts", "0", -5:1, 698806, 1387, 1, 0, 0);
add (48325,"Medina County", "TX", "Texas", "7", -6:1, 37685, 1148, 1, 0, 0);
add (13255,"Spalding County", "GA", "Georgia", "3", -5:1, 57626, 1387, 1, 0, 0);
add (35029,"Luna County", "NM", "New Mexico", "8", -7:1, 24070, 4647, 1, 0, 0);
add (51750,"Radford city", "VA", "Virginia", "2", -5:1, 15734, 1387, 1, 0, 0);
add (27031,"Cook County", "MN", "Minnesota", "5", -6:1, 4792, 1148, 1, 0, 0);
add (31055,"Douglas County", "NE", "Nebraska", "6", -6:1, 443794, 1148, 1, 0, 0);
add (06089,"Shasta County", "CA", "California", "9", -8:1, 164349, 3240, 0, 1, 0);
add (05103,"Ouachita County", "AR", "Arkansas", "7", -6:1, 27921, 1148, 1, 0, 0);
add (36017,"Chenango County", "NY", "New York", "1", -5:1, 51052, 1387, 1, 0, 0);
add (34015,"Gloucester County", "NJ", "New Jersey", "0", -5:1, 247897, 1387, 1, 0, 0);
add (27071,"Koochiching County", "MN", "Minnesota", "5", -6:1, 15538, 1148, 1, 0, 0);
add (55075,"Marinette County", "WI", "Wisconnsin", "5", -6:1, 43033, 1148, 1, 0, 0);
add (48037,"Bowie County", "TX", "Texas", "7", -6:1, 83509, 1148, 1, 0, 0);
add (36041,"Hamilton County", "NY", "New York", "1", -5:1, 5193, 1387, 1, 0, 0);
add (29009,"Barry County", "MO", "Mosourri", "6", -6:1, 33120, 1148, 1, 0, 0);
add (49037,"San Juan County", "UT", "Utah", "8", -7:1, 13711, 4647, 1, 0, 0);
add (37007,"Anson County", "NC", "North Carolina", "2", -5:1, 24354, 1387, 1, 0, 0);
add (46015,"Brule County", "SD", "South Dakota", "5", -6:1, 5555, 1148, 1, 0, 0);
add (23023,"Sagadahoc County", "ME", "Maine", "0", -5:1, 35779, 1387, 1, 0, 0);
add (48003,"Andrews County", "TX", "Texas", "7", -6:1, 13976, 1148, 1, 0, 0);
add (17127,"Massac County", "IL", "Illinois", "6", -6:1, 15584, 1148, 1, 0, 0);
add (21147,"McCreary County", "KY", "Kentucky", "4", -5:1, 16659, 1387, 1, 0, 0);
add (48193,"Hamilton County", "TX", "Texas", "7", -6:1, 7603, 1148, 1, 0, 0);
add (24019,"Dorchester County", "MD", "Maryland", "2", -5:1, 29503, 1387, 1, 0, 0);
add (31027,"Cedar County", "NE", "Nebraska", "6", -6:1, 9650, 1148, 1, 0, 0);
add (45065,"McCormick County", "SC", "South Carolina", "2", -5:1, 9545, 1387, 1, 0, 0);
add (13093,"Dooly County", "GA", "Georgia", "3", -5:1, 10388, 1387, 1, 0, 0);
add (54045,"Logan County", "WV", "West Virginia", "2", -5:1, 41080, 1387, 1, 0, 0);
add (42057,"Fulton County", "PA", "Pennsylvania", "1", -5:1, 14498, 1387, 1, 0, 0);
add (39171,"Williams County", "OH", "Ohio", "4", -5:1, 38001, 1387, 1, 0, 0);
add (29007,"Audrain County", "MO", "Mosourri", "6", -6:1, 23573, 1148, 1, 0, 0);
add (36123,"Yates County", "NY", "New York", "1", -5:1, 24202, 1387, 1, 0, 0);
add (27085,"McLeod County", "MN", "Minnesota", "5", -6:1, 34017, 1148, 1, 0, 0);
add (51570,"Colonial Heights city", "VA", "Virginia", "2", -5:1, 16955, 1387, 1, 0, 0);
add (04013,"Maricopa County", "AZ", "Arizona", "8", -7:1, 2784075, 4647, 1, 0, 0);
add (16013,"Blaine County", "ID", "Idaho", "8", -7:1, 17200, 4647, 1, 0, 0);
add (08043,"Fremont County", "CO", "Colorado", "8", -7:1, 43904, 4647, 1, 0, 0);
add (12077,"Liberty County", "FL", "Florida", "3", -5:1, 6759, 1387, 1, 0, 0);
add (08125,"Yuma County", "CO", "Colorado", "8", -7:1, 9389, 4647, 1, 0, 0);
add (42077,"Lehigh County", "PA", "Pennsylvania", "1", -5:1, 299341, 1387, 1, 0, 0);
add (08071,"Las Animas County", "CO", "Colorado", "8", -7:1, 14573, 4647, 1, 0, 0);
add (28063,"Jefferson County", "MS", "Missippi", "5", -6:1, 8427, 1148, 1, 0, 0);
add (51800,"Suffolk city", "VA", "Virginia", "2", -5:1, 62703, 1387, 1, 0, 0);
add (42111,"Somerset County", "PA", "Pennsylvania", "1", -5:1, 80267, 1387, 1, 0, 0);
add (48035,"Bosque County", "TX", "Texas", "7", -6:1, 16557, 1148, 1, 0, 0);
add (29031,"Cape Girardeau County", "MO", "Mosourri", "6", -6:1, 66314, 1148, 1, 0, 0);
add (18163,"Vanderburgh County", "IN", "Indiana", "4", -5:1, 168179, 1387, 1, 0, 0);
add (21049,"Clark County", "KY", "Kentucky", "4", -6:1, 31978, 1148, 1, 0, 0);
add (48027,"Bell County", "TX", "Texas", "7", -6:1, 223468, 1148, 1, 0, 0);
add (02060,"Bristol Bay Borough", "AK", "Alaska", "9", -9:1, 1356, 1174, 0, 0, 1);
add (18111,"Newton County", "IN", "Indiana", "4", -5:1, 14734, 1387, 1, 0, 0);
add (35001,"Bernalillo County", "NM", "New Mexico", "8", -7:1, 525958, 4647, 1, 0, 0);
add (55095,"Polk County", "WI", "Wisconnsin", "5", -6:1, 38786, 1148, 1, 0, 0);
add (25015,"Hampshire County", "MA", "Massachusetts", "0", -5:1, 149384, 1387, 1, 0, 0);
add (40037,"Creek County", "OK", "Oklahoma", "7", -6:1, 67142, 1148, 1, 0, 0);
add (54021,"Gilmer County", "WV", "West Virginia", "2", -5:1, 7130, 1387, 1, 0, 0);
add (30029,"Flathead County", "MT", "Montana", "6", -7:1, 71831, 4647, 1, 0, 0);
add (26113,"Missaukee County", "MI", "Michigan", "4", -5:1, 13892, 1387, 1, 0, 0);
add (19163,"Scott County", "IA", "Iowa", "5", -6:1, 158591, 1148, 1, 0, 0);
add (26059,"Hillsdale County", "MI", "Michigan", "4", -5:1, 46614, 1387, 1, 0, 0);
add (19075,"Grundy County", "IA", "Iowa", "5", -6:1, 12183, 1148, 1, 0, 0);
add (39153,"Summit County", "OH", "Ohio", "4", -5:1, 537730, 1387, 1, 0, 0);
add (51183,"Sussex County", "VA", "Virginia", "2", -5:1, 9925, 1387, 1, 0, 0);
add (51029,"Buckingham County", "VA", "Virginia", "2", -5:1, 14639, 1387, 1, 0, 0);
add (53021,"Franklin County", "WA", "Washiington", "9", -8:1, 46459, 3240, 0, 1, 0);
add (37111,"McDowell County", "NC", "North Carolina", "2", -5:1, 40048, 1387, 1, 0, 0);
add (47011,"Bradley County", "TN", "Tennesee", "3", -5:1, 83292, 1387, 1, 0, 0);
add (21027,"Breckinridge County", "KY", "Kentucky", "4", -6:1, 17465, 1148, 1, 0, 0);
add (30039,"Granite County", "MT", "Montana", "6", -7:1, 2667, 4647, 1, 0, 0);
add (13009,"Baldwin County", "GA", "Georgia", "3", -5:1, 41968, 1387, 1, 0, 0);
add (13101,"Echols County", "GA", "Georgia", "3", -5:1, 2401, 1387, 1, 0, 0);
add (21063,"Elliott County", "KY", "Kentucky", "4", -6:1, 6602, 1148, 1, 0, 0);
add (27127,"Redwood County", "MN", "Minnesota", "5", -6:1, 16489, 1148, 1, 0, 0);
add (30077,"Powell County", "MT", "Montana", "6", -7:1, 7000, 4647, 1, 0, 0);
add (40047,"Garfield County", "OK", "Oklahoma", "7", -6:1, 56859, 1148, 1, 0, 0);
add (56023,"Lincoln County", "WY", "Wyoming", "8", -7:1, 13876, 4647, 1, 0, 0);
add (29037,"Cass County", "MO", "Mosourri", "6", -6:1, 80520, 1148, 1, 0, 0);
add (01081,"Lee County", "AL", "Alabama", "3", -6:1, 100444, 1148, 1, 0, 0);
add (06033,"Lake County", "CA", "California", "9", -8:1, 55147, 3240, 0, 1, 0);
add (13021,"Bibb County", "GA", "Georgia", "3", -5:1, 156086, 1387, 1, 0, 0);
add (27151,"Swift County", "MN", "Minnesota", "5", -6:1, 10804, 1148, 1, 0, 0);
add (20049,"Elk County", "KS", "Kansas", "6", -6:1, 3351, 1148, 1, 0, 0);
add (42109,"Snyder County", "PA", "Pennsylvania", "1", -5:1, 38226, 1387, 1, 0, 0);
add (46115,"Spink County", "SD", "South Dakota", "5", -7:1, 7572, 4647, 1, 0, 0);
add (20015,"Butler County", "KS", "Kansas", "6", -6:1, 61932, 1148, 1, 0, 0);
add (06081,"San Mateo County", "CA", "California", "9", -8:1, 700765, 3240, 0, 1, 0);
add (49051,"Wasatch County", "UT", "Utah", "8", -7:1, 13267, 4647, 1, 0, 0);
add (16061,"Lewis County", "ID", "Idaho", "8", -7:1, 4007, 4647, 1, 0, 0);
add (18173,"Warrick County", "IN", "Indiana", "4", -5:1, 51609, 1387, 1, 0, 0);
add (36019,"Clinton County", "NY", "New York", "1", -5:1, 79970, 1387, 1, 0, 0);
add (20155,"Reno County", "KS", "Kansas", "6", -6:1, 63211, 1148, 1, 0, 0);
add (20007,"Barber County", "KS", "Kansas", "6", -6:1, 5342, 1148, 1, 0, 0);
add (37049,"Craven County", "NC", "North Carolina", "2", -5:1, 88129, 1387, 1, 0, 0);
add (23015,"Lincoln County", "ME", "Maine", "0", -5:1, 31815, 1387, 1, 0, 0);
add (18059,"Hancock County", "IN", "Indiana", "4", -5:1, 54524, 1387, 1, 0, 0);
add (39065,"Hardin County", "OH", "Ohio", "4", -5:1, 31725, 1387, 1, 0, 0);
add (17017,"Cass County", "IL", "Illinois", "6", -6:1, 13266, 1148, 1, 0, 0);
add (29207,"Stoddard County", "MO", "Mosourri", "6", -6:1, 29623, 1148, 1, 0, 0);
add (46007,"Bennett County", "SD", "South Dakota", "5", -6:1, 3389, 1148, 1, 0, 0);
add (29117,"Livingston County", "MO", "Mosourri", "6", -6:1, 14151, 1148, 1, 0, 0);
add (13261,"Sumter County", "GA", "Georgia", "3", -5:1, 31324, 1387, 1, 0, 0);
add (40049,"Garvin County", "OK", "Oklahoma", "7", -6:1, 27044, 1148, 1, 0, 0);
add (32015,"Lander County", "NV", "Nevada", "8", -8:1, 6987, 3240, 0, 1, 0);
add (36093,"Schenectady County", "NY", "New York", "1", -5:1, 145530, 1387, 1, 0, 0);
add (12027,"DeSoto County", "FL", "Florida", "3", -5:1, 24820, 1387, 1, 0, 0);
add (48059,"Callahan County", "TX", "Texas", "7", -6:1, 12796, 1148, 1, 0, 0);
add (48401,"Rusk County", "TX", "Texas", "7", -6:1, 45877, 1148, 1, 0, 0);
add (47073,"Hawkins County", "TN", "Tennesee", "3", -5:1, 49719, 1387, 1, 0, 0);
add (30059,"Meagher County", "MT", "Montana", "6", -7:1, 1797, 4647, 1, 0, 0);
add (05099,"Nevada County", "AR", "Arkansas", "7", -6:1, 10034, 1148, 1, 0, 0);
add (13055,"Chattooga County", "GA", "Georgia", "3", -5:1, 22813, 1387, 1, 0, 0);
add (18007,"Benton County", "IN", "Indiana", "4", -5:1, 9725, 1387, 1, 0, 0);
add (47125,"Montgomery County", "TN", "Tennesee", "3", -6:1, 127265, 1148, 1, 0, 0);
add (36101,"Steuben County", "NY", "New York", "1", -5:1, 97950, 1387, 1, 0, 0);
add (44003,"Kent County", "RI", "Rhode Island", "0", -5:1, 161811, 1387, 1, 0, 0);
add (18137,"Ripley County", "IN", "Indiana", "4", -5:1, 27205, 1387, 1, 0, 0);
add (48001,"Anderson County", "TX", "Texas", "7", -6:1, 52352, 1148, 1, 0, 0);
add (49019,"Grand County", "UT", "Utah", "8", -7:1, 8068, 4647, 1, 0, 0);
add (32033,"White Pine County", "NV", "Nevada", "8", -8:1, 10078, 3240, 0, 1, 0);
add (19147,"Palo Alto County", "IA", "Iowa", "5", -6:1, 10017, 1148, 1, 0, 0);
add (23017,"Oxford County", "ME", "Maine", "0", -5:1, 53673, 1387, 1, 0, 0);
add (05047,"Franklin County", "AR", "Arkansas", "7", -6:1, 16932, 1148, 1, 0, 0);
add (37041,"Chowan County", "NC", "North Carolina", "2", -5:1, 14191, 1387, 1, 0, 0);
add (55079,"Milwaukee County", "WI", "Wisconnsin", "5", -6:1, 911713, 1148, 1, 0, 0);
add (56035,"Sublette County", "WY", "Wyoming", "8", -7:1, 5738, 4647, 1, 0, 0);
add (29181,"Ripley County", "MO", "Mosourri", "6", -6:1, 14072, 1148, 1, 0, 0);
add (30045,"Judith Basin County", "MT", "Montana", "6", -7:1, 2294, 4647, 1, 0, 0);
add (01041,"Crenshaw County", "AL", "Alabama", "3", -6:1, 13636, 1148, 1, 0, 0);
add (06105,"Trinity County", "CA", "California", "9", -8:1, 13117, 3240, 0, 1, 0);
add (01101,"Montgomery County", "AL", "Alabama", "3", -6:1, 217693, 1148, 1, 0, 0);
add (29169,"Pulaski County", "MO", "Mosourri", "6", -6:1, 38507, 1148, 1, 0, 0);
add (40073,"Kingfisher County", "OK", "Oklahoma", "7", -6:1, 13528, 1148, 1, 0, 0);
add (19095,"Iowa County", "IA", "Iowa", "5", -6:1, 15550, 1148, 1, 0, 0);
add (05145,"White County", "AR", "Arkansas", "7", -6:1, 64526, 1148, 1, 0, 0);
add (26075,"Jackson County", "MI", "Michigan", "4", -5:1, 156157, 1387, 1, 0, 0);
add (13165,"Jenkins County", "GA", "Georgia", "3", -5:1, 8447, 1387, 1, 0, 0);
add (24025,"Harford County", "MD", "Maryland", "2", -5:1, 214668, 1387, 1, 0, 0);
add (47043,"Dickson County", "TN", "Tennesee", "3", -5:1, 42254, 1387, 1, 0, 0);
add (28127,"Simpson County", "MS", "Missippi", "5", -6:1, 25338, 1148, 1, 0, 0);
add (27001,"Aitkin County", "MN", "Minnesota", "5", -6:1, 14152, 1148, 1, 0, 0);
add (04009,"Graham County", "AZ", "Arizona", "8", -7:1, 31696, 4647, 1, 0, 0);
add (17049,"Effingham County", "IL", "Illinois", "6", -6:1, 33504, 1148, 1, 0, 0);
add (35035,"Otero County", "NM", "New Mexico", "8", -7:1, 54630, 4647, 1, 0, 0);
add (53033,"King County", "WA", "Washiington", "9", -8:1, 1654876, 3240, 0, 1, 0);
add (29139,"Montgomery County", "MO", "Mosourri", "6", -6:1, 12074, 1148, 1, 0, 0);
add (51550,"Chesapeake city", "VA", "Virginia", "2", -5:1, 199564, 1387, 1, 0, 0);
add (48459,"Upshur County", "TX", "Texas", "7", -6:1, 35885, 1148, 1, 0, 0);
add (05113,"Polk County", "AR", "Arkansas", "7", -6:1, 19662, 1148, 1, 0, 0);
add (02270,"Wade Hampton Census Area", "AK", "Alaska", "9", -9:1, 6812, 1174, 0, 0, 1);
add (19005,"Allamakee County", "IA", "Iowa", "5", -6:1, 13989, 1148, 1, 0, 0);
add (20039,"Decatur County", "KS", "Kansas", "6", -6:1, 3456, 1148, 1, 0, 0);
add (55125,"Vilas County", "WI", "Wisconnsin", "5", -6:1, 21277, 1148, 1, 0, 0);
add (51700,"Newport News city", "VA", "Virginia", "2", -5:1, 178615, 1387, 1, 0, 0);
add (28129,"Smith County", "MS", "Missippi", "5", -6:1, 15296, 1148, 1, 0, 0);
add (17169,"Schuyler County", "IL", "Illinois", "6", -6:1, 7632, 1148, 1, 0, 0);
add (29053,"Cooper County", "MO", "Mosourri", "6", -6:1, 16029, 1148, 1, 0, 0);
add (18005,"Bartholomew County", "IN", "Indiana", "4", -5:1, 69579, 1387, 1, 0, 0);
add (48357,"Ochiltree County", "TX", "Texas", "7", -6:1, 8827, 1148, 1, 0, 0);
add (20133,"Neosho County", "KS", "Kansas", "6", -6:1, 16760, 1148, 1, 0, 0);
add (48287,"Lee County", "TX", "Texas", "7", -6:1, 14916, 1148, 1, 0, 0);
add (05077,"Lee County", "AR", "Arkansas", "7", -6:1, 12406, 1148, 1, 0, 0);
add (35011,"DeBaca County", "NM", "New Mexico", "8", -7:1, 2389, 4647, 1, 0, 0);
add (45035,"Dorchester County", "SC", "South Carolina", "2", -5:1, 88133, 1387, 1, 0, 0);
add (48373,"Polk County", "TX", "Texas", "7", -6:1, 50309, 1148, 1, 0, 0);
add (33007,"Coos County", "NH", "New Hampshire", "0", -5:1, 32875, 1387, 1, 0, 0);
add (37031,"Carteret County", "NC", "North Carolina", "2", -5:1, 60054, 1387, 1, 0, 0);
add (54075,"Pocahontas County", "WV", "West Virginia", "2", -5:1, 9268, 1387, 1, 0, 0);
add (16027,"Canyon County", "ID", "Idaho", "8", -7:1, 120266, 4647, 1, 0, 0);
add (01039,"Covington County", "AL", "Alabama", "3", -6:1, 37402, 1148, 1, 0, 0);
add (19041,"Clay County", "IA", "Iowa", "5", -6:1, 17532, 1148, 1, 0, 0);
add (01033,"Colbert County", "AL", "Alabama", "3", -6:1, 52946, 1148, 1, 0, 0);
add (48271,"Kinney County", "TX", "Texas", "7", -6:1, 3482, 1148, 1, 0, 0);
add (54053,"Mason County", "WV", "West Virginia", "2", -5:1, 25869, 1387, 1, 0, 0);
add (13077,"Coweta County", "GA", "Georgia", "3", -5:1, 85028, 1387, 1, 0, 0);
add (18035,"Delaware County", "IN", "Indiana", "4", -5:1, 116828, 1387, 1, 0, 0);
add (54017,"Doddridge County", "WV", "West Virginia", "2", -5:1, 7554, 1387, 1, 0, 0);
add (24011,"Caroline County", "MD", "Maryland", "2", -5:1, 29489, 1387, 1, 0, 0);
add (29199,"Scotland County", "MO", "Mosourri", "6", -6:1, 4814, 1148, 1, 0, 0);
add (51075,"Goochland County", "VA", "Virginia", "2", -5:1, 17823, 1387, 1, 0, 0);
add (01005,"Barbour County", "AL", "Alabama", "3", -6:1, 26895, 1148, 1, 0, 0);
add (13189,"McDuffie County", "GA", "Georgia", "3", -5:1, 21770, 1387, 1, 0, 0);
add (01055,"Etowah County", "AL", "Alabama", "3", -6:1, 103975, 1148, 1, 0, 0);
add (20175,"Seward County", "KS", "Kansas", "6", -6:1, 19984, 1148, 1, 0, 0);
add (48501,"Yoakum County", "TX", "Texas", "7", -6:1, 8010, 1148, 1, 0, 0);
add (05039,"Dallas County", "AR", "Arkansas", "7", -6:1, 9060, 1148, 1, 0, 0);
add (29081,"Harrison County", "MO", "Mosourri", "6", -6:1, 8506, 1148, 1, 0, 0);
add (28105,"Oktibbeha County", "MS", "Missippi", "5", -6:1, 39291, 1148, 1, 0, 0);
add (34007,"Camden County", "NJ", "New Jersey", "0", -5:1, 505204, 1387, 1, 0, 0);
add (27147,"Steele County", "MN", "Minnesota", "5", -6:1, 31736, 1148, 1, 0, 0);
add (55029,"Door County", "WI", "Wisconnsin", "5", -6:1, 27027, 1148, 1, 0, 0);
add (18073,"Jasper County", "IN", "Indiana", "4", -5:1, 29260, 1387, 1, 0, 0);
add (28155,"Webster County", "MS", "Missippi", "5", -6:1, 10547, 1148, 1, 0, 0);
add (23019,"Penobscot County", "ME", "Maine", "0", -5:1, 142323, 1387, 1, 0, 0);
add (13199,"Meriwether County", "GA", "Georgia", "3", -5:1, 23112, 1387, 1, 0, 0);
add (37091,"Hertford County", "NC", "North Carolina", "2", -5:1, 22289, 1387, 1, 0, 0);
add (02150,"Kodiak Island Borough", "AK", "Alaska", "9", -9:1, 14520, 1174, 0, 0, 1);
add (48503,"Young County", "TX", "Texas", "7", -6:1, 17697, 1148, 1, 0, 0);
add (47087,"Jackson County", "TN", "Tennesee", "3", -5:1, 9629, 1387, 1, 0, 0);
add (29131,"Miller County", "MO", "Mosourri", "6", -6:1, 22422, 1148, 1, 0, 0);
add (20161,"Riley County", "KS", "Kansas", "6", -6:1, 63615, 1148, 1, 0, 0);
add (01105,"Perry County", "AL", "Alabama", "3", -6:1, 12667, 1148, 1, 0, 0);
add (41007,"Clatsop County", "OR", "Oregon", "9", -8:1, 35424, 3240, 0, 1, 0);
add (36013,"Chautauqua County", "NY", "New York", "1", -5:1, 138103, 1387, 1, 0, 0);
add (18167,"Vigo County", "IN", "Indiana", "4", -5:1, 105083, 1387, 1, 0, 0);
add (49009,"Daggett County", "UT", "Utah", "8", -7:1, 737, 4647, 1, 0, 0);
add (19083,"Hardin County", "IA", "Iowa", "5", -6:1, 18462, 1148, 1, 0, 0);
add (26131,"Ontonagon County", "MI", "Michigan", "4", -5:1, 7878, 1387, 1, 0, 0);
add (20013,"Brown County", "KS", "Kansas", "6", -6:1, 11070, 1148, 1, 0, 0);
add (48225,"Houston County", "TX", "Texas", "7", -6:1, 21901, 1148, 1, 0, 0);
add (29021,"Buchanan County", "MO", "Mosourri", "6", -6:1, 81776, 1148, 1, 0, 0);
add (48369,"Parmer County", "TX", "Texas", "7", -6:1, 10302, 1148, 1, 0, 0);
add (31095,"Jefferson County", "NE", "Nebraska", "6", -7:1, 8378, 4647, 1, 0, 0);
add (24031,"Montgomery County", "MD", "Maryland", "2", -5:1, 840879, 1387, 1, 0, 0);
add (31133,"Pawnee County", "NE", "Nebraska", "6", -7:1, 3131, 4647, 1, 0, 0);
add (17081,"Jefferson County", "IL", "Illinois", "6", -6:1, 37373, 1148, 1, 0, 0);
add (42049,"Erie County", "PA", "Pennsylvania", "1", -5:1, 276401, 1387, 1, 0, 0);
add (24013,"Carroll County", "MD", "Maryland", "2", -5:1, 149697, 1387, 1, 0, 0);
add (48257,"Kaufman County", "TX", "Texas", "7", -6:1, 65736, 1148, 1, 0, 0);
add (48391,"Refugio County", "TX", "Texas", "7", -6:1, 7907, 1148, 1, 0, 0);
add (28133,"Sunflower County", "MS", "Missippi", "5", -6:1, 34577, 1148, 1, 0, 0);
add (21177,"Muhlenberg County", "KY", "Kentucky", "4", -5:1, 32173, 1387, 1, 0, 0);
add (26125,"Oakland County", "MI", "Michigan", "4", -5:1, 1176488, 1387, 1, 0, 0);
add (41029,"Jackson County", "OR", "Oregon", "9", -8:1, 173123, 3240, 0, 1, 0);
add (47069,"Hardeman County", "TN", "Tennesee", "3", -5:1, 24895, 1387, 1, 0, 0);
add (54099,"Wayne County", "WV", "West Virginia", "2", -5:1, 41957, 1387, 1, 0, 0);
add (47105,"Loudon County", "TN", "Tennesee", "3", -6:1, 39052, 1148, 1, 0, 0);
add (54025,"Greenbrier County", "WV", "West Virginia", "2", -5:1, 35383, 1387, 1, 0, 0);
add (54001,"Barbour County", "WV", "West Virginia", "2", -5:1, 16152, 1387, 1, 0, 0);
add (12047,"Hamilton County", "FL", "Florida", "3", -5:1, 12651, 1387, 1, 0, 0);
add (30089,"Sanders County", "MT", "Montana", "6", -7:1, 10185, 4647, 1, 0, 0);
add (28033,"DeSoto County", "MS", "Missippi", "5", -6:1, 96897, 1148, 1, 0, 0);
add (19195,"Worth County", "IA", "Iowa", "5", -6:1, 7779, 1148, 1, 0, 0);
add (40127,"Pushmataha County", "OK", "Oklahoma", "7", -6:1, 11584, 1148, 1, 0, 0);
add (38011,"Bowman County", "ND", "North Dakota", "5", -6:1, 3317, 1148, 1, 0, 0);
add (05081,"Little River County", "AR", "Arkansas", "7", -6:1, 13206, 1148, 1, 0, 0);
add (13035,"Butts County", "GA", "Georgia", "3", -5:1, 17837, 1387, 1, 0, 0);
add (37053,"Currituck County", "NC", "North Carolina", "2", -5:1, 17908, 1387, 1, 0, 0);
add (19057,"Des Moines County", "IA", "Iowa", "5", -6:1, 41944, 1148, 1, 0, 0);
add (48251,"Johnson County", "TX", "Texas", "7", -6:1, 118125, 1148, 1, 0, 0);
add (54089,"Summers County", "WV", "West Virginia", "2", -5:1, 13146, 1387, 1, 0, 0);
add (48153,"Floyd County", "TX", "Texas", "7", -6:1, 8191, 1148, 1, 0, 0);
add (37115,"Madison County", "NC", "North Carolina", "2", -5:1, 18756, 1387, 1, 0, 0);
add (18041,"Fayette County", "IN", "Indiana", "4", -5:1, 25969, 1387, 1, 0, 0);
add (21019,"Boyd County", "KY", "Kentucky", "4", -6:1, 49543, 1148, 1, 0, 0);
add (48173,"Glasscock County", "TX", "Texas", "7", -6:1, 1396, 1148, 1, 0, 0);
add (17089,"Kane County", "IL", "Illinois", "6", -6:1, 391249, 1148, 1, 0, 0);
add (54039,"Kanawha County", "WV", "West Virginia", "2", -5:1, 202011, 1387, 1, 0, 0);
add (13265,"Taliaferro County", "GA", "Georgia", "3", -5:1, 1908, 1387, 1, 0, 0);
add (34027,"Morris County", "NJ", "New Jersey", "0", -5:1, 459896, 1387, 1, 0, 0);
add (51105,"Lee County", "VA", "Virginia", "2", -5:1, 23815, 1387, 1, 0, 0);
add (40079,"Le Flore County", "OK", "Oklahoma", "7", -6:1, 46564, 1148, 1, 0, 0);
add (19063,"Emmet County", "IA", "Iowa", "5", -6:1, 10887, 1148, 1, 0, 0);
add (48449,"Titus County", "TX", "Texas", "7", -6:1, 25422, 1148, 1, 0, 0);
add (21069,"Fleming County", "KY", "Kentucky", "4", -6:1, 13441, 1148, 1, 0, 0);
add (18017,"Cass County", "IN", "Indiana", "4", -5:1, 38685, 1387, 1, 0, 0);
add (35053,"Socorro County", "NM", "New Mexico", "8", -7:1, 16333, 4647, 1, 0, 0);
add (08001,"Adams County", "CO", "Colorado", "8", -7:1, 323853, 4647, 1, 0, 0);
add (33019,"Sullivan County", "NH", "New Hampshire", "0", -5:1, 40027, 1387, 1, 0, 0);
add (40039,"Custer County", "OK", "Oklahoma", "7", -6:1, 25493, 1148, 1, 0, 0);
add (51081,"Greensville County", "VA", "Virginia", "2", -5:1, 11281, 1387, 1, 0, 0);
add (48223,"Hopkins County", "TX", "Texas", "7", -6:1, 30512, 1148, 1, 0, 0);
add (40011,"Blaine County", "OK", "Oklahoma", "7", -6:1, 10513, 1148, 1, 0, 0);
add (36033,"Franklin County", "NY", "New York", "1", -5:1, 48582, 1387, 1, 0, 0);
add (47039,"Decatur County", "TN", "Tennesee", "3", -5:1, 10807, 1387, 1, 0, 0);
add (55127,"Walworth County", "WI", "Wisconnsin", "5", -6:1, 85353, 1148, 1, 0, 0);
add (48217,"Hill County", "TX", "Texas", "7", -6:1, 30534, 1148, 1, 0, 0);
add (13321,"Worth County", "GA", "Georgia", "3", -5:1, 22485, 1387, 1, 0, 0);
add (21201,"Robertson County", "KY", "Kentucky", "4", -5:1, 2209, 1387, 1, 0, 0);
add (47141,"Putnam County", "TN", "Tennesee", "3", -6:1, 59143, 1148, 1, 0, 0);
add (21213,"Simpson County", "KY", "Kentucky", "4", -5:1, 16401, 1387, 1, 0, 0);
add (40109,"Oklahoma County", "OK", "Oklahoma", "7", -6:1, 632988, 1148, 1, 0, 0);
add (24035,"Queen Anne County", "MD", "Maryland", "2", -5:1, 39672, 1387, 1, 0, 0);
add (17203,"Woodford County", "IL", "Illinois", "6", -6:1, 35212, 1148, 1, 0, 0);
add (19127,"Marshall County", "IA", "Iowa", "5", -6:1, 38732, 1148, 1, 0, 0);
add (35009,"Curry County", "NM", "New Mexico", "8", -7:1, 45290, 4647, 1, 0, 0);
add (21009,"Barren County", "KY", "Kentucky", "4", -6:1, 36979, 1148, 1, 0, 0);
add (21229,"Washington County", "KY", "Kentucky", "4", -5:1, 10918, 1387, 1, 0, 0);
add (13183,"Long County", "GA", "Georgia", "3", -5:1, 8585, 1387, 1, 0, 0);
add (22013,"Bienville Parish", "LA", "Louisiana", "7", -6:1, 15814, 1148, 1, 0, 0);
add (31117,"McPherson County", "NE", "Nebraska", "6", -7:1, 563, 4647, 1, 0, 0);
add (46055,"Haakon County", "SD", "South Dakota", "5", -7:1, 2353, 4647, 1, 0, 0);
add (16005,"Bannock County", "ID", "Idaho", "8", -7:1, 74866, 4647, 1, 0, 0);
add (04017,"Navajo County", "AZ", "Arizona", "8", -7:1, 96997, 4647, 1, 0, 0);
add (37055,"Dare County", "NC", "North Carolina", "2", -5:1, 28952, 1387, 1, 0, 0);
add (48067,"Cass County", "TX", "Texas", "7", -6:1, 30828, 1148, 1, 0, 0);
add (17023,"Clark County", "IL", "Illinois", "6", -6:1, 16534, 1148, 1, 0, 0);
add (55071,"Manitowoc County", "WI", "Wisconnsin", "5", -6:1, 82412, 1148, 1, 0, 0);
add (16065,"Madison County", "ID", "Idaho", "8", -7:1, 23569, 4647, 1, 0, 0);
add (31155,"Saunders County", "NE", "Nebraska", "6", -7:1, 19245, 4647, 1, 0, 0);
add (35039,"Rio Arriba County", "NM", "New Mexico", "8", -7:1, 37787, 4647, 1, 0, 0);
add (01125,"Tuscaloosa County", "AL", "Alabama", "3", -6:1, 160768, 1148, 1, 0, 0);
add (16019,"Bonneville County", "ID", "Idaho", "8", -7:1, 80672, 4647, 1, 0, 0);
add (55129,"Washburn County", "WI", "Wisconnsin", "5", -6:1, 15421, 1148, 1, 0, 0);
add (16033,"Clark County", "ID", "Idaho", "8", -7:1, 873, 4647, 1, 0, 0);
add (47115,"Marion County", "TN", "Tennesee", "3", -6:1, 26851, 1148, 1, 0, 0);
add (37195,"Wilson County", "NC", "North Carolina", "2", -5:1, 68188, 1387, 1, 0, 0);
add (21161,"Mason County", "KY", "Kentucky", "4", -5:1, 17021, 1387, 1, 0, 0);
add (36051,"Livingston County", "NY", "New York", "1", -5:1, 66000, 1387, 1, 0, 0);
add (13061,"Clay County", "GA", "Georgia", "3", -5:1, 3453, 1387, 1, 0, 0);
add (20191,"Sumner County", "KS", "Kansas", "6", -6:1, 27043, 1148, 1, 0, 0);
add (20143,"Ottawa County", "KS", "Kansas", "6", -6:1, 5905, 1148, 1, 0, 0);
add (51127,"New Kent County", "VA", "Virginia", "2", -5:1, 13052, 1387, 1, 0, 0);
add (19071,"Fremont County", "IA", "Iowa", "5", -6:1, 7746, 1148, 1, 0, 0);
add (45059,"Laurens County", "SC", "South Carolina", "2", -5:1, 63249, 1387, 1, 0, 0);
add (20117,"Marshall County", "KS", "Kansas", "6", -6:1, 11006, 1148, 1, 0, 0);
add (06025,"Imperial County", "CA", "California", "9", -8:1, 144051, 3240, 0, 1, 0);
add (27049,"Goodhue County", "MN", "Minnesota", "5", -6:1, 43137, 1148, 1, 0, 0);
add (31105,"Kimball County", "NE", "Nebraska", "6", -7:1, 4082, 4647, 1, 0, 0);
add (40043,"Dewey County", "OK", "Oklahoma", "7", -6:1, 4928, 1148, 1, 0, 0);
add (29087,"Holt County", "MO", "Mosourri", "6", -6:1, 5554, 1148, 1, 0, 0);
add (13159,"Jasper County", "GA", "Georgia", "3", -5:1, 10155, 1387, 1, 0, 0);
add (48105,"Crockett County", "TX", "Texas", "7", -6:1, 4602, 1148, 1, 0, 0);
add (54047,"McDowell County", "WV", "West Virginia", "2", -5:1, 29916, 1387, 1, 0, 0);
add (48437,"Swisher County", "TX", "Texas", "7", -6:1, 8301, 1148, 1, 0, 0);
add (38089,"Stark County", "ND", "North Dakota", "5", -6:1, 22780, 1148, 1, 0, 0);
add (26051,"Gladwin County", "MI", "Michigan", "4", -5:1, 25333, 1387, 1, 0, 0);
add (50027,"Windsor County", "VT", "Vermont", "0", -5:1, 55444, 1387, 1, 0, 0);
add (46119,"Sully County", "SD", "South Dakota", "5", -7:1, 1470, 4647, 1, 0, 0);
add (01109,"Pike County", "AL", "Alabama", "3", -6:1, 28646, 1148, 1, 0, 0);
add (12059,"Holmes County", "FL", "Florida", "3", -5:1, 18622, 1387, 1, 0, 0);
add (29145,"Newton County", "MO", "Mosourri", "6", -6:1, 49152, 1148, 1, 0, 0);
add (37167,"Stanly County", "NC", "North Carolina", "2", -5:1, 56083, 1387, 1, 0, 0);
add (53027,"Grays Harbor County", "WA", "Washiington", "9", -8:1, 67739, 3240, 0, 1, 0);
add (24023,"Garrett County", "MD", "Maryland", "2", -5:1, 29238, 1387, 1, 0, 0);
add (38097,"Traill County", "ND", "North Dakota", "5", -6:1, 8544, 1148, 1, 0, 0);
add (18071,"Jackson County", "IN", "Indiana", "4", -5:1, 40992, 1387, 1, 0, 0);
add (08085,"Montrose County", "CO", "Colorado", "8", -7:1, 30764, 4647, 1, 0, 0);
add (38027,"Eddy County", "ND", "North Dakota", "5", -6:1, 2847, 1148, 1, 0, 0);
add (29043,"Christian County", "MO", "Mosourri", "6", -6:1, 48997, 1148, 1, 0, 0);
add (22127,"Winn Parish", "LA", "Louisiana", "7", -6:1, 17714, 1148, 1, 0, 0);
add (39075,"Holmes County", "OH", "Ohio", "4", -5:1, 37841, 1387, 1, 0, 0);
add (33005,"Cheshire County", "NH", "New Hampshire", "0", -5:1, 71828, 1387, 1, 0, 0);
add (36053,"Madison County", "NY", "New York", "1", -5:1, 71069, 1387, 1, 0, 0);
add (19079,"Hamilton County", "IA", "Iowa", "5", -6:1, 16011, 1148, 1, 0, 0);
add (29001,"Adair County", "MO", "Mosourri", "6", -6:1, 24286, 1148, 1, 0, 0);
add (48149,"Fayette County", "TX", "Texas", "7", -6:1, 21414, 1148, 1, 0, 0);
add (42101,"Philadelphia County", "PA", "Pennsylvania", "1", -5:1, 1436287, 1387, 1, 0, 0);
add (47129,"Morgan County", "TN", "Tennesee", "3", -6:1, 18775, 1148, 1, 0, 0);
add (17137,"Morgan County", "IL", "Illinois", "6", -6:1, 35346, 1148, 1, 0, 0);
add (17071,"Henderson County", "IL", "Illinois", "6", -6:1, 8601, 1148, 1, 0, 0);
add (16009,"Benewah County", "ID", "Idaho", "8", -7:1, 9119, 4647, 1, 0, 0);
add (48427,"Starr County", "TX", "Texas", "7", -6:1, 55906, 1148, 1, 0, 0);
add (05131,"Sebastian County", "AR", "Arkansas", "7", -6:1, 106180, 1148, 1, 0, 0);
add (38017,"Cass County", "ND", "North Dakota", "5", -6:1, 116832, 1148, 1, 0, 0);
add (17135,"Montgomery County", "IL", "Illinois", "6", -6:1, 31390, 1148, 1, 0, 0);
add (28143,"Tunica County", "MS", "Missippi", "5", -6:1, 8039, 1148, 1, 0, 0);
add (47061,"Grundy County", "TN", "Tennesee", "3", -5:1, 14138, 1387, 1, 0, 0);
add (56011,"Crook County", "WY", "Wyoming", "8", -7:1, 5829, 4647, 1, 0, 0);
add (39159,"Union County", "OH", "Ohio", "4", -5:1, 39494, 1387, 1, 0, 0);
add (21065,"Estill County", "KY", "Kentucky", "4", -6:1, 15588, 1148, 1, 0, 0);
add (42007,"Beaver County", "PA", "Pennsylvania", "1", -5:1, 184406, 1387, 1, 0, 0);
add (38025,"Dunn County", "ND", "North Dakota", "5", -6:1, 3560, 1148, 1, 0, 0);
add (55083,"Oconto County", "WI", "Wisconnsin", "5", -6:1, 34014, 1148, 1, 0, 0);
add (51820,"Waynesboro city", "VA", "Virginia", "2", -5:1, 18561, 1387, 1, 0, 0);
add (18081,"Johnson County", "IN", "Indiana", "4", -5:1, 109368, 1387, 1, 0, 0);
add (40005,"Atoka County", "OK", "Oklahoma", "7", -6:1, 13237, 1148, 1, 0, 0);
add (48341,"Moore County", "TX", "Texas", "7", -6:1, 19686, 1148, 1, 0, 0);
add (45053,"Jasper County", "SC", "South Carolina", "2", -5:1, 16995, 1387, 1, 0, 0);
add (39101,"Marion County", "OH", "Ohio", "4", -5:1, 64774, 1387, 1, 0, 0);
add (22027,"Claiborne Parish", "LA", "Louisiana", "7", -6:1, 16919, 1148, 1, 0, 0);
add (13129,"Gordon County", "GA", "Georgia", "3", -5:1, 41052, 1387, 1, 0, 0);
add (04001,"Apache County", "AZ", "Arizona", "8", -7:1, 68782, 4647, 1, 0, 0);
add (48355,"Nueces County", "TX", "Texas", "7", -6:1, 316340, 1148, 1, 0, 0);
add (18155,"Switzerland County", "IN", "Indiana", "4", -5:1, 8893, 1387, 1, 0, 0);
add (41027,"Hood River County", "OR", "Oregon", "9", -8:1, 19553, 3240, 0, 1, 0);
add (27035,"Crow Wing County", "MN", "Minnesota", "5", -6:1, 51681, 1148, 1, 0, 0);
add (55065,"Lafayette County", "WI", "Wisconnsin", "5", -6:1, 16261, 1148, 1, 0, 0);
add (21225,"Union County", "KY", "Kentucky", "4", -5:1, 16577, 1387, 1, 0, 0);
add (06037,"Los Angeles County", "CA", "California", "9", -8:1, 9213533, 3240, 0, 1, 0);
add (21219,"Todd County", "KY", "Kentucky", "4", -5:1, 11222, 1387, 1, 0, 0);
add (39091,"Logan County", "OH", "Ohio", "4", -5:1, 46204, 1387, 1, 0, 0);
add (26091,"Lenawee County", "MI", "Michigan", "4", -5:1, 98412, 1387, 1, 0, 0);
add (46123,"Tripp County", "SD", "South Dakota", "5", -7:1, 6737, 4647, 1, 0, 0);
add (40119,"Payne County", "OK", "Oklahoma", "7", -6:1, 65109, 1148, 1, 0, 0);
add (55105,"Rock County", "WI", "Wisconnsin", "5", -6:1, 150736, 1148, 1, 0, 0);
add (08081,"Moffat County", "CO", "Colorado", "8", -7:1, 12535, 4647, 1, 0, 0);
add (48261,"Kenedy County", "TX", "Texas", "7", -6:1, 438, 1148, 1, 0, 0);
add (51710,"Norfolk city", "VA", "Virginia", "2", -5:1, 215215, 1387, 1, 0, 0);
add (29229,"Wright County", "MO", "Mosourri", "6", -6:1, 19578, 1148, 1, 0, 0);
add (30107,"Wheatland County", "MT", "Montana", "6", -7:1, 2373, 4647, 1, 0, 0);
add (37029,"Camden County", "NC", "North Carolina", "2", -5:1, 6878, 1387, 1, 0, 0);
add (54027,"Hampshire County", "WV", "West Virginia", "2", -5:1, 19041, 1387, 1, 0, 0);
add (17055,"Franklin County", "IL", "Illinois", "6", -6:1, 40476, 1148, 1, 0, 0);
add (48441,"Taylor County", "TX", "Texas", "7", -6:1, 122016, 1148, 1, 0, 0);
add (48203,"Harrison County", "TX", "Texas", "7", -6:1, 59773, 1148, 1, 0, 0);
add (19049,"Dallas County", "IA", "Iowa", "5", -6:1, 36900, 1148, 1, 0, 0);
add (44001,"Bristol County", "RI", "Rhode Island", "0", -5:1, 49114, 1387, 1, 0, 0);
add (48181,"Grayson County", "TX", "Texas", "7", -6:1, 102815, 1148, 1, 0, 0);
add (05119,"Pulaski County", "AR", "Arkansas", "7", -6:1, 350345, 1148, 1, 0, 0);
add (48007,"Aransas County", "TX", "Texas", "7", -6:1, 22910, 1148, 1, 0, 0);
add (29045,"Clark County", "MO", "Mosourri", "6", -6:1, 7467, 1148, 1, 0, 0);
add (26047,"Emmet County", "MI", "Michigan", "4", -5:1, 28677, 1387, 1, 0, 0);
add (17195,"Whiteside County", "IL", "Illinois", "6", -6:1, 59623, 1148, 1, 0, 0);
add (06019,"Fresno County", "CA", "California", "9", -8:1, 755730, 3240, 0, 1, 0);
add (48319,"Mason County", "TX", "Texas", "7", -6:1, 3692, 1148, 1, 0, 0);
add (51117,"Mecklenburg County", "VA", "Virginia", "2", -5:1, 31047, 1387, 1, 0, 0);
add (31093,"Howard County", "NE", "Nebraska", "6", -7:1, 6458, 4647, 1, 0, 0);
add (36031,"Essex County", "NY", "New York", "1", -5:1, 37548, 1387, 1, 0, 0);
add (26129,"Ogemaw County", "MI", "Michigan", "4", -5:1, 21193, 1387, 1, 0, 0);
add (18055,"Greene County", "IN", "Indiana", "4", -5:1, 33467, 1387, 1, 0, 0);
add (31151,"Saline County", "NE", "Nebraska", "6", -7:1, 12966, 4647, 1, 0, 0);
add (20019,"Chautauqua County", "KS", "Kansas", "6", -6:1, 4360, 1148, 1, 0, 0);
add (48507,"Zavala County", "TX", "Texas", "7", -6:1, 11927, 1148, 1, 0, 0);
add (21127,"Lawrence County", "KY", "Kentucky", "4", -5:1, 15647, 1387, 1, 0, 0);
add (48005,"Angelina County", "TX", "Texas", "7", -6:1, 77351, 1148, 1, 0, 0);
add (29077,"Greene County", "MO", "Mosourri", "6", -6:1, 226758, 1148, 1, 0, 0);
add (27003,"Anoka County", "MN", "Minnesota", "5", -6:1, 292181, 1148, 1, 0, 0);
add (51017,"Bath County", "VA", "Virginia", "2", -5:1, 4891, 1387, 1, 0, 0);
add (06071,"San Bernardino County", "CA", "California", "9", -8:1, 1635234, 3240, 0, 1, 0);
add (13289,"Twiggs County", "GA", "Georgia", "3", -5:1, 10126, 1387, 1, 0, 0);
add (33003,"Carroll County", "NH", "New Hampshire", "0", -5:1, 39346, 1387, 1, 0, 0);
add (48143,"Erath County", "TX", "Texas", "7", -6:1, 31562, 1148, 1, 0, 0);
add (45045,"Greenville County", "SC", "South Carolina", "2", -5:1, 353845, 1387, 1, 0, 0);
add (20087,"Jefferson County", "KS", "Kansas", "6", -6:1, 18243, 1148, 1, 0, 0);
add (49015,"Emery County", "UT", "Utah", "8", -7:1, 10989, 4647, 1, 0, 0);
add (48479,"Webb County", "TX", "Texas", "7", -6:1, 188166, 1148, 1, 0, 0);
add (39027,"Clinton County", "OH", "Ohio", "4", -5:1, 39979, 1387, 1, 0, 0);
add (21033,"Caldwell County", "KY", "Kentucky", "4", -6:1, 13314, 1148, 1, 0, 0);
add (06003,"Alpine County", "CA", "California", "9", -8:1, 1209, 3240, 0, 1, 0);
add (47005,"Benton County", "TN", "Tennesee", "3", -5:1, 16328, 1387, 1, 0, 0);
add (18183,"Whitley County", "IN", "Indiana", "4", -5:1, 30459, 1387, 1, 0, 0);
add (22007,"Assumption Parish", "LA", "Louisiana", "7", -6:1, 23015, 1148, 1, 0, 0);
add (48385,"Real County", "TX", "Texas", "7", -6:1, 2687, 1148, 1, 0, 0);
add (12017,"Citrus County", "FL", "Florida", "3", -5:1, 114068, 1387, 1, 0, 0);
add (29149,"Oregon County", "MO", "Mosourri", "6", -6:1, 10164, 1148, 1, 0, 0);
add (08097,"Pitkin County", "CO", "Colorado", "8", -7:1, 13423, 4647, 1, 0, 0);
add (18093,"Lawrence County", "IN", "Indiana", "4", -5:1, 45615, 1387, 1, 0, 0);
add (36063,"Niagara County", "NY", "New York", "1", -5:1, 218070, 1387, 1, 0, 0);
add (51053,"Dinwiddie County", "VA", "Virginia", "2", -5:1, 24657, 1387, 1, 0, 0);
add (05101,"Newton County", "AR", "Arkansas", "7", -6:1, 8180, 1148, 1, 0, 0);
add (12127,"Volusia County", "FL", "Florida", "3", -5:1, 423409, 1387, 1, 0, 0);
add (37089,"Henderson County", "NC", "North Carolina", "2", -5:1, 80822, 1387, 1, 0, 0);
add (54015,"Clay County", "WV", "West Virginia", "2", -5:1, 10530, 1387, 1, 0, 0);
add (27007,"Beltrami County", "MN", "Minnesota", "5", -6:1, 38729, 1148, 1, 0, 0);
add (29039,"Cedar County", "MO", "Mosourri", "6", -6:1, 13215, 1148, 1, 0, 0);
add (32027,"Pershing County", "NV", "Nevada", "8", -8:1, 5434, 3240, 0, 1, 0);
add (09011,"New London County", "CT", "Connecticut", "0", -5:1, 245740, 1387, 1, 0, 0);
add (27095,"Mille Lacs County", "MN", "Minnesota", "5", -6:1, 21044, 1148, 1, 0, 0);
add (46047,"Fall River County", "SD", "South Dakota", "5", -6:1, 7133, 1148, 1, 0, 0);
add (41025,"Harney County", "OR", "Oregon", "9", -8:1, 7198, 3240, 0, 1, 0);
add (49049,"Utah County", "UT", "Utah", "8", -7:1, 335635, 4647, 1, 0, 0);
add (18161,"Union County", "IN", "Indiana", "4", -5:1, 7263, 1387, 1, 0, 0);
add (02013,"Aleutians East Borough", "AK", "Alaska", "9", -9:1, 2253, 1174, 0, 0, 1);
add (01085,"Lowndes County", "AL", "Alabama", "3", -6:1, 12984, 1148, 1, 0, 0);
add (19029,"Cass County", "IA", "Iowa", "5", -6:1, 14591, 1148, 1, 0, 0);
add (50019,"Orleans County", "VT", "Vermont", "0", -5:1, 25296, 1387, 1, 0, 0);
add (37045,"Cleveland County", "NC", "North Carolina", "2", -5:1, 92753, 1387, 1, 0, 0);
add (12081,"Manatee County", "FL", "Florida", "3", -5:1, 239682, 1387, 1, 0, 0);
add (37155,"Robeson County", "NC", "North Carolina", "2", -5:1, 115589, 1387, 1, 0, 0);
add (20111,"Lyon County", "KS", "Kansas", "6", -6:1, 33920, 1148, 1, 0, 0);
add (38079,"Rolette County", "ND", "North Dakota", "5", -6:1, 14219, 1148, 1, 0, 0);
add (06095,"Solano County", "CA", "California", "9", -8:1, 377415, 3240, 0, 1, 0);
add (47035,"Cumberland County", "TN", "Tennesee", "3", -5:1, 44291, 1387, 1, 0, 0);
add (05137,"Stone County", "AR", "Arkansas", "7", -6:1, 11154, 1148, 1, 0, 0);
add (48241,"Jasper County", "TX", "Texas", "7", -6:1, 33437, 1148, 1, 0, 0);
add (28029,"Copiah County", "MS", "Missippi", "5", -6:1, 28944, 1148, 1, 0, 0);
add (20179,"Sheridan County", "KS", "Kansas", "6", -6:1, 2741, 1148, 1, 0, 0);
add (53007,"Chelan County", "WA", "Washiington", "9", -8:1, 60052, 3240, 0, 1, 0);
add (48177,"Gonzales County", "TX", "Texas", "7", -6:1, 17551, 1148, 1, 0, 0);
add (53013,"Columbia County", "WA", "Washiington", "9", -8:1, 4156, 3240, 0, 1, 0);
add (53073,"Whatcom County", "WA", "Washiington", "9", -8:1, 156830, 3240, 0, 1, 0);
add (48481,"Wharton County", "TX", "Texas", "7", -6:1, 40133, 1148, 1, 0, 0);
add (27105,"Nobles County", "MN", "Minnesota", "5", -6:1, 19312, 1148, 1, 0, 0);
add (48161,"Freestone County", "TX", "Texas", "7", -6:1, 17675, 1148, 1, 0, 0);
add (49013,"Duchesne County", "UT", "Utah", "8", -7:1, 14481, 4647, 1, 0, 0);
add (55067,"Langlade County", "WI", "Wisconnsin", "5", -6:1, 20466, 1148, 1, 0, 0);
add (45019,"Charleston County", "SC", "South Carolina", "2", -5:1, 316482, 1387, 1, 0, 0);
add (47063,"Hamblen County", "TN", "Tennesee", "3", -5:1, 54050, 1387, 1, 0, 0);
add (48281,"Lampasas County", "TX", "Texas", "7", -6:1, 17775, 1148, 1, 0, 0);
add (20205,"Wilson County", "KS", "Kansas", "6", -6:1, 10218, 1148, 1, 0, 0);
add (26161,"Washtenaw County", "MI", "Michigan", "4", -5:1, 303069, 1387, 1, 0, 0);
add (54013,"Calhoun County", "WV", "West Virginia", "2", -5:1, 7940, 1387, 1, 0, 0);
add (17007,"Boone County", "IL", "Illinois", "6", -6:1, 38734, 1148, 1, 0, 0);
add (37051,"Cumberland County", "NC", "North Carolina", "2", -5:1, 284629, 1387, 1, 0, 0);
add (28037,"Franklin County", "MS", "Missippi", "5", -6:1, 8319, 1148, 1, 0, 0);
add (27023,"Chippewa County", "MN", "Minnesota", "5", -6:1, 13053, 1148, 1, 0, 0);
add (08053,"Hinsdale County", "CO", "Colorado", "8", -7:1, 737, 4647, 1, 0, 0);
add (46109,"Roberts County", "SD", "South Dakota", "5", -7:1, 9786, 4647, 1, 0, 0);
add (08027,"Custer County", "CO", "Colorado", "8", -7:1, 3449, 4647, 1, 0, 0);
add (40093,"Major County", "OK", "Oklahoma", "7", -6:1, 7829, 1148, 1, 0, 0);
add (26109,"Menominee County", "MI", "Michigan", "4", -5:1, 24468, 1387, 1, 0, 0);
add (34037,"Sussex County", "NJ", "New Jersey", "0", -5:1, 143030, 1387, 1, 0, 0);
add (24027,"Howard County", "MD", "Maryland", "2", -5:1, 236388, 1387, 1, 0, 0);
add (37137,"Pamlico County", "NC", "North Carolina", "2", -5:1, 12345, 1387, 1, 0, 0);
add (47157,"Shelby County", "TN", "Tennesee", "3", -6:1, 868825, 1148, 1, 0, 0);
add (38037,"Grant County", "ND", "North Dakota", "5", -6:1, 2969, 1148, 1, 0, 0);
add (41033,"Josephine County", "OR", "Oregon", "9", -8:1, 74377, 3240, 0, 1, 0);
add (42003,"Allegheny County", "PA", "Pennsylvania", "1", -5:1, 1268446, 1387, 1, 0, 0);
add (51620,"Franklin city", "VA", "Virginia", "2", -5:1, 8685, 1387, 1, 0, 0);
add (05085,"Lonoke County", "AR", "Arkansas", "7", -6:1, 50156, 1148, 1, 0, 0);
add (40021,"Cherokee County", "OK", "Oklahoma", "7", -6:1, 39138, 1148, 1, 0, 0);
add (45089,"Williamsburg County", "SC", "South Carolina", "2", -5:1, 37121, 1387, 1, 0, 0);
add (51059,"Fairfax County", "VA", "Virginia", "2", -5:1, 929239, 1387, 1, 0, 0);
add (08039,"Elbert County", "CO", "Colorado", "8", -7:1, 18600, 4647, 1, 0, 0);
add (05051,"Garland County", "AR", "Arkansas", "7", -6:1, 83976, 1148, 1, 0, 0);
add (21181,"Nicholas County", "KY", "Kentucky", "4", -5:1, 6998, 1387, 1, 0, 0);
add (19037,"Chickasaw County", "IA", "Iowa", "5", -6:1, 13441, 1148, 1, 0, 0);
add (48285,"Lavaca County", "TX", "Texas", "7", -6:1, 18813, 1148, 1, 0, 0);
add (20177,"Shawnee County", "KS", "Kansas", "6", -6:1, 165348, 1148, 1, 0, 0);
add (39123,"Ottawa County", "OH", "Ohio", "4", -5:1, 40983, 1387, 1, 0, 0);
add (18151,"Steuben County", "IN", "Indiana", "4", -5:1, 31450, 1387, 1, 0, 0);
add (17121,"Marion County", "IL", "Illinois", "6", -6:1, 41883, 1148, 1, 0, 0);
add (17059,"Gallatin County", "IL", "Illinois", "6", -6:1, 6642, 1148, 1, 0, 0);
add (56007,"Carbon County", "WY", "Wyoming", "8", -7:1, 15575, 4647, 1, 0, 0);
add (51595,"Emporia city", "VA", "Virginia", "2", -5:1, 5474, 1387, 1, 0, 0);
add (29085,"Hickory County", "MO", "Mosourri", "6", -6:1, 8617, 1148, 1, 0, 0);
add (08017,"Cheyenne County", "CO", "Colorado", "8", -7:1, 2346, 4647, 1, 0, 0);
add (46025,"Clark County", "SD", "South Dakota", "5", -6:1, 4337, 1148, 1, 0, 0);
add (39001,"Adams County", "OH", "Ohio", "4", -5:1, 28587, 1387, 1, 0, 0);
add (18033,"De Kalb County", "IN", "Indiana", "4", -5:1, 39330, 1387, 1, 0, 0);
add (29041,"Chariton County", "MO", "Mosourri", "6", -6:1, 8621, 1148, 1, 0, 0);
add (40089,"McCurtain County", "OK", "Oklahoma", "7", -6:1, 34783, 1148, 1, 0, 0);
add (20189,"Stevens County", "KS", "Kansas", "6", -6:1, 5371, 1148, 1, 0, 0);
add (48267,"Kimble County", "TX", "Texas", "7", -6:1, 4124, 1148, 1, 0, 0);
add (05037,"Cross County", "AR", "Arkansas", "7", -6:1, 19564, 1148, 1, 0, 0);
add (41013,"Crook County", "OR", "Oregon", "9", -8:1, 17236, 3240, 0, 1, 0);
add (13163,"Jefferson County", "GA", "Georgia", "3", -5:1, 17767, 1387, 1, 0, 0);
add (48313,"Madison County", "TX", "Texas", "7", -6:1, 11889, 1148, 1, 0, 0);
add (36077,"Otsego County", "NY", "New York", "1", -5:1, 60788, 1387, 1, 0, 0);
add (51007,"Amelia County", "VA", "Virginia", "2", -5:1, 10367, 1387, 1, 0, 0);
add (40007,"Beaver County", "OK", "Oklahoma", "7", -6:1, 6056, 1148, 1, 0, 0);
add (13085,"Dawson County", "GA", "Georgia", "3", -5:1, 14851, 1387, 1, 0, 0);
add (48049,"Brown County", "TX", "Texas", "7", -6:1, 37051, 1148, 1, 0, 0);
add (30021,"Dawson County", "MT", "Montana", "6", -7:1, 8849, 4647, 1, 0, 0);
add (50015,"Lamoille County", "VT", "Vermont", "0", -5:1, 21597, 1387, 1, 0, 0);
add (29099,"Jefferson County", "MO", "Mosourri", "6", -6:1, 195675, 1148, 1, 0, 0);
add (48263,"Kent County", "TX", "Texas", "7", -6:1, 880, 1148, 1, 0, 0);
add (41071,"Yamhill County", "OR", "Oregon", "9", -8:1, 82085, 3240, 0, 1, 0);
add (42027,"Centre County", "PA", "Pennsylvania", "1", -5:1, 132700, 1387, 1, 0, 0);
add (13147,"Hart County", "GA", "Georgia", "3", -5:1, 21833, 1387, 1, 0, 0);
add (19003,"Adams County", "IA", "Iowa", "5", -6:1, 4352, 1148, 1, 0, 0);
add (31077,"Greeley County", "NE", "Nebraska", "6", -6:1, 2850, 1148, 1, 0, 0);
add (50003,"Bennington County", "VT", "Vermont", "0", -5:1, 35968, 1387, 1, 0, 0);
add (19053,"Decatur County", "IA", "Iowa", "5", -6:1, 8220, 1148, 1, 0, 0);
add (48133,"Eastland County", "TX", "Texas", "7", -6:1, 17591, 1148, 1, 0, 0);
add (12071,"Lee County", "FL", "Florida", "3", -5:1, 392895, 1387, 1, 0, 0);
add (13043,"Candler County", "GA", "Georgia", "3", -5:1, 9078, 1387, 1, 0, 0);
add (05057,"Hempstead County", "AR", "Arkansas", "7", -6:1, 22113, 1148, 1, 0, 0);
add (18025,"Crawford County", "IN", "Indiana", "4", -5:1, 10582, 1387, 1, 0, 0);
add (26079,"Kalkaska County", "MI", "Michigan", "4", -5:1, 15568, 1387, 1, 0, 0);
add (31183,"Wheeler County", "NE", "Nebraska", "6", -7:1, 925, 4647, 1, 0, 0);
add (25001,"Barnstable County", "MA", "Massachusetts", "0", -5:1, 208418, 1387, 1, 0, 0);
add (53023,"Garfield County", "WA", "Washiington", "9", -8:1, 2330, 3240, 0, 1, 0);
add (38103,"Wells County", "ND", "North Dakota", "5", -6:1, 5200, 1148, 1, 0, 0);
add (18133,"Putnam County", "IN", "Indiana", "4", -5:1, 34468, 1387, 1, 0, 0);
add (21207,"Russell County", "KY", "Kentucky", "4", -5:1, 16233, 1387, 1, 0, 0);
add (53067,"Thurston County", "WA", "Washiington", "9", -8:1, 202255, 3240, 0, 1, 0);
add (06083,"Santa Barbara County", "CA", "California", "9", -8:1, 389502, 3240, 0, 1, 0);
add (18027,"Daviess County", "IN", "Indiana", "4", -5:1, 28987, 1387, 1, 0, 0);
add (48185,"Grimes County", "TX", "Texas", "7", -6:1, 23293, 1148, 1, 0, 0);
add (17139,"Moultrie County", "IL", "Illinois", "6", -6:1, 14410, 1148, 1, 0, 0);
add (20113,"McPherson County", "KS", "Kansas", "6", -6:1, 28630, 1148, 1, 0, 0);
add (40031,"Comanche County", "OK", "Oklahoma", "7", -6:1, 113508, 1148, 1, 0, 0);
add (18057,"Hamilton County", "IN", "Indiana", "4", -5:1, 162597, 1387, 1, 0, 0);
add (29173,"Ralls County", "MO", "Mosourri", "6", -6:1, 8813, 1148, 1, 0, 0);
add (17171,"Scott County", "IL", "Illinois", "6", -6:1, 5610, 1148, 1, 0, 0);
add (48335,"Mitchell County", "TX", "Texas", "7", -6:1, 9708, 1148, 1, 0, 0);
add (37187,"Washington County", "NC", "North Carolina", "2", -5:1, 13615, 1387, 1, 0, 0);
add (37163,"Sampson County", "NC", "North Carolina", "2", -5:1, 52438, 1387, 1, 0, 0);
add (48231,"Hunt County", "TX", "Texas", "7", -6:1, 70893, 1148, 1, 0, 0);
add (56029,"Park County", "WY", "Wyoming", "8", -7:1, 25782, 4647, 1, 0, 0);
add (51678,"Lexington city", "VA", "Virginia", "2", -5:1, 7360, 1387, 1, 0, 0);
add (41043,"Linn County", "OR", "Oregon", "9", -8:1, 104464, 3240, 0, 1, 0);
add (31123,"Morrill County", "NE", "Nebraska", "6", -7:1, 5455, 4647, 1, 0, 0);
add (48211,"Hemphill County", "TX", "Texas", "7", -6:1, 3529, 1148, 1, 0, 0);
add (22079,"Rapides Parish", "LA", "Louisiana", "7", -6:1, 126763, 1148, 1, 0, 0);
add (28087,"Lowndes County", "MS", "Missippi", "5", -6:1, 61208, 1148, 1, 0, 0);
add (37063,"Durham County", "NC", "North Carolina", "2", -5:1, 202411, 1387, 1, 0, 0);
add (29019,"Boone County", "MO", "Mosourri", "6", -6:1, 129098, 1148, 1, 0, 0);
add (48493,"Wilson County", "TX", "Texas", "7", -6:1, 31423, 1148, 1, 0, 0);
add (20157,"Republic County", "KS", "Kansas", "6", -6:1, 6102, 1148, 1, 0, 0);
add (41039,"Lane County", "OR", "Oregon", "9", -8:1, 314068, 3240, 0, 1, 0);
add (54029,"Hancock County", "WV", "West Virginia", "2", -5:1, 33973, 1387, 1, 0, 0);
add (48483,"Wheeler County", "TX", "Texas", "7", -6:1, 5293, 1148, 1, 0, 0);
add (48113,"Dallas County", "TX", "Texas", "7", -6:1, 2050865, 1148, 1, 0, 0);
add (28091,"Marion County", "MS", "Missippi", "5", -6:1, 26386, 1148, 1, 0, 0);
add (29155,"Pemiscot County", "MO", "Mosourri", "6", -6:1, 21516, 1148, 1, 0, 0);
add (16001,"Ada County", "ID", "Idaho", "8", -7:1, 275687, 4647, 1, 0, 0);
add (16053,"Jerome County", "ID", "Idaho", "8", -7:1, 17962, 4647, 1, 0, 0);
add (06065,"Riverside County", "CA", "California", "9", -8:1, 1478838, 3240, 0, 1, 0);
add (18029,"Dearborn County", "IN", "Indiana", "4", -5:1, 47206, 1387, 1, 0, 0);
add (42079,"Luzerne County", "PA", "Pennsylvania", "1", -5:1, 313767, 1387, 1, 0, 0);
add (55003,"Ashland County", "WI", "Wisconnsin", "5", -6:1, 16474, 1148, 1, 0, 0);
add (18105,"Monroe County", "IN", "Indiana", "4", -5:1, 115130, 1387, 1, 0, 0);
add (21081,"Grant County", "KY", "Kentucky", "4", -6:1, 20347, 1148, 1, 0, 0);
add (46065,"Hughes County", "SD", "South Dakota", "5", -7:1, 15373, 4647, 1, 0, 0);
add (45027,"Clarendon County", "SC", "South Carolina", "2", -5:1, 30814, 1387, 1, 0, 0);
add (20181,"Sherman County", "KS", "Kansas", "6", -6:1, 6511, 1148, 1, 0, 0);
add (27145,"Stearns County", "MN", "Minnesota", "5", -6:1, 128094, 1148, 1, 0, 0);
add (39069,"Henry County", "OH", "Ohio", "4", -5:1, 29923, 1387, 1, 0, 0);
add (26003,"Alger County", "MI", "Michigan", "4", -5:1, 9887, 1387, 1, 0, 0);
add (05133,"Sevier County", "AR", "Arkansas", "7", -6:1, 14623, 1148, 1, 0, 0);
add (54101,"Webster County", "WV", "West Virginia", "2", -5:1, 10230, 1387, 1, 0, 0);
add (51630,"Fredericksburg city", "VA", "Virginia", "2", -5:1, 21686, 1387, 1, 0, 0);
add (17085,"Jo Daviess County", "IL", "Illinois", "6", -6:1, 21468, 1148, 1, 0, 0);
add (48165,"Gaines County", "TX", "Texas", "7", -6:1, 14992, 1148, 1, 0, 0);
add (20115,"Marion County", "KS", "Kansas", "6", -6:1, 13593, 1148, 1, 0, 0);
add (41055,"Sherman County", "OR", "Oregon", "9", -8:1, 1789, 3240, 0, 1, 0);
add (13115,"Floyd County", "GA", "Georgia", "3", -5:1, 85185, 1387, 1, 0, 0);
add (21089,"Greenup County", "KY", "Kentucky", "4", -6:1, 36874, 1148, 1, 0, 0);
add (06087,"Santa Cruz County", "CA", "California", "9", -8:1, 242994, 3240, 0, 1, 0);
add (27139,"Scott County", "MN", "Minnesota", "5", -6:1, 79031, 1148, 1, 0, 0);
add (47077,"Henderson County", "TN", "Tennesee", "3", -5:1, 24424, 1387, 1, 0, 0);
add (53055,"San Juan County", "WA", "Washiington", "9", -8:1, 12493, 3240, 0, 1, 0);
add (35006,"Cibola County", "NM", "New Mexico", "8", -7:1, 26250, 4647, 1, 0, 0);
add (19193,"Woodbury County", "IA", "Iowa", "5", -6:1, 101672, 1148, 1, 0, 0);
add (46073,"Jerauld County", "SD", "South Dakota", "5", -7:1, 2222, 4647, 1, 0, 0);
add (27153,"Todd County", "MN", "Minnesota", "5", -6:1, 24020, 1148, 1, 0, 0);
add (48065,"Carson County", "TX", "Texas", "7", -6:1, 6696, 1148, 1, 0, 0);
add (47161,"Stewart County", "TN", "Tennesee", "3", -6:1, 11545, 1148, 1, 0, 0);
add (42093,"Montour County", "PA", "Pennsylvania", "1", -5:1, 17730, 1387, 1, 0, 0);
add (18053,"Grant County", "IN", "Indiana", "4", -5:1, 72570, 1387, 1, 0, 0);
add (26019,"Benzie County", "MI", "Michigan", "4", -5:1, 14678, 1387, 1, 0, 0);
add (29141,"Morgan County", "MO", "Mosourri", "6", -6:1, 18434, 1148, 1, 0, 0);
add (13025,"Brantley County", "GA", "Georgia", "3", -5:1, 13571, 1387, 1, 0, 0);
add (13301,"Warren County", "GA", "Georgia", "3", -5:1, 6059, 1387, 1, 0, 0);
add (54077,"Preston County", "WV", "West Virginia", "2", -5:1, 29811, 1387, 1, 0, 0);
add (46033,"Custer County", "SD", "South Dakota", "5", -6:1, 6930, 1148, 1, 0, 0);
add (22005,"Ascension Parish", "LA", "Louisiana", "7", -6:1, 71628, 1148, 1, 0, 0);
add (08055,"Huerfano County", "CO", "Colorado", "8", -7:1, 6813, 4647, 1, 0, 0);
add (47107,"McMinn County", "TN", "Tennesee", "3", -6:1, 46283, 1148, 1, 0, 0);
add (01027,"Clay County", "AL", "Alabama", "3", -6:1, 13970, 1148, 1, 0, 0);
add (48303,"Lubbock County", "TX", "Texas", "7", -6:1, 229475, 1148, 1, 0, 0);
add (08123,"Weld County", "CO", "Colorado", "8", -7:1, 159429, 4647, 1, 0, 0);
add (28135,"Tallahatchie County", "MS", "Missippi", "5", -6:1, 14893, 1148, 1, 0, 0);
add (08023,"Costilla County", "CO", "Colorado", "8", -7:1, 3641, 4647, 1, 0, 0);
add (48279,"Lamb County", "TX", "Texas", "7", -6:1, 14760, 1148, 1, 0, 0);
add (29101,"Johnson County", "MO", "Mosourri", "6", -6:1, 47644, 1148, 1, 0, 0);
add (40045,"Ellis County", "OK", "Oklahoma", "7", -6:1, 4291, 1148, 1, 0, 0);
add (55121,"Trempealeau County", "WI", "Wisconnsin", "5", -6:1, 26469, 1148, 1, 0, 0);
add (22119,"Webster Parish", "LA", "Louisiana", "7", -6:1, 42707, 1148, 1, 0, 0);
add (19021,"Buena Vista County", "IA", "Iowa", "5", -6:1, 19454, 1148, 1, 0, 0);
add (47015,"Cannon County", "TN", "Tennesee", "3", -5:1, 12139, 1387, 1, 0, 0);
add (21173,"Montgomery County", "KY", "Kentucky", "4", -5:1, 20932, 1387, 1, 0, 0);
add (47169,"Trousdale County", "TN", "Tennesee", "3", -6:1, 6844, 1148, 1, 0, 0);
add (31049,"Deuel County", "NE", "Nebraska", "6", -6:1, 2029, 1148, 1, 0, 0);
add (19169,"Story County", "IA", "Iowa", "5", -6:1, 75268, 1148, 1, 0, 0);
add (17157,"Randolph County", "IL", "Illinois", "6", -6:1, 33489, 1148, 1, 0, 0);
add (17037,"DeKalb County", "IL", "Illinois", "6", -6:1, 84169, 1148, 1, 0, 0);
add (40067,"Jefferson County", "OK", "Oklahoma", "7", -6:1, 6583, 1148, 1, 0, 0);
add (47185,"White County", "TN", "Tennesee", "3", -6:1, 22708, 1148, 1, 0, 0);
add (05089,"Marion County", "AR", "Arkansas", "7", -6:1, 14918, 1148, 1, 0, 0);
add (20083,"Hodgeman County", "KS", "Kansas", "6", -6:1, 2209, 1148, 1, 0, 0);
add (13135,"Gwinnett County", "GA", "Georgia", "3", -5:1, 522095, 1387, 1, 0, 0);
add (05095,"Monroe County", "AR", "Arkansas", "7", -6:1, 10200, 1148, 1, 0, 0);
add (45083,"Spartanburg County", "SC", "South Carolina", "2", -5:1, 247458, 1387, 1, 0, 0);
add (30055,"McCone County", "MT", "Montana", "6", -7:1, 1964, 4647, 1, 0, 0);
add (05079,"Lincoln County", "AR", "Arkansas", "7", -6:1, 14274, 1148, 1, 0, 0);
add (30105,"Valley County", "MT", "Montana", "6", -7:1, 8195, 4647, 1, 0, 0);
add (26077,"Kalamazoo County", "MI", "Michigan", "4", -5:1, 229660, 1387, 1, 0, 0);
add (06073,"San Diego County", "CA", "California", "9", -8:1, 2780592, 3240, 0, 1, 0);
add (20135,"Ness County", "KS", "Kansas", "6", -6:1, 3607, 1148, 1, 0, 0);
add (31175,"Valley County", "NE", "Nebraska", "6", -7:1, 4602, 4647, 1, 0, 0);
add (19065,"Fayette County", "IA", "Iowa", "5", -6:1, 21761, 1148, 1, 0, 0);
add (47091,"Johnson County", "TN", "Tennesee", "3", -6:1, 16755, 1148, 1, 0, 0);
add (05007,"Benton County", "AR", "Arkansas", "7", -6:1, 134162, 1148, 1, 0, 0);
add (46031,"Corson County", "SD", "South Dakota", "5", -6:1, 4190, 1148, 1, 0, 0);
add (45009,"Bamberg County", "SC", "South Carolina", "2", -5:1, 16498, 1387, 1, 0, 0);
add (31137,"Phelps County", "NE", "Nebraska", "6", -7:1, 9908, 4647, 1, 0, 0);
add (01011,"Bullock County", "AL", "Alabama", "3", -6:1, 11311, 1148, 1, 0, 0);
add (06005,"Amador County", "CA", "California", "9", -8:1, 33334, 3240, 0, 1, 0);
add (29159,"Pettis County", "MO", "Mosourri", "6", -6:1, 37069, 1148, 1, 0, 0);
add (13271,"Telfair County", "GA", "Georgia", "3", -5:1, 11558, 1387, 1, 0, 0);
add (19101,"Jefferson County", "IA", "Iowa", "5", -6:1, 17113, 1148, 1, 0, 0);
add (17191,"Wayne County", "IL", "Illinois", "6", -6:1, 16989, 1148, 1, 0, 0);
add (20079,"Harvey County", "KS", "Kansas", "6", -6:1, 34361, 1148, 1, 0, 0);
add (22019,"Calcasieu Parish", "LA", "Louisiana", "7", -6:1, 180330, 1148, 1, 0, 0);
add (46095,"Mellette County", "SD", "South Dakota", "5", -7:1, 2029, 4647, 1, 0, 0);
add (21227,"Warren County", "KY", "Kentucky", "4", -5:1, 87323, 1387, 1, 0, 0);
add (30099,"Teton County", "MT", "Montana", "6", -7:1, 6333, 4647, 1, 0, 0);
add (27055,"Houston County", "MN", "Minnesota", "5", -6:1, 19267, 1148, 1, 0, 0);
add (33017,"Strafford County", "NH", "New Hampshire", "0", -5:1, 108650, 1387, 1, 0, 0);
add (47159,"Smith County", "TN", "Tennesee", "3", -6:1, 16368, 1148, 1, 0, 0);
add (44007,"Providence County", "RI", "Rhode Island", "0", -5:1, 574038, 1387, 1, 0, 0);
add (16015,"Boise County", "ID", "Idaho", "8", -7:1, 5114, 4647, 1, 0, 0);
add (56019,"Johnson County", "WY", "Wyoming", "8", -7:1, 6824, 4647, 1, 0, 0);
add (01067,"Henry County", "AL", "Alabama", "3", -6:1, 15836, 1148, 1, 0, 0);
add (30101,"Toole County", "MT", "Montana", "6", -7:1, 4727, 4647, 1, 0, 0);
add (51177,"Spotsylvania County", "VA", "Virginia", "2", -5:1, 83692, 1387, 1, 0, 0);
add (21029,"Bullitt County", "KY", "Kentucky", "4", -6:1, 59304, 1148, 1, 0, 0);
add (40111,"Okmulgee County", "OK", "Oklahoma", "7", -6:1, 38860, 1148, 1, 0, 0);
add (12123,"Taylor County", "FL", "Florida", "3", -5:1, 18849, 1387, 1, 0, 0);
add (51135,"Nottoway County", "VA", "Virginia", "2", -5:1, 14999, 1387, 1, 0, 0);
add (29133,"Mississippi County", "MO", "Mosourri", "6", -6:1, 13395, 1148, 1, 0, 0);
add (01129,"Washington County", "AL", "Alabama", "3", -6:1, 17677, 1148, 1, 0, 0);
add (08035,"Douglas County", "CO", "Colorado", "8", -7:1, 140975, 4647, 1, 0, 0);
add (55133,"Waukesha County", "WI", "Wisconnsin", "5", -6:1, 353110, 1148, 1, 0, 0);
add (47131,"Obion County", "TN", "Tennesee", "3", -6:1, 32219, 1148, 1, 0, 0);
add (42061,"Huntingdon County", "PA", "Pennsylvania", "1", -5:1, 44599, 1387, 1, 0, 0);
add (46013,"Brown County", "SD", "South Dakota", "5", -6:1, 35433, 1148, 1, 0, 0);
add (17187,"Warren County", "IL", "Illinois", "6", -6:1, 18824, 1148, 1, 0, 0);
add (34041,"Warren County", "NJ", "New Jersey", "0", -5:1, 98600, 1387, 1, 0, 0);
add (28043,"Grenada County", "MS", "Missippi", "5", -6:1, 22427, 1148, 1, 0, 0);
add (36079,"Putnam County", "NY", "New York", "1", -5:1, 93358, 1387, 1, 0, 0);
add (42067,"Juniata County", "PA", "Pennsylvania", "1", -5:1, 22101, 1387, 1, 0, 0);
add (20109,"Logan County", "KS", "Kansas", "6", -6:1, 2987, 1148, 1, 0, 0);
add (05115,"Pope County", "AR", "Arkansas", "7", -6:1, 52059, 1148, 1, 0, 0);
add (13029,"Bryan County", "GA", "Georgia", "3", -5:1, 23482, 1387, 1, 0, 0);
add (49039,"Sanpete County", "UT", "Utah", "8", -7:1, 21452, 4647, 1, 0, 0);
add (29011,"Barton County", "MO", "Mosourri", "6", -6:1, 12078, 1148, 1, 0, 0);
add (20097,"Kiowa County", "KS", "Kansas", "6", -6:1, 3470, 1148, 1, 0, 0);
add (40081,"Lincoln County", "OK", "Oklahoma", "7", -6:1, 31361, 1148, 1, 0, 0);
add (48297,"Live Oak County", "TX", "Texas", "7", -6:1, 10137, 1148, 1, 0, 0);
add (48269,"King County", "TX", "Texas", "7", -6:1, 363, 1148, 1, 0, 0);
add (37121,"Mitchell County", "NC", "North Carolina", "2", -5:1, 14831, 1387, 1, 0, 0);
add (18043,"Floyd County", "IN", "Indiana", "4", -5:1, 71990, 1387, 1, 0, 0);
add (31115,"Loup County", "NE", "Nebraska", "6", -7:1, 666, 4647, 1, 0, 0);
add (48259,"Kendall County", "TX", "Texas", "7", -6:1, 21222, 1148, 1, 0, 0);
add (18095,"Madison County", "IN", "Indiana", "4", -5:1, 131360, 1387, 1, 0, 0);
add (27021,"Cass County", "MN", "Minnesota", "5", -6:1, 26465, 1148, 1, 0, 0);
add (18123,"Perry County", "IN", "Indiana", "4", -5:1, 19350, 1387, 1, 0, 0);
add (30033,"Garfield County", "MT", "Montana", "6", -7:1, 1393, 4647, 1, 0, 0);
add (12131,"Walton County", "FL", "Florida", "3", -5:1, 37410, 1387, 1, 0, 0);
add (16049,"Idaho County", "ID", "Idaho", "8", -7:1, 15066, 4647, 1, 0, 0);
add (45013,"Beaufort County", "SC", "South Carolina", "2", -5:1, 108959, 1387, 1, 0, 0);
add (20129,"Morton County", "KS", "Kansas", "6", -6:1, 3440, 1148, 1, 0, 0);
add (08033,"Dolores County", "CO", "Colorado", "8", -7:1, 1822, 4647, 1, 0, 0);
add (18169,"Wabash County", "IN", "Indiana", "4", -5:1, 34537, 1387, 1, 0, 0);
add (39175,"Wyandot County", "OH", "Ohio", "4", -5:1, 22826, 1387, 1, 0, 0);
add (12087,"Monroe County", "FL", "Florida", "3", -5:1, 81203, 1387, 1, 0, 0);
add (28131,"Stone County", "MS", "Missippi", "5", -6:1, 13166, 1148, 1, 0, 0);
add (18147,"Spencer County", "IN", "Indiana", "4", -5:1, 20937, 1387, 1, 0, 0);
add (17075,"Iroquois County", "IL", "Illinois", "6", -6:1, 31243, 1148, 1, 0, 0);
add (51043,"Clarke County", "VA", "Virginia", "2", -5:1, 12779, 1387, 1, 0, 0);
add (46059,"Hand County", "SD", "South Dakota", "5", -7:1, 4144, 4647, 1, 0, 0);
add (42035,"Clinton County", "PA", "Pennsylvania", "1", -5:1, 37000, 1387, 1, 0, 0);
add (17185,"Wabash County", "IL", "Illinois", "6", -6:1, 12630, 1148, 1, 0, 0);
add (39031,"Coshocton County", "OH", "Ohio", "4", -5:1, 36115, 1387, 1, 0, 0);
add (01111,"Randolph County", "AL", "Alabama", "3", -6:1, 19923, 1148, 1, 0, 0);
add (35028,"Los Alamos County", "NM", "New Mexico", "8", -7:1, 18344, 4647, 1, 0, 0);
add (28089,"Madison County", "MS", "Missippi", "5", -6:1, 72857, 1148, 1, 0, 0);
add (46117,"Stanley County", "SD", "South Dakota", "5", -7:1, 2929, 4647, 1, 0, 0);
add (13185,"Lowndes County", "GA", "Georgia", "3", -5:1, 85231, 1387, 1, 0, 0);
add (46023,"Charles Mix County", "SD", "South Dakota", "5", -6:1, 9337, 1148, 1, 0, 0);
add (19161,"Sac County", "IA", "Iowa", "5", -6:1, 11931, 1148, 1, 0, 0);
add (22011,"Beauregard Parish", "LA", "Louisiana", "7", -6:1, 31976, 1148, 1, 0, 0);
add (19157,"Poweshiek County", "IA", "Iowa", "5", -6:1, 18865, 1148, 1, 0, 0);
add (53041,"Lewis County", "WA", "Washiington", "9", -8:1, 68163, 3240, 0, 1, 0);
add (05055,"Greene County", "AR", "Arkansas", "7", -6:1, 36192, 1148, 1, 0, 0);
add (18153,"Sullivan County", "IN", "Indiana", "4", -5:1, 19270, 1387, 1, 0, 0);
add (55061,"Kewaunee County", "WI", "Wisconnsin", "5", -6:1, 19806, 1148, 1, 0, 0);
add (45015,"Berkeley County", "SC", "South Carolina", "2", -5:1, 136544, 1387, 1, 0, 0);
add (41017,"Deschutes County", "OR", "Oregon", "9", -8:1, 105640, 3240, 0, 1, 0);
add (21189,"Owsley County", "KY", "Kentucky", "4", -5:1, 5404, 1387, 1, 0, 0);
add (01029,"Cleburne County", "AL", "Alabama", "3", -6:1, 14308, 1148, 1, 0, 0);
add (42131,"Wyoming County", "PA", "Pennsylvania", "1", -5:1, 29149, 1387, 1, 0, 0);
add (06077,"San Joaquin County", "CA", "California", "9", -8:1, 550445, 3240, 0, 1, 0);
add (29005,"Atchison County", "MO", "Mosourri", "6", -6:1, 6999, 1148, 1, 0, 0);
add (31009,"Blaine County", "NE", "Nebraska", "6", -6:1, 578, 1148, 1, 0, 0);
add (37037,"Chatham County", "NC", "North Carolina", "2", -5:1, 45406, 1387, 1, 0, 0);
add (22039,"Evangeline Parish", "LA", "Louisiana", "7", -6:1, 34097, 1148, 1, 0, 0);
add (12099,"Palm Beach County", "FL", "Florida", "3", -5:1, 1032625, 1387, 1, 0, 0);
add (01031,"Coffee County", "AL", "Alabama", "3", -6:1, 42436, 1148, 1, 0, 0);
add (48123,"DeWitt County", "TX", "Texas", "7", -6:1, 19661, 1148, 1, 0, 0);
add (54065,"Morgan County", "WV", "West Virginia", "2", -5:1, 13640, 1387, 1, 0, 0);
add (47133,"Overton County", "TN", "Tennesee", "3", -6:1, 19557, 1148, 1, 0, 0);
add (26133,"Osceola County", "MI", "Michigan", "4", -5:1, 22106, 1387, 1, 0, 0);
add (40121,"Pittsburg County", "OK", "Oklahoma", "7", -6:1, 42798, 1148, 1, 0, 0);
add (37047,"Columbus County", "NC", "North Carolina", "2", -5:1, 52634, 1387, 1, 0, 0);
add (27047,"Freeborn County", "MN", "Minnesota", "5", -6:1, 31584, 1148, 1, 0, 0);
add (28103,"Noxubee County", "MS", "Missippi", "5", -6:1, 12366, 1148, 1, 0, 0);
add (48073,"Cherokee County", "TX", "Texas", "7", -6:1, 42947, 1148, 1, 0, 0);
add (31149,"Rock County", "NE", "Nebraska", "6", -7:1, 1743, 4647, 1, 0, 0);
add (19197,"Wright County", "IA", "Iowa", "5", -6:1, 14003, 1148, 1, 0, 0);
add (47143,"Rhea County", "TN", "Tennesee", "3", -6:1, 27836, 1148, 1, 0, 0);
add (24001,"Allegany County", "MD", "Maryland", "2", -5:1, 71333, 1387, 1, 0, 0);
add (36085,"Richmond County", "NY", "New York", "1", -5:1, 407123, 1387, 1, 0, 0);
add (21185,"Oldham County", "KY", "Kentucky", "4", -5:1, 44395, 1387, 1, 0, 0);
add (28159,"Winston County", "MS", "Missippi", "5", -6:1, 19387, 1148, 1, 0, 0);
add (47149,"Rutherford County", "TN", "Tennesee", "3", -6:1, 166035, 1148, 1, 0, 0);
add (19017,"Bremer County", "IA", "Iowa", "5", -6:1, 23411, 1148, 1, 0, 0);
add (41051,"Multnomah County", "OR", "Oregon", "9", -8:1, 631082, 3240, 0, 1, 0);
add (42125,"Washington County", "PA", "Pennsylvania", "1", -5:1, 205566, 1387, 1, 0, 0);
add (51013,"Arlington County", "VA", "Virginia", "2", -5:1, 177275, 1387, 1, 0, 0);
add (22031,"De Soto Parish", "LA", "Louisiana", "7", -6:1, 24921, 1148, 1, 0, 0);
add (19107,"Keokuk County", "IA", "Iowa", "5", -6:1, 11499, 1148, 1, 0, 0);
add (13251,"Screven County", "GA", "Georgia", "3", -5:1, 14431, 1387, 1, 0, 0);
add (38059,"Morton County", "ND", "North Dakota", "5", -6:1, 24575, 1148, 1, 0, 0);
add (19011,"Benton County", "IA", "Iowa", "5", -6:1, 25418, 1148, 1, 0, 0);
add (26023,"Branch County", "MI", "Michigan", "4", -5:1, 43634, 1387, 1, 0, 0);
add (51720,"Norton city", "VA", "Virginia", "2", -5:1, 4155, 1387, 1, 0, 0);
add (02130,"Ketchikan Gateway Borough", "AK", "Alaska", "9", -9:1, 13443, 1174, 0, 0, 1);
add (35033,"Mora County", "NM", "New Mexico", "8", -7:1, 4861, 4647, 1, 0, 0);
add (29071,"Franklin County", "MO", "Mosourri", "6", -6:1, 91763, 1148, 1, 0, 0);
add (22043,"Grant Parish", "LA", "Louisiana", "7", -6:1, 18990, 1148, 1, 0, 0);
add (54043,"Lincoln County", "WV", "West Virginia", "2", -5:1, 22192, 1387, 1, 0, 0);
add (37113,"Macon County", "NC", "North Carolina", "2", -5:1, 28338, 1387, 1, 0, 0);
add (17053,"Ford County", "IL", "Illinois", "6", -6:1, 14084, 1148, 1, 0, 0);
add (37001,"Alamance County", "NC", "North Carolina", "2", -5:1, 119397, 1387, 1, 0, 0);
add (38099,"Walsh County", "ND", "North Dakota", "5", -6:1, 13532, 1148, 1, 0, 0);
add (42075,"Lebanon County", "PA", "Pennsylvania", "1", -5:1, 117434, 1387, 1, 0, 0);
add (18097,"Marion County", "IN", "Indiana", "4", -5:1, 813405, 1387, 1, 0, 0);
add (51023,"Botetourt County", "VA", "Virginia", "2", -5:1, 28561, 1387, 1, 0, 0);
add (51171,"Shenandoah County", "VA", "Virginia", "2", -5:1, 34663, 1387, 1, 0, 0);
add (18061,"Harrison County", "IN", "Indiana", "4", -5:1, 34730, 1387, 1, 0, 0);
add (51035,"Carroll County", "VA", "Virginia", "2", -5:1, 27873, 1387, 1, 0, 0);
add (51091,"Highland County", "VA", "Virginia", "2", -5:1, 2499, 1387, 1, 0, 0);
add (20045,"Douglas County", "KS", "Kansas", "6", -6:1, 93137, 1148, 1, 0, 0);
add (31059,"Fillmore County", "NE", "Nebraska", "6", -6:1, 6929, 1148, 1, 0, 0);
add (36057,"Montgomery County", "NY", "New York", "1", -5:1, 50755, 1387, 1, 0, 0);
add (20005,"Atchison County", "KS", "Kansas", "6", -6:1, 16908, 1148, 1, 0, 0);
add (47147,"Robertson County", "TN", "Tennesee", "3", -6:1, 53077, 1148, 1, 0, 0);
add (48043,"Brewster County", "TX", "Texas", "7", -6:1, 8893, 1148, 1, 0, 0);
add (05117,"Prairie County", "AR", "Arkansas", "7", -6:1, 9410, 1148, 1, 0, 0);
add (26157,"Tuscola County", "MI", "Michigan", "4", -5:1, 58181, 1387, 1, 0, 0);
add (46061,"Hanson County", "SD", "South Dakota", "5", -7:1, 2935, 4647, 1, 0, 0);
add (48061,"Cameron County", "TX", "Texas", "7", -6:1, 326449, 1148, 1, 0, 0);
add (21077,"Gallatin County", "KY", "Kentucky", "4", -6:1, 7182, 1148, 1, 0, 0);
add (42073,"Lawrence County", "PA", "Pennsylvania", "1", -5:1, 94887, 1387, 1, 0, 0);
add (48137,"Edwards County", "TX", "Texas", "7", -6:1, 3779, 1148, 1, 0, 0);
add (55011,"Buffalo County", "WI", "Wisconnsin", "5", -6:1, 14298, 1148, 1, 0, 0);
add (42001,"Adams County", "PA", "Pennsylvania", "1", -5:1, 86537, 1387, 1, 0, 0);
add (18037,"Dubois County", "IN", "Indiana", "4", -5:1, 39682, 1387, 1, 0, 0);
add (19143,"Osceola County", "IA", "Iowa", "5", -6:1, 6980, 1148, 1, 0, 0);
add (32005,"Douglas County", "NV", "Nevada", "8", -8:1, 37051, 3240, 0, 1, 0);
add (50025,"Windham County", "VT", "Vermont", "0", -5:1, 42650, 1387, 1, 0, 0);
add (45063,"Lexington County", "SC", "South Carolina", "2", -5:1, 205260, 1387, 1, 0, 0);
add (12085,"Martin County", "FL", "Florida", "3", -5:1, 115940, 1387, 1, 0, 0);
add (46093,"Meade County", "SD", "South Dakota", "5", -7:1, 21911, 4647, 1, 0, 0);
add (27075,"Lake County", "MN", "Minnesota", "5", -6:1, 10566, 1148, 1, 0, 0);
add (20033,"Comanche County", "KS", "Kansas", "6", -6:1, 2012, 1148, 1, 0, 0);
add (21139,"Livingston County", "KY", "Kentucky", "4", -5:1, 9432, 1387, 1, 0, 0);
add (19023,"Butler County", "IA", "Iowa", "5", -6:1, 15693, 1148, 1, 0, 0);
add (48367,"Parker County", "TX", "Texas", "7", -6:1, 81985, 1148, 1, 0, 0);
add (53019,"Ferry County", "WA", "Washiington", "9", -8:1, 7170, 3240, 0, 1, 0);
add (09015,"Windham County", "CT", "Connecticut", "0", -5:1, 105121, 1387, 1, 0, 0);
add (48315,"Marion County", "TX", "Texas", "7", -6:1, 10886, 1148, 1, 0, 0);
add (21165,"Menifee County", "KY", "Kentucky", "4", -5:1, 5736, 1387, 1, 0, 0);
add (53043,"Lincoln County", "WA", "Washiington", "9", -8:1, 9734, 3240, 0, 1, 0);
add (19109,"Kossuth County", "IA", "Iowa", "5", -6:1, 17738, 1148, 1, 0, 0);
add (40035,"Craig County", "OK", "Oklahoma", "7", -6:1, 14450, 1148, 1, 0, 0);
add (51037,"Charlotte County", "VA", "Virginia", "2", -5:1, 12259, 1387, 1, 0, 0);
add (48429,"Stephens County", "TX", "Texas", "7", -6:1, 9811, 1148, 1, 0, 0);
add (30093,"Silver Bow County", "MT", "Montana", "6", -7:1, 34560, 4647, 1, 0, 0);
add (54083,"Randolph County", "WV", "West Virginia", "2", -5:1, 28658, 1387, 1, 0, 0);
add (55078,"Menominee County", "WI", "Wisconnsin", "5", -6:1, 4779, 1148, 1, 0, 0);
add (21183,"Ohio County", "KY", "Kentucky", "4", -5:1, 22005, 1387, 1, 0, 0);
add (27063,"Jackson County", "MN", "Minnesota", "5", -6:1, 11529, 1148, 1, 0, 0);
add (08009,"Baca County", "CO", "Colorado", "8", -7:1, 4365, 4647, 1, 0, 0);
add (06053,"Monterey County", "CA", "California", "9", -8:1, 365605, 3240, 0, 1, 0);
add (29161,"Phelps County", "MO", "Mosourri", "6", -6:1, 38592, 1148, 1, 0, 0);
add (29067,"Douglas County", "MO", "Mosourri", "6", -6:1, 12422, 1148, 1, 0, 0);
add (39111,"Monroe County", "OH", "Ohio", "4", -5:1, 15357, 1387, 1, 0, 0);
add (48189,"Hale County", "TX", "Texas", "7", -6:1, 36676, 1148, 1, 0, 0);
add (13257,"Stephens County", "GA", "Georgia", "3", -5:1, 25421, 1387, 1, 0, 0);
add (29167,"Polk County", "MO", "Mosourri", "6", -6:1, 25530, 1148, 1, 0, 0);
add (36071,"Orange County", "NY", "New York", "1", -5:1, 329220, 1387, 1, 0, 0);
add (19047,"Crawford County", "IA", "Iowa", "5", -6:1, 16446, 1148, 1, 0, 0);
add (46085,"Lyman County", "SD", "South Dakota", "5", -7:1, 3768, 4647, 1, 0, 0);
add (12091,"Okaloosa County", "FL", "Florida", "3", -5:1, 169289, 1387, 1, 0, 0);
add (29153,"Ozark County", "MO", "Mosourri", "6", -6:1, 9897, 1148, 1, 0, 0);
add (48109,"Culberson County", "TX", "Texas", "7", -6:1, 3050, 1148, 1, 0, 0);
add (18009,"Blackford County", "IN", "Indiana", "4", -5:1, 13910, 1387, 1, 0, 0);
add (40033,"Cotton County", "OK", "Oklahoma", "7", -6:1, 6705, 1148, 1, 0, 0);
add (48155,"Foard County", "TX", "Texas", "7", -6:1, 1699, 1148, 1, 0, 0);
add (28073,"Lamar County", "MS", "Missippi", "5", -6:1, 36888, 1148, 1, 0, 0);
add (17107,"Logan County", "IL", "Illinois", "6", -6:1, 31289, 1148, 1, 0, 0);
add (38013,"Burke County", "ND", "North Dakota", "5", -6:1, 2266, 1148, 1, 0, 0);
add (29075,"Gentry County", "MO", "Mosourri", "6", -6:1, 6938, 1148, 1, 0, 0);
add (35013,"Dona Ana County", "NM", "New Mexico", "8", -7:1, 169165, 4647, 1, 0, 0);
add (51107,"Loudoun County", "VA", "Virginia", "2", -5:1, 143940, 1387, 1, 0, 0);
add (06023,"Humboldt County", "CA", "California", "9", -8:1, 122262, 3240, 0, 1, 0);
add (21193,"Perry County", "KY", "Kentucky", "4", -5:1, 31049, 1387, 1, 0, 0);
add (20203,"Wichita County", "KS", "Kansas", "6", -6:1, 2643, 1148, 1, 0, 0);
add (01053,"Escambia County", "AL", "Alabama", "3", -6:1, 36740, 1148, 1, 0, 0);
add (22049,"Jackson Parish", "LA", "Louisiana", "7", -6:1, 15566, 1148, 1, 0, 0);
add (42025,"Carbon County", "PA", "Pennsylvania", "1", -5:1, 58857, 1387, 1, 0, 0);
add (32023,"Nye County", "NV", "Nevada", "8", -8:1, 28799, 3240, 0, 1, 0);
add (32031,"Washoe County", "NV", "Nevada", "8", -8:1, 313660, 3240, 0, 1, 0);
add (31127,"Nemaha County", "NE", "Nebraska", "6", -7:1, 7697, 4647, 1, 0, 0);
add (48121,"Denton County", "TX", "Texas", "7", -6:1, 384020, 1148, 1, 0, 0);
add (39163,"Vinton County", "OH", "Ohio", "4", -5:1, 12158, 1387, 1, 0, 0);
add (27029,"Clearwater County", "MN", "Minnesota", "5", -6:1, 8285, 1148, 1, 0, 0);
add (39081,"Jefferson County", "OH", "Ohio", "4", -5:1, 74558, 1387, 1, 0, 0);
add (08099,"Prowers County", "CO", "Colorado", "8", -7:1, 13729, 4647, 1, 0, 0);
add (49005,"Cache County", "UT", "Utah", "8", -7:1, 86949, 4647, 1, 0, 0);
add (31001,"Adams County", "NE", "Nebraska", "6", -6:1, 29464, 1148, 1, 0, 0);
add (29111,"Lewis County", "MO", "Mosourri", "6", -6:1, 10199, 1148, 1, 0, 0);
add (45011,"Barnwell County", "SC", "South Carolina", "2", -5:1, 21766, 1387, 1, 0, 0);
add (36035,"Fulton County", "NY", "New York", "1", -5:1, 52914, 1387, 1, 0, 0);
add (05065,"Izard County", "AR", "Arkansas", "7", -6:1, 13093, 1148, 1, 0, 0);
add (39019,"Carroll County", "OH", "Ohio", "4", -5:1, 29095, 1387, 1, 0, 0);
add (38075,"Renville County", "ND", "North Dakota", "5", -6:1, 2808, 1148, 1, 0, 0);
add (08109,"Saguache County", "CO", "Colorado", "8", -7:1, 6076, 4647, 1, 0, 0);
add (46037,"Day County", "SD", "South Dakota", "5", -6:1, 6400, 1148, 1, 0, 0);
add (08089,"Otero County", "CO", "Colorado", "8", -7:1, 20671, 4647, 1, 0, 0);
add (21217,"Taylor County", "KY", "Kentucky", "4", -5:1, 22943, 1387, 1, 0, 0);
add (31071,"Garfield County", "NE", "Nebraska", "6", -6:1, 2039, 1148, 1, 0, 0);
add (09007,"Middlesex County", "CT", "Connecticut", "0", -5:1, 150034, 1387, 1, 0, 0);
add (27111,"Otter Tail County", "MN", "Minnesota", "5", -6:1, 54911, 1148, 1, 0, 0);
add (48375,"Potter County", "TX", "Texas", "7", -6:1, 108943, 1148, 1, 0, 0);
add (47041,"DeKalb County", "TN", "Tennesee", "3", -5:1, 15943, 1387, 1, 0, 0);
add (48011,"Armstrong County", "TX", "Texas", "7", -6:1, 2164, 1148, 1, 0, 0);
add (48331,"Milam County", "TX", "Texas", "7", -6:1, 24286, 1148, 1, 0, 0);
add (55009,"Brown County", "WI", "Wisconnsin", "5", -6:1, 215373, 1148, 1, 0, 0);
add (21001,"Adair County", "KY", "Kentucky", "4", -6:1, 16447, 1148, 1, 0, 0);
add (40077,"Latimer County", "OK", "Oklahoma", "7", -6:1, 10321, 1148, 1, 0, 0);
add (48333,"Mills County", "TX", "Texas", "7", -6:1, 4723, 1148, 1, 0, 0);
add (20141,"Osborne County", "KS", "Kansas", "6", -6:1, 4712, 1148, 1, 0, 0);
add (37017,"Bladen County", "NC", "North Carolina", "2", -5:1, 30717, 1387, 1, 0, 0);
add (19181,"Warren County", "IA", "Iowa", "5", -6:1, 40196, 1148, 1, 0, 0);
add (12013,"Calhoun County", "FL", "Florida", "3", -5:1, 12420, 1387, 1, 0, 0);
add (39105,"Meigs County", "OH", "Ohio", "4", -5:1, 24006, 1387, 1, 0, 0);
add (37169,"Stokes County", "NC", "North Carolina", "2", -5:1, 43292, 1387, 1, 0, 0);
add (55123,"Vernon County", "WI", "Wisconnsin", "5", -6:1, 27343, 1148, 1, 0, 0);
add (41011,"Coos County", "OR", "Oregon", "9", -8:1, 62162, 3240, 0, 1, 0);
add (31085,"Hayes County", "NE", "Nebraska", "6", -6:1, 1069, 1148, 1, 0, 0);
add (13051,"Chatham County", "GA", "Georgia", "3", -5:1, 225543, 1387, 1, 0, 0);
add (26143,"Roscommon County", "MI", "Michigan", "4", -5:1, 23467, 1387, 1, 0, 0);
add (29093,"Iron County", "MO", "Mosourri", "6", -6:1, 10871, 1148, 1, 0, 0);
add (48075,"Childress County", "TX", "Texas", "7", -6:1, 7532, 1148, 1, 0, 0);
add (35031,"McKinley County", "NM", "New Mexico", "8", -7:1, 67558, 4647, 1, 0, 0);
add (12039,"Gadsden County", "FL", "Florida", "3", -5:1, 44043, 1387, 1, 0, 0);
add (47123,"Monroe County", "TN", "Tennesee", "3", -6:1, 34830, 1148, 1, 0, 0);
add (17045,"Edgar County", "IL", "Illinois", "6", -6:1, 19652, 1148, 1, 0, 0);
add (20187,"Stanton County", "KS", "Kansas", "6", -6:1, 2265, 1148, 1, 0, 0);
add (19059,"Dickinson County", "IA", "Iowa", "5", -6:1, 16236, 1148, 1, 0, 0);
add (29073,"Gasconade County", "MO", "Mosourri", "6", -6:1, 14890, 1148, 1, 0, 0);
add (49035,"Salt Lake County", "UT", "Utah", "8", -7:1, 850667, 4647, 1, 0, 0);
add (13215,"Muscogee County", "GA", "Georgia", "3", -5:1, 182752, 1387, 1, 0, 0);
add (38057,"Mercer County", "ND", "North Dakota", "5", -6:1, 9418, 1148, 1, 0, 0);
add (16081,"Teton County", "ID", "Idaho", "8", -7:1, 5488, 4647, 1, 0, 0);
add (42085,"Mercer County", "PA", "Pennsylvania", "1", -5:1, 121938, 1387, 1, 0, 0);
add (40085,"Love County", "OK", "Oklahoma", "7", -6:1, 8536, 1148, 1, 0, 0);
add (29151,"Osage County", "MO", "Mosourri", "6", -6:1, 12425, 1148, 1, 0, 0);
add (48213,"Henderson County", "TX", "Texas", "7", -6:1, 68757, 1148, 1, 0, 0);
add (40059,"Harper County", "OK", "Oklahoma", "7", -6:1, 3596, 1148, 1, 0, 0);
add (18031,"Decatur County", "IN", "Indiana", "4", -5:1, 25562, 1387, 1, 0, 0);
add (45069,"Marlboro County", "SC", "South Carolina", "2", -5:1, 29589, 1387, 1, 0, 0);
add (51113,"Madison County", "VA", "Virginia", "2", -5:1, 12697, 1387, 1, 0, 0);
add (01093,"Marion County", "AL", "Alabama", "3", -6:1, 30986, 1148, 1, 0, 0);
add (37135,"Orange County", "NC", "North Carolina", "2", -5:1, 110116, 1387, 1, 0, 0);
add (19087,"Henry County", "IA", "Iowa", "5", -6:1, 19983, 1148, 1, 0, 0);
add (37185,"Warren County", "NC", "North Carolina", "2", -5:1, 18297, 1387, 1, 0, 0);
add (30051,"Liberty County", "MT", "Montana", "6", -7:1, 2323, 4647, 1, 0, 0);
add (22037,"East Feliciana Parish", "LA", "Louisiana", "7", -6:1, 20847, 1148, 1, 0, 0);
add (17159,"Richland County", "IL", "Illinois", "6", -6:1, 16769, 1148, 1, 0, 0);
add (39087,"Lawrence County", "OH", "Ohio", "4", -5:1, 64427, 1387, 1, 0, 0);
add (22117,"Washington Parish", "LA", "Louisiana", "7", -6:1, 43059, 1148, 1, 0, 0);
add (48265,"Kerr County", "TX", "Texas", "7", -6:1, 43248, 1148, 1, 0, 0);
add (12083,"Marion County", "FL", "Florida", "3", -5:1, 241513, 1387, 1, 0, 0);
add (48055,"Caldwell County", "TX", "Texas", "7", -6:1, 32447, 1148, 1, 0, 0);
add (13011,"Banks County", "GA", "Georgia", "3", -5:1, 12798, 1387, 1, 0, 0);
add (51770,"Roanoke city", "VA", "Virginia", "2", -5:1, 93749, 1387, 1, 0, 0);
add (20009,"Barton County", "KS", "Kansas", "6", -6:1, 27641, 1148, 1, 0, 0);
add (21099,"Hart County", "KY", "Kentucky", "4", -6:1, 16738, 1148, 1, 0, 0);
add (51063,"Floyd County", "VA", "Virginia", "2", -5:1, 13091, 1387, 1, 0, 0);
add (40097,"Mayes County", "OK", "Oklahoma", "7", -6:1, 37638, 1148, 1, 0, 0);
add (46075,"Jones County", "SD", "South Dakota", "5", -7:1, 1219, 4647, 1, 0, 0);
add (38071,"Ramsey County", "ND", "North Dakota", "5", -6:1, 12120, 1148, 1, 0, 0);
add (40055,"Greer County", "OK", "Oklahoma", "7", -6:1, 6366, 1148, 1, 0, 0);
add (41047,"Marion County", "OR", "Oregon", "9", -8:1, 268541, 3240, 0, 1, 0);
add (47089,"Jefferson County", "TN", "Tennesee", "3", -6:1, 43663, 1148, 1, 0, 0);
add (38033,"Golden Valley County", "ND", "North Dakota", "5", -6:1, 1876, 1148, 1, 0, 0);
add (40153,"Woodward County", "OK", "Oklahoma", "7", -6:1, 18553, 1148, 1, 0, 0);
add (28027,"Coahoma County", "MS", "Missippi", "5", -6:1, 31089, 1148, 1, 0, 0);
add (48487,"Wilbarger County", "TX", "Texas", "7", -6:1, 13711, 1148, 1, 0, 0);
add (55021,"Columbia County", "WI", "Wisconnsin", "5", -6:1, 51152, 1148, 1, 0, 0);
add (37161,"Rutherford County", "NC", "North Carolina", "2", -5:1, 60842, 1387, 1, 0, 0);
add (18145,"Shelby County", "IN", "Indiana", "4", -5:1, 43451, 1387, 1, 0, 0);
add (01013,"Butler County", "AL", "Alabama", "3", -6:1, 21695, 1148, 1, 0, 0);
add (37175,"Transylvania County", "NC", "North Carolina", "2", -5:1, 28481, 1387, 1, 0, 0);
add (20139,"Osage County", "KS", "Kansas", "6", -6:1, 17139, 1148, 1, 0, 0);
add (21073,"Franklin County", "KY", "Kentucky", "4", -6:1, 46438, 1148, 1, 0, 0);
add (06009,"Calaveras County", "CA", "California", "9", -8:1, 39830, 3240, 0, 1, 0);
add (28097,"Montgomery County", "MS", "Missippi", "5", -6:1, 12425, 1148, 1, 0, 0);
add (26081,"Kent County", "MI", "Michigan", "4", -5:1, 545166, 1387, 1, 0, 0);
add (20031,"Coffey County", "KS", "Kansas", "6", -6:1, 8696, 1148, 1, 0, 0);
add (39005,"Ashland County", "OH", "Ohio", "4", -5:1, 52237, 1387, 1, 0, 0);
add (27077,"Lake of the Woods County", "MN", "Minnesota", "5", -6:1, 4563, 1148, 1, 0, 0);
add (51099,"King George County", "VA", "Virginia", "2", -5:1, 17236, 1387, 1, 0, 0);
add (39015,"Brown County", "OH", "Ohio", "4", -5:1, 40795, 1387, 1, 0, 0);
add (54073,"Pleasants County", "WV", "West Virginia", "2", -5:1, 7421, 1387, 1, 0, 0);
add (21023,"Bracken County", "KY", "Kentucky", "4", -6:1, 8455, 1148, 1, 0, 0);
add (40069,"Johnston County", "OK", "Oklahoma", "7", -6:1, 10346, 1148, 1, 0, 0);
add (39115,"Morgan County", "OH", "Ohio", "4", -5:1, 14536, 1387, 1, 0, 0);
add (37181,"Vance County", "NC", "North Carolina", "2", -5:1, 42155, 1387, 1, 0, 0);
add (13259,"Stewart County", "GA", "Georgia", "3", -5:1, 5468, 1387, 1, 0, 0);
add (01099,"Monroe County", "AL", "Alabama", "3", -6:1, 23965, 1148, 1, 0, 0);
add (13087,"Decatur County", "GA", "Georgia", "3", -5:1, 27035, 1387, 1, 0, 0);
add (41009,"Columbia County", "OR", "Oregon", "9", -8:1, 44416, 3240, 0, 1, 0);
add (49047,"Uintah County", "UT", "Utah", "8", -7:1, 25660, 4647, 1, 0, 0);
add (51025,"Brunswick County", "VA", "Virginia", "2", -5:1, 16716, 1387, 1, 0, 0);
add (28139,"Tippah County", "MS", "Missippi", "5", -6:1, 21031, 1148, 1, 0, 0);
add (53061,"Snohomish County", "WA", "Washiington", "9", -8:1, 587783, 3240, 0, 1, 0);
add (40125,"Pottawatomie County", "OK", "Oklahoma", "7", -6:1, 62244, 1148, 1, 0, 0);
add (55023,"Crawford County", "WI", "Wisconnsin", "5", -6:1, 16576, 1148, 1, 0, 0);
add (26069,"Iosco County", "MI", "Michigan", "4", -5:1, 25111, 1387, 1, 0, 0);
add (19155,"Pottawattamie County", "IA", "Iowa", "5", -6:1, 86174, 1148, 1, 0, 0);
add (04003,"Cochise County", "AZ", "Arizona", "8", -7:1, 112564, 4647, 1, 0, 0);
add (36113,"Warren County", "NY", "New York", "1", -5:1, 61261, 1387, 1, 0, 0);
add (46051,"Grant County", "SD", "South Dakota", "5", -6:1, 8063, 1148, 1, 0, 0);
add (12069,"Lake County", "FL", "Florida", "3", -5:1, 202207, 1387, 1, 0, 0);
add (16059,"Lemhi County", "ID", "Idaho", "8", -7:1, 8030, 4647, 1, 0, 0);
add (45037,"Edgefield County", "SC", "South Carolina", "2", -5:1, 20003, 1387, 1, 0, 0);
add (21143,"Lyon County", "KY", "Kentucky", "4", -5:1, 8052, 1387, 1, 0, 0);
add (36009,"Cattaraugus County", "NY", "New York", "1", -5:1, 85086, 1387, 1, 0, 0);
add (48019,"Bandera County", "TX", "Texas", "7", -6:1, 15754, 1148, 1, 0, 0);
add (27087,"Mahnomen County", "MN", "Minnesota", "5", -6:1, 5077, 1148, 1, 0, 0);
add (20041,"Dickinson County", "KS", "Kansas", "6", -6:1, 19742, 1148, 1, 0, 0);
add (37097,"Iredell County", "NC", "North Carolina", "2", -5:1, 113247, 1387, 1, 0, 0);
add (20195,"Trego County", "KS", "Kansas", "6", -6:1, 3283, 1148, 1, 0, 0);
add (51790,"Staunton city", "VA", "Virginia", "2", -5:1, 23346, 1387, 1, 0, 0);
add (37157,"Rockingham County", "NC", "North Carolina", "2", -5:1, 90039, 1387, 1, 0, 0);
add (56021,"Laramie County", "WY", "Wyoming", "8", -7:1, 78872, 4647, 1, 0, 0);
add (51600,"Fairfax city", "VA", "Virginia", "2", -5:1, 20697, 1387, 1, 0, 0);
add (12003,"Baker County", "FL", "Florida", "3", -5:1, 21103, 1387, 1, 0, 0);
add (46039,"Deuel County", "SD", "South Dakota", "5", -6:1, 4512, 1148, 1, 0, 0);
add (51095,"James City County", "VA", "Virginia", "2", -5:1, 44233, 1387, 1, 0, 0);
add (06063,"Plumas County", "CA", "California", "9", -8:1, 20370, 3240, 0, 1, 0);
add (20069,"Gray County", "KS", "Kansas", "6", -6:1, 5595, 1148, 1, 0, 0);
add (48191,"Hall County", "TX", "Texas", "7", -6:1, 3644, 1148, 1, 0, 0);
add (51195,"Wise County", "VA", "Virginia", "2", -5:1, 38599, 1387, 1, 0, 0);
add (40143,"Tulsa County", "OK", "Oklahoma", "7", -6:1, 543539, 1148, 1, 0, 0);
add (22045,"Iberia Parish", "LA", "Louisiana", "7", -6:1, 73154, 1148, 1, 0, 0);
add (53035,"Kitsap County", "WA", "Washiington", "9", -8:1, 232623, 3240, 0, 1, 0);
add (45005,"Allendale County", "SC", "South Carolina", "2", -5:1, 11460, 1387, 1, 0, 0);
add (42047,"Elk County", "PA", "Pennsylvania", "1", -5:1, 34540, 1387, 1, 0, 0);
add (48275,"Knox County", "TX", "Texas", "7", -6:1, 4252, 1148, 1, 0, 0);
add (51079,"Greene County", "VA", "Virginia", "2", -5:1, 13991, 1387, 1, 0, 0);
add (18103,"Miami County", "IN", "Indiana", "4", -5:1, 33543, 1387, 1, 0, 0);
add (29079,"Grundy County", "MO", "Mosourri", "6", -6:1, 10159, 1148, 1, 0, 0);
add (37139,"Pasquotank County", "NC", "North Carolina", "2", -5:1, 35474, 1387, 1, 0, 0);
add (42065,"Jefferson County", "PA", "Pennsylvania", "1", -5:1, 46250, 1387, 1, 0, 0);
add (47025,"Claiborne County", "TN", "Tennesee", "3", -5:1, 29529, 1387, 1, 0, 0);
add (17173,"Shelby County", "IL", "Illinois", "6", -6:1, 22731, 1148, 1, 0, 0);
add (44009,"Washington County", "RI", "Rhode Island", "0", -5:1, 120649, 1387, 1, 0, 0);
add (01015,"Calhoun County", "AL", "Alabama", "3", -6:1, 117018, 1148, 1, 0, 0);
add (08087,"Morgan County", "CO", "Colorado", "8", -7:1, 25087, 4647, 1, 0, 0);
add (55001,"Adams County", "WI", "Wisconnsin", "5", -6:1, 18492, 1148, 1, 0, 0);
add (17129,"Menard County", "IL", "Illinois", "6", -6:1, 12469, 1148, 1, 0, 0);
add (47093,"Knox County", "TN", "Tennesee", "3", -6:1, 366846, 1148, 1, 0, 0);
add (21167,"Mercer County", "KY", "Kentucky", "4", -5:1, 20704, 1387, 1, 0, 0);
add (31179,"Wayne County", "NE", "Nebraska", "6", -7:1, 9400, 4647, 1, 0, 0);
add (38091,"Steele County", "ND", "North Dakota", "5", -6:1, 2263, 1148, 1, 0, 0);
add (48491,"Williamson County", "TX", "Texas", "7", -6:1, 223910, 1148, 1, 0, 0);
add (55025,"Dane County", "WI", "Wisconnsin", "5", -6:1, 424586, 1148, 1, 0, 0);
add (31161,"Sheridan County", "NE", "Nebraska", "6", -7:1, 6454, 4647, 1, 0, 0);
add (54109,"Wyoming County", "WV", "West Virginia", "2", -5:1, 27380, 1387, 1, 0, 0);
add (40133,"Seminole County", "OK", "Oklahoma", "7", -6:1, 24770, 1148, 1, 0, 0);
add (26105,"Mason County", "MI", "Michigan", "4", -5:1, 27950, 1387, 1, 0, 0);
add (24510,"Baltimore city", "MD", "Maryland", "2", -5:1, 645593, 1387, 1, 0, 0);
add (39025,"Clermont County", "OH", "Ohio", "4", -5:1, 175960, 1387, 1, 0, 0);
add (42087,"Mifflin County", "PA", "Pennsylvania", "1", -5:1, 46961, 1387, 1, 0, 0);
add (23009,"Hancock County", "ME", "Maine", "0", -5:1, 49932, 1387, 1, 0, 0);
add (18039,"Elkhart County", "IN", "Indiana", "4", -5:1, 172310, 1387, 1, 0, 0);
add (48157,"Fort Bend County", "TX", "Texas", "7", -6:1, 337798, 1148, 1, 0, 0);
add (41015,"Curry County", "OR", "Oregon", "9", -8:1, 21157, 3240, 0, 1, 0);
add (01061,"Geneva County", "AL", "Alabama", "3", -6:1, 24944, 1148, 1, 0, 0);
add (53053,"Pierce County", "WA", "Washiington", "9", -8:1, 676505, 3240, 0, 1, 0);
add (48299,"Llano County", "TX", "Texas", "7", -6:1, 13480, 1148, 1, 0, 0);
add (55053,"Jackson County", "WI", "Wisconnsin", "5", -6:1, 17735, 1148, 1, 0, 0);
add (38019,"Cavalier County", "ND", "North Dakota", "5", -6:1, 5011, 1148, 1, 0, 0);
add (34031,"Passaic County", "NJ", "New Jersey", "0", -5:1, 485737, 1387, 1, 0, 0);
add (26089,"Leelanau County", "MI", "Michigan", "4", -5:1, 19142, 1387, 1, 0, 0);
add (32011,"Eureka County", "NV", "Nevada", "8", -8:1, 1994, 3240, 0, 1, 0);
add (21007,"Ballard County", "KY", "Kentucky", "4", -6:1, 8488, 1148, 1, 0, 0);
add (27015,"Brown County", "MN", "Minnesota", "5", -6:1, 27037, 1148, 1, 0, 0);
add (48195,"Hansford County", "TX", "Texas", "7", -6:1, 5347, 1148, 1, 0, 0);
add (54033,"Harrison County", "WV", "West Virginia", "2", -5:1, 70891, 1387, 1, 0, 0);
add (47171,"Unicoi County", "TN", "Tennesee", "3", -6:1, 17216, 1148, 1, 0, 0);
add (40103,"Noble County", "OK", "Oklahoma", "7", -6:1, 11425, 1148, 1, 0, 0);
add (32009,"Esmeralda County", "NV", "Nevada", "8", -8:1, 1135, 3240, 0, 1, 0);
add (08067,"La Plata County", "CO", "Colorado", "8", -7:1, 40413, 4647, 1, 0, 0);
add (48167,"Galveston County", "TX", "Texas", "7", -6:1, 245556, 1148, 1, 0, 0);
add (31031,"Cherry County", "NE", "Nebraska", "6", -6:1, 6326, 1148, 1, 0, 0);
add (23027,"Waldo County", "ME", "Maine", "0", -5:1, 36465, 1387, 1, 0, 0);
add (49057,"Weber County", "UT", "Utah", "8", -7:1, 184065, 4647, 1, 0, 0);
add (06111,"Ventura County", "CA", "California", "9", -8:1, 731967, 3240, 0, 1, 0);
add (46121,"Todd County", "SD", "South Dakota", "5", -7:1, 9247, 4647, 1, 0, 0);
add (17019,"Champaign County", "IL", "Illinois", "6", -6:1, 167788, 1148, 1, 0, 0);
add (05093,"Mississippi County", "AR", "Arkansas", "7", -6:1, 50635, 1148, 1, 0, 0);
add (51173,"Smyth County", "VA", "Virginia", "2", -5:1, 32757, 1387, 1, 0, 0);
add (26073,"Isabella County", "MI", "Michigan", "4", -5:1, 58026, 1387, 1, 0, 0);
add (55043,"Grant County", "WI", "Wisconnsin", "5", -6:1, 49340, 1148, 1, 0, 0);
add (26067,"Ionia County", "MI", "Michigan", "4", -5:1, 61700, 1387, 1, 0, 0);
add (13281,"Towns County", "GA", "Georgia", "3", -5:1, 8529, 1387, 1, 0, 0);
add (55057,"Juneau County", "WI", "Wisconnsin", "5", -6:1, 23822, 1148, 1, 0, 0);
add (20075,"Hamilton County", "KS", "Kansas", "6", -6:1, 2343, 1148, 1, 0, 0);
add (45087,"Union County", "SC", "South Carolina", "2", -5:1, 30495, 1387, 1, 0, 0);
add (51085,"Hanover County", "VA", "Virginia", "2", -5:1, 81975, 1387, 1, 0, 0);
add (26093,"Livingston County", "MI", "Michigan", "4", -5:1, 146165, 1387, 1, 0, 0);
add (48079,"Cochran County", "TX", "Texas", "7", -6:1, 3952, 1148, 1, 0, 0);
add (31075,"Grant County", "NE", "Nebraska", "6", -6:1, 763, 1148, 1, 0, 0);
add (17147,"Piatt County", "IL", "Illinois", "6", -6:1, 16400, 1148, 1, 0, 0);
add (55037,"Florence County", "WI", "Wisconnsin", "5", -6:1, 5199, 1148, 1, 0, 0);
add (48097,"Cooke County", "TX", "Texas", "7", -6:1, 32837, 1148, 1, 0, 0);
add (55077,"Marquette County", "WI", "Wisconnsin", "5", -6:1, 15101, 1148, 1, 0, 0);
add (51193,"Westmoreland County", "VA", "Virginia", "2", -5:1, 16282, 1387, 1, 0, 0);
add (09009,"New Haven County", "CT", "Connecticut", "0", -5:1, 793504, 1387, 1, 0, 0);
add (08025,"Crowley County", "CO", "Colorado", "8", -7:1, 4310, 4647, 1, 0, 0);
add (17025,"Clay County", "IL", "Illinois", "6", -6:1, 14485, 1148, 1, 0, 0);
add (26107,"Mecosta County", "MI", "Michigan", "4", -5:1, 40006, 1387, 1, 0, 0);
add (37065,"Edgecombe County", "NC", "North Carolina", "2", -5:1, 55199, 1387, 1, 0, 0);
add (21013,"Bell County", "KY", "Kentucky", "4", -6:1, 29133, 1148, 1, 0, 0);
add (27081,"Lincoln County", "MN", "Minnesota", "5", -6:1, 6459, 1148, 1, 0, 0);
add (29107,"Lafayette County", "MO", "Mosourri", "6", -6:1, 32653, 1148, 1, 0, 0);
add (49017,"Garfield County", "UT", "Utah", "8", -7:1, 4272, 4647, 1, 0, 0);
add (18011,"Boone County", "IN", "Indiana", "4", -5:1, 43843, 1387, 1, 0, 0);
add (55027,"Dodge County", "WI", "Wisconnsin", "5", -6:1, 83261, 1148, 1, 0, 0);
add (51137,"Orange County", "VA", "Virginia", "2", -5:1, 25408, 1387, 1, 0, 0);
add (42099,"Perry County", "PA", "Pennsylvania", "1", -5:1, 44384, 1387, 1, 0, 0);
add (21021,"Boyle County", "KY", "Kentucky", "4", -6:1, 27186, 1148, 1, 0, 0);
add (04011,"Greenlee County", "AZ", "Arizona", "8", -7:1, 9304, 4647, 1, 0, 0);
add (31029,"Chase County", "NE", "Nebraska", "6", -6:1, 4248, 1148, 1, 0, 0);
add (48305,"Lynn County", "TX", "Texas", "7", -6:1, 6706, 1148, 1, 0, 0);
add (40095,"Marshall County", "OK", "Oklahoma", "7", -6:1, 12326, 1148, 1, 0, 0);
add (48245,"Jefferson County", "TX", "Texas", "7", -6:1, 241901, 1148, 1, 0, 0);
add (51031,"Campbell County", "VA", "Virginia", "2", -5:1, 50335, 1387, 1, 0, 0);
add (51145,"Powhatan County", "VA", "Virginia", "2", -5:1, 21950, 1387, 1, 0, 0);
add (48145,"Falls County", "TX", "Texas", "7", -6:1, 17434, 1148, 1, 0, 0);
add (01009,"Blount County", "AL", "Alabama", "3", -6:1, 46266, 1148, 1, 0, 0);
add (51157,"Rappahannock County", "VA", "Virginia", "2", -5:1, 7269, 1387, 1, 0, 0);
add (26155,"Shiawassee County", "MI", "Michigan", "4", -5:1, 72569, 1387, 1, 0, 0);
add (18085,"Kosciusko County", "IN", "Indiana", "4", -5:1, 71207, 1387, 1, 0, 0);
add (20163,"Rooks County", "KS", "Kansas", "6", -6:1, 5660, 1148, 1, 0, 0);
add (30035,"Glacier County", "MT", "Montana", "6", -7:1, 12540, 4647, 1, 0, 0);
add (48365,"Panola County", "TX", "Texas", "7", -6:1, 23070, 1148, 1, 0, 0);
add (51073,"Gloucester County", "VA", "Virginia", "2", -5:1, 35081, 1387, 1, 0, 0);
add (13097,"Douglas County", "GA", "Georgia", "3", -5:1, 89843, 1387, 1, 0, 0);
add (12031,"Duval County", "FL", "Florida", "3", -5:1, 735733, 1387, 1, 0, 0);
add (39089,"Licking County", "OH", "Ohio", "4", -5:1, 136896, 1387, 1, 0, 0);
add (36117,"Wayne County", "NY", "New York", "1", -5:1, 94977, 1387, 1, 0, 0);
add (29083,"Henry County", "MO", "Mosourri", "6", -6:1, 21232, 1148, 1, 0, 0);
add (33011,"Hillsborough County", "NH", "New Hampshire", "0", -5:1, 363031, 1387, 1, 0, 0);
add (48025,"Bee County", "TX", "Texas", "7", -6:1, 27718, 1148, 1, 0, 0);
add (47109,"McNairy County", "TN", "Tennesee", "3", -6:1, 24048, 1148, 1, 0, 0);
add (51185,"Tazewell County", "VA", "Virginia", "2", -5:1, 46766, 1387, 1, 0, 0);
add (45085,"Sumter County", "SC", "South Carolina", "2", -5:1, 107127, 1387, 1, 0, 0);
add (42089,"Monroe County", "PA", "Pennsylvania", "1", -5:1, 125583, 1387, 1, 0, 0);
add (05121,"Randolph County", "AR", "Arkansas", "7", -6:1, 17802, 1148, 1, 0, 0);
add (48253,"Jones County", "TX", "Texas", "7", -6:1, 18669, 1148, 1, 0, 0);
add (22035,"East Carroll Parish", "LA", "Louisiana", "7", -6:1, 8905, 1148, 1, 0, 0);
add (48163,"Frio County", "TX", "Texas", "7", -6:1, 15757, 1148, 1, 0, 0);
add (17021,"Christian County", "IL", "Illinois", "6", -6:1, 34543, 1148, 1, 0, 0);
add (04012,"La Paz County", "AZ", "Arizona", "8", -7:1, 14880, 4647, 1, 0, 0);
add (54041,"Lewis County", "WV", "West Virginia", "2", -5:1, 17427, 1387, 1, 0, 0);
add (28035,"Forrest County", "MS", "Missippi", "5", -6:1, 74364, 1148, 1, 0, 0);
add (21215,"Spencer County", "KY", "Kentucky", "4", -5:1, 9660, 1387, 1, 0, 0);
add (18115,"Ohio County", "IN", "Indiana", "4", -5:1, 5423, 1387, 1, 0, 0);
add (13307,"Webster County", "GA", "Georgia", "3", -5:1, 2193, 1387, 1, 0, 0);
add (18003,"Allen County", "IN", "Indiana", "4", -5:1, 314218, 1387, 1, 0, 0);
add (56009,"Converse County", "WY", "Wyoming", "8", -7:1, 12337, 4647, 1, 0, 0);
add (20105,"Lincoln County", "KS", "Kansas", "6", -6:1, 3338, 1148, 1, 0, 0);
add (21235,"Whitley County", "KY", "Kentucky", "4", -5:1, 35938, 1387, 1, 0, 0);
add (39169,"Wayne County", "OH", "Ohio", "4", -5:1, 110125, 1387, 1, 0, 0);
add (42107,"Schuylkill County", "PA", "Pennsylvania", "1", -5:1, 148266, 1387, 1, 0, 0);
add (13277,"Tift County", "GA", "Georgia", "3", -5:1, 36673, 1387, 1, 0, 0);
add (45033,"Dillon County", "SC", "South Carolina", "2", -5:1, 29747, 1387, 1, 0, 0);
add (22033,"East Baton Rouge Parish", "LA", "Louisiana", "7", -6:1, 394714, 1148, 1, 0, 0);
add (17151,"Pope County", "IL", "Illinois", "6", -6:1, 4808, 1148, 1, 0, 0);
add (42095,"Northampton County", "PA", "Pennsylvania", "1", -5:1, 258679, 1387, 1, 0, 0);
add (48183,"Gregg County", "TX", "Texas", "7", -6:1, 113330, 1148, 1, 0, 0);
add (18113,"Noble County", "IN", "Indiana", "4", -5:1, 42626, 1387, 1, 0, 0);
add (29059,"Dallas County", "MO", "Mosourri", "6", -6:1, 15245, 1148, 1, 0, 0);
add (51057,"Essex County", "VA", "Virginia", "2", -5:1, 9127, 1387, 1, 0, 0);
add (19167,"Sioux County", "IA", "Iowa", "5", -6:1, 31280, 1148, 1, 0, 0);
add (21195,"Pike County", "KY", "Kentucky", "4", -5:1, 72121, 1387, 1, 0, 0);
add (31081,"Hamilton County", "NE", "Nebraska", "6", -6:1, 9471, 1148, 1, 0, 0);
add (12079,"Madison County", "FL", "Florida", "3", -5:1, 17652, 1387, 1, 0, 0);
add (17005,"Bond County", "IL", "Illinois", "6", -6:1, 15858, 1148, 1, 0, 0);
add (48197,"Hardeman County", "TX", "Texas", "7", -6:1, 4591, 1148, 1, 0, 0);
add (20119,"Meade County", "KS", "Kansas", "6", -6:1, 4424, 1148, 1, 0, 0);
add (17039,"De Witt County", "IL", "Illinois", "6", -6:1, 16796, 1148, 1, 0, 0);
add (12023,"Columbia County", "FL", "Florida", "3", -5:1, 52956, 1387, 1, 0, 0);
add (29015,"Benton County", "MO", "Mosourri", "6", -6:1, 17040, 1148, 1, 0, 0);
add (17009,"Brown County", "IL", "Illinois", "6", -6:1, 6822, 1148, 1, 0, 0);
add (19141,"O-Brien County", "IA", "Iowa", "5", -6:1, 14910, 1148, 1, 0, 0);
add (48309,"McLennan County", "TX", "Texas", "7", -6:1, 203446, 1148, 1, 0, 0);
add (13235,"Pulaski County", "GA", "Georgia", "3", -5:1, 8401, 1387, 1, 0, 0);
add (12065,"Jefferson County", "FL", "Florida", "3", -5:1, 12952, 1387, 1, 0, 0);
add (37123,"Montgomery County", "NC", "North Carolina", "2", -5:1, 24080, 1387, 1, 0, 0);
add (28023,"Clarke County", "MS", "Missippi", "5", -6:1, 18231, 1148, 1, 0, 0);
add (27019,"Carver County", "MN", "Minnesota", "5", -6:1, 64674, 1148, 1, 0, 0);
add (30057,"Madison County", "MT", "Montana", "6", -7:1, 6875, 4647, 1, 0, 0);
add (20093,"Kearny County", "KS", "Kansas", "6", -6:1, 4177, 1148, 1, 0, 0);
add (39051,"Fulton County", "OH", "Ohio", "4", -5:1, 41895, 1387, 1, 0, 0);
add (29215,"Texas County", "MO", "Mosourri", "6", -6:1, 22357, 1148, 1, 0, 0);
add (19055,"Delaware County", "IA", "Iowa", "5", -6:1, 18578, 1148, 1, 0, 0);
add (40061,"Haskell County", "OK", "Oklahoma", "7", -6:1, 11368, 1148, 1, 0, 0);
add (31103,"Keya Paha County", "NE", "Nebraska", "6", -7:1, 972, 4647, 1, 0, 0);
add (39003,"Allen County", "OH", "Ohio", "4", -5:1, 107139, 1387, 1, 0, 0);
add (48317,"Martin County", "TX", "Texas", "7", -6:1, 5043, 1148, 1, 0, 0);
add (40071,"Kay County", "OK", "Oklahoma", "7", -6:1, 46698, 1148, 1, 0, 0);
add (37095,"Hyde County", "NC", "North Carolina", "2", -5:1, 5612, 1387, 1, 0, 0);
add (20127,"Morris County", "KS", "Kansas", "6", -6:1, 6169, 1148, 1, 0, 0);
add (30069,"Petroleum County", "MT", "Montana", "6", -7:1, 499, 4647, 1, 0, 0);
add (06045,"Mendocino County", "CA", "California", "9", -8:1, 83734, 3240, 0, 1, 0);
add (28011,"Bolivar County", "MS", "Missippi", "5", -6:1, 40318, 1148, 1, 0, 0);
add (51101,"King William County", "VA", "Virginia", "2", -5:1, 12768, 1387, 1, 0, 0);
add (02110,"Juneau Borough", "AK", "Alaska", "9", -9:1, 30191, 1174, 0, 0, 1);
add (35043,"Sandoval County", "NM", "New Mexico", "8", -7:1, 88049, 4647, 1, 0, 0);
add (13225,"Peach County", "GA", "Georgia", "3", -5:1, 24462, 1387, 1, 0, 0);
add (39117,"Morrow County", "OH", "Ohio", "4", -5:1, 31467, 1387, 1, 0, 0);
add (47009,"Blount County", "TN", "Tennesee", "3", -5:1, 101295, 1387, 1, 0, 0);
add (28017,"Chickasaw County", "MS", "Missippi", "5", -6:1, 18013, 1148, 1, 0, 0);
add (13267,"Tattnall County", "GA", "Georgia", "3", -5:1, 18975, 1387, 1, 0, 0);
add (26101,"Manistee County", "MI", "Michigan", "4", -5:1, 23330, 1387, 1, 0, 0);
add (13081,"Crisp County", "GA", "Georgia", "3", -5:1, 20725, 1387, 1, 0, 0);
add (13317,"Wilkes County", "GA", "Georgia", "3", -5:1, 10568, 1387, 1, 0, 0);
add (12001,"Alachua County", "FL", "Florida", "3", -5:1, 198662, 1387, 1, 0, 0);
add (54051,"Marshall County", "WV", "West Virginia", "2", -5:1, 35441, 1387, 1, 0, 0);
add (17197,"Will County", "IL", "Illinois", "6", -6:1, 459189, 1148, 1, 0, 0);
add (54009,"Brooke County", "WV", "West Virginia", "2", -5:1, 26004, 1387, 1, 0, 0);
add (42129,"Westmoreland County", "PA", "Pennsylvania", "1", -5:1, 372103, 1387, 1, 0, 0);
add (33013,"Merrimack County", "NH", "New Hampshire", "0", -5:1, 127381, 1387, 1, 0, 0);
add (27045,"Fillmore County", "MN", "Minnesota", "5", -6:1, 20793, 1148, 1, 0, 0);
add (48091,"Comal County", "TX", "Texas", "7", -6:1, 73391, 1148, 1, 0, 0);
add (28031,"Covington County", "MS", "Missippi", "5", -6:1, 17802, 1148, 1, 0, 0);
add (06091,"Sierra County", "CA", "California", "9", -8:1, 3380, 3240, 0, 1, 0);
add (48151,"Fisher County", "TX", "Texas", "7", -6:1, 4241, 1148, 1, 0, 0);
add (48477,"Washington County", "TX", "Texas", "7", -6:1, 29127, 1148, 1, 0, 0);
add (13091,"Dodge County", "GA", "Georgia", "3", -5:1, 18108, 1387, 1, 0, 0);
add (48469,"Victoria County", "TX", "Texas", "7", -6:1, 82650, 1148, 1, 0, 0);
add (40113,"Osage County", "OK", "Oklahoma", "7", -6:1, 42838, 1148, 1, 0, 0);
add (47059,"Greene County", "TN", "Tennesee", "3", -5:1, 60502, 1387, 1, 0, 0);
add (29217,"Vernon County", "MO", "Mosourri", "6", -6:1, 19436, 1148, 1, 0, 0);
add (55111,"Sauk County", "WI", "Wisconnsin", "5", -6:1, 53369, 1148, 1, 0, 0);
add (31061,"Franklin County", "NE", "Nebraska", "6", -6:1, 3730, 1148, 1, 0, 0);
add (48495,"Winkler County", "TX", "Texas", "7", -6:1, 7964, 1148, 1, 0, 0);
add (30007,"Broadwater County", "MT", "Montana", "6", -7:1, 4132, 4647, 1, 0, 0);
add (20065,"Graham County", "KS", "Kansas", "6", -6:1, 3204, 1148, 1, 0, 0);
add (45021,"Cherokee County", "SC", "South Carolina", "2", -5:1, 49170, 1387, 1, 0, 0);
add (39061,"Hamilton County", "OH", "Ohio", "4", -5:1, 847403, 1387, 1, 0, 0);
add (56003,"Big Horn County", "WY", "Wyoming", "8", -7:1, 11380, 4647, 1, 0, 0);
add (13187,"Lumpkin County", "GA", "Georgia", "3", -5:1, 18981, 1387, 1, 0, 0);
add (19051,"Davis County", "IA", "Iowa", "5", -6:1, 8403, 1148, 1, 0, 0);
add (30017,"Custer County", "MT", "Montana", "6", -7:1, 12035, 4647, 1, 0, 0);
add (54093,"Tucker County", "WV", "West Virginia", "2", -5:1, 7631, 1387, 1, 0, 0);
add (20131,"Nemaha County", "KS", "Kansas", "6", -6:1, 10132, 1148, 1, 0, 0);
add (37015,"Bertie County", "NC", "North Carolina", "2", -5:1, 20459, 1387, 1, 0, 0);
add (55119,"Taylor County", "WI", "Wisconnsin", "5", -6:1, 19313, 1148, 1, 0, 0);
add (13137,"Habersham County", "GA", "Georgia", "3", -5:1, 31858, 1387, 1, 0, 0);
add (48031,"Blanco County", "TX", "Texas", "7", -6:1, 8400, 1148, 1, 0, 0);
add (16057,"Latah County", "ID", "Idaho", "8", -7:1, 32051, 4647, 1, 0, 0);
add (26029,"Charlevoix County", "MI", "Michigan", "4", -5:1, 24436, 1387, 1, 0, 0);
add (37103,"Jones County", "NC", "North Carolina", "2", -5:1, 9456, 1387, 1, 0, 0);
add (05015,"Carroll County", "AR", "Arkansas", "7", -6:1, 22534, 1148, 1, 0, 0);
add (48111,"Dallam County", "TX", "Texas", "7", -6:1, 6602, 1148, 1, 0, 0);
add (40141,"Tillman County", "OK", "Oklahoma", "7", -6:1, 9503, 1148, 1, 0, 0);
add (26111,"Midland County", "MI", "Michigan", "4", -5:1, 81842, 1387, 1, 0, 0);
add (18159,"Tipton County", "IN", "Indiana", "4", -5:1, 16724, 1387, 1, 0, 0);
add (48359,"Oldham County", "TX", "Texas", "7", -6:1, 2153, 1148, 1, 0, 0);
add (53051,"Pend Oreille County", "WA", "Washiington", "9", -8:1, 11526, 3240, 0, 1, 0);
add (45017,"Calhoun County", "SC", "South Carolina", "2", -5:1, 14051, 1387, 1, 0, 0);
add (42119,"Union County", "PA", "Pennsylvania", "1", -5:1, 40897, 1387, 1, 0, 0);
add (48485,"Wichita County", "TX", "Texas", "7", -6:1, 128904, 1148, 1, 0, 0);
add (30025,"Fallon County", "MT", "Montana", "6", -7:1, 2941, 4647, 1, 0, 0);
add (01051,"Elmore County", "AL", "Alabama", "3", -6:1, 61993, 1148, 1, 0, 0);
add (38093,"Stutsman County", "ND", "North Dakota", "5", -6:1, 20964, 1148, 1, 0, 0);
add (05105,"Perry County", "AR", "Arkansas", "7", -6:1, 9640, 1148, 1, 0, 0);
add (48081,"Coke County", "TX", "Texas", "7", -6:1, 3367, 1148, 1, 0, 0);
add (19171,"Tama County", "IA", "Iowa", "5", -6:1, 17739, 1148, 1, 0, 0);
add (29213,"Taney County", "MO", "Mosourri", "6", -6:1, 34504, 1148, 1, 0, 0);
add (31185,"York County", "NE", "Nebraska", "6", -6:1, 14512, 1148, 1, 0, 0);
add (39155,"Trumbull County", "OH", "Ohio", "4", -5:1, 225066, 1387, 1, 0, 0);
add (46107,"Potter County", "SD", "South Dakota", "5", -7:1, 2857, 4647, 1, 0, 0);
add (38023,"Divide County", "ND", "North Dakota", "5", -6:1, 2366, 1148, 1, 0, 0);
add (13067,"Cobb County", "GA", "Georgia", "3", -5:1, 566203, 1387, 1, 0, 0);
add (16035,"Clearwater County", "ID", "Idaho", "8", -7:1, 9310, 4647, 1, 0, 0);
add (54061,"Monongalia County", "WV", "West Virginia", "2", -5:1, 77505, 1387, 1, 0, 0);
add (50011,"Franklin County", "VT", "Vermont", "0", -5:1, 44017, 1387, 1, 0, 0);
add (32001,"Churchill County", "NV", "Nevada", "8", -8:1, 23293, 3240, 0, 1, 0);
add (47079,"Henry County", "TN", "Tennesee", "3", -5:1, 30066, 1387, 1, 0, 0);
add (26017,"Bay County", "MI", "Michigan", "4", -5:1, 110048, 1387, 1, 0, 0);
add (13017,"Ben Hill County", "GA", "Georgia", "3", -5:1, 17496, 1387, 1, 0, 0);
add (39059,"Guernsey County", "OH", "Ohio", "4", -5:1, 40994, 1387, 1, 0, 0);
add (05029,"Conway County", "AR", "Arkansas", "7", -6:1, 19920, 1148, 1, 0, 0);
add (18023,"Clinton County", "IN", "Indiana", "4", -5:1, 33215, 1387, 1, 0, 0);
add (46111,"Sanborn County", "SD", "South Dakota", "5", -7:1, 2740, 4647, 1, 0, 0);
add (51121,"Montgomery County", "VA", "Virginia", "2", -5:1, 75878, 1387, 1, 0, 0);
add (47001,"Anderson County", "TN", "Tennesee", "3", -5:1, 71116, 1387, 1, 0, 0);
add (21097,"Harrison County", "KY", "Kentucky", "4", -6:1, 17565, 1148, 1, 0, 0);
add (27083,"Lyon County", "MN", "Minnesota", "5", -6:1, 24339, 1148, 1, 0, 0);
add (22069,"Natchitoches Parish", "LA", "Louisiana", "7", -6:1, 37018, 1148, 1, 0, 0);
add (13305,"Wayne County", "GA", "Georgia", "3", -5:1, 25437, 1387, 1, 0, 0);
add (21067,"Fayette County", "KY", "Kentucky", "4", -6:1, 241749, 1148, 1, 0, 0);
add (27157,"Wabasha County", "MN", "Minnesota", "5", -6:1, 20943, 1148, 1, 0, 0);
add (41001,"Baker County", "OR", "Oregon", "9", -8:1, 16448, 3240, 0, 1, 0);
add (13283,"Treutlen County", "GA", "Georgia", "3", -5:1, 6003, 1387, 1, 0, 0);
add (47051,"Franklin County", "TN", "Tennesee", "3", -5:1, 37465, 1387, 1, 0, 0);
add (48283,"La Salle County", "TX", "Texas", "7", -6:1, 6034, 1148, 1, 0, 0);
add (17011,"Bureau County", "IL", "Illinois", "6", -6:1, 35530, 1148, 1, 0, 0);
add (26165,"Wexford County", "MI", "Michigan", "4", -5:1, 29185, 1387, 1, 0, 0);
add (40091,"McIntosh County", "OK", "Oklahoma", "7", -6:1, 19050, 1148, 1, 0, 0);
add (48209,"Hays County", "TX", "Texas", "7", -6:1, 88536, 1148, 1, 0, 0);
add (38007,"Billings County", "ND", "North Dakota", "5", -6:1, 1058, 1148, 1, 0, 0);
add (05017,"Chicot County", "AR", "Arkansas", "7", -6:1, 14841, 1148, 1, 0, 0);
add (38041,"Hettinger County", "ND", "North Dakota", "5", -6:1, 2924, 1148, 1, 0, 0);
add (13133,"Greene County", "GA", "Georgia", "3", -5:1, 13651, 1387, 1, 0, 0);
add (20193,"Thomas County", "KS", "Kansas", "6", -6:1, 8037, 1148, 1, 0, 0);
add (37149,"Polk County", "NC", "North Carolina", "2", -5:1, 16835, 1387, 1, 0, 0);
add (55081,"Monroe County", "WI", "Wisconnsin", "5", -6:1, 39532, 1148, 1, 0, 0);
add (46005,"Beadle County", "SD", "South Dakota", "5", -6:1, 17183, 1148, 1, 0, 0);
add (39097,"Madison County", "OH", "Ohio", "4", -5:1, 41576, 1387, 1, 0, 0);
add (48239,"Jackson County", "TX", "Texas", "7", -6:1, 13685, 1148, 1, 0, 0);
add (12097,"Osceola County", "FL", "Florida", "3", -5:1, 145666, 1387, 1, 0, 0);
add (37105,"Lee County", "NC", "North Carolina", "2", -5:1, 49328, 1387, 1, 0, 0);
add (48371,"Pecos County", "TX", "Texas", "7", -6:1, 16003, 1148, 1, 0, 0);
add (51810,"Virginia Beach city", "VA", "Virginia", "2", -5:1, 432380, 1387, 1, 0, 0);
add (40065,"Jackson County", "OK", "Oklahoma", "7", -6:1, 28771, 1148, 1, 0, 0);
add (13155,"Irwin County", "GA", "Georgia", "3", -5:1, 8982, 1387, 1, 0, 0);
add (20101,"Lane County", "KS", "Kansas", "6", -6:1, 2264, 1148, 1, 0, 0);
add (40017,"Canadian County", "OK", "Oklahoma", "7", -6:1, 85463, 1148, 1, 0, 0);
add (19073,"Greene County", "IA", "Iowa", "5", -6:1, 10065, 1148, 1, 0, 0);
add (25013,"Hampden County", "MA", "Massachusetts", "0", -5:1, 439609, 1387, 1, 0, 0);
add (04025,"Yavapai County", "AZ", "Arizona", "8", -7:1, 148511, 4647, 1, 0, 0);
add (47113,"Madison County", "TN", "Tennesee", "3", -6:1, 85954, 1148, 1, 0, 0);
add (18175,"Washington County", "IN", "Indiana", "4", -5:1, 27900, 1387, 1, 0, 0);
add (55005,"Barron County", "WI", "Wisconnsin", "5", -6:1, 43872, 1148, 1, 0, 0);
add (17109,"McDonough County", "IL", "Illinois", "6", -6:1, 33917, 1148, 1, 0, 0);
add (17113,"McLean County", "IL", "Illinois", "6", -6:1, 142652, 1148, 1, 0, 0);
add (13033,"Burke County", "GA", "Georgia", "3", -5:1, 22854, 1387, 1, 0, 0);
add (04023,"Santa Cruz County", "AZ", "Arizona", "8", -7:1, 38116, 4647, 1, 0, 0);
add (54067,"Nicholas County", "WV", "West Virginia", "2", -5:1, 27595, 1387, 1, 0, 0);
add (19135,"Monroe County", "IA", "Iowa", "5", -6:1, 8041, 1148, 1, 0, 0);
add (21083,"Graves County", "KY", "Kentucky", "4", -6:1, 35847, 1148, 1, 0, 0);
add (38067,"Pembina County", "ND", "North Dakota", "5", -6:1, 8485, 1148, 1, 0, 0);
add (19031,"Cedar County", "IA", "Iowa", "5", -6:1, 17977, 1148, 1, 0, 0);
add (21059,"Daviess County", "KY", "Kentucky", "4", -6:1, 91139, 1148, 1, 0, 0);
add (51580,"Covington city", "VA", "Virginia", "2", -5:1, 6857, 1387, 1, 0, 0);
add (42045,"Delaware County", "PA", "Pennsylvania", "1", -5:1, 542593, 1387, 1, 0, 0);
add (05009,"Boone County", "AR", "Arkansas", "7", -6:1, 31872, 1148, 1, 0, 0);
add (42103,"Pike County", "PA", "Pennsylvania", "1", -5:1, 40172, 1387, 1, 0, 0);
add (40151,"Woods County", "OK", "Oklahoma", "7", -6:1, 8366, 1148, 1, 0, 0);
add (13311,"White County", "GA", "Georgia", "3", -5:1, 17457, 1387, 1, 0, 0);
add (22065,"Madison Parish", "LA", "Louisiana", "7", -6:1, 12808, 1148, 1, 0, 0);
add (55091,"Pepin County", "WI", "Wisconnsin", "5", -6:1, 7118, 1148, 1, 0, 0);
add (28047,"Harrison County", "MS", "Missippi", "5", -6:1, 177981, 1148, 1, 0, 0);
add (38105,"Williams County", "ND", "North Dakota", "5", -6:1, 20150, 1148, 1, 0, 0);
add (12093,"Okeechobee County", "FL", "Florida", "3", -5:1, 31158, 1387, 1, 0, 0);
add (27043,"Faribault County", "MN", "Minnesota", "5", -6:1, 16244, 1148, 1, 0, 0);
add (29027,"Callaway County", "MO", "Mosourri", "6", -6:1, 37437, 1148, 1, 0, 0);
add (20081,"Haskell County", "KS", "Kansas", "6", -6:1, 3976, 1148, 1, 0, 0);
add (06067,"Sacramento County", "CA", "California", "9", -8:1, 1144202, 3240, 0, 1, 0);
add (20011,"Bourbon County", "KS", "Kansas", "6", -6:1, 15260, 1148, 1, 0, 0);
add (21129,"Lee County", "KY", "Kentucky", "4", -5:1, 8021, 1387, 1, 0, 0);
add (40101,"Muskogee County", "OK", "Oklahoma", "7", -6:1, 70004, 1148, 1, 0, 0);
add (35045,"San Juan County", "NM", "New Mexico", "8", -7:1, 106020, 4647, 1, 0, 0);
add (39021,"Champaign County", "OH", "Ohio", "4", -5:1, 38182, 1387, 1, 0, 0);
add (51045,"Craig County", "VA", "Virginia", "2", -5:1, 4882, 1387, 1, 0, 0);
add (40123,"Pontotoc County", "OK", "Oklahoma", "7", -6:1, 34591, 1148, 1, 0, 0);
add (48457,"Tyler County", "TX", "Texas", "7", -6:1, 20408, 1148, 1, 0, 0);
add (39077,"Huron County", "OH", "Ohio", "4", -5:1, 60293, 1387, 1, 0, 0);
add (28141,"Tishomingo County", "MS", "Missippi", "5", -6:1, 18654, 1148, 1, 0, 0);
add (21171,"Monroe County", "KY", "Kentucky", "4", -5:1, 11201, 1387, 1, 0, 0);
add (20025,"Clark County", "KS", "Kansas", "6", -6:1, 2361, 1148, 1, 0, 0);
add (30083,"Richland County", "MT", "Montana", "6", -7:1, 10105, 4647, 1, 0, 0);
add (45003,"Aiken County", "SC", "South Carolina", "2", -5:1, 134051, 1387, 1, 0, 0);
add (31113,"Logan County", "NE", "Nebraska", "6", -7:1, 880, 4647, 1, 0, 0);
add (51670,"Hopewell city", "VA", "Virginia", "2", -5:1, 22529, 1387, 1, 0, 0);
add (38053,"McKenzie County", "ND", "North Dakota", "5", -6:1, 5682, 1148, 1, 0, 0);
add (08075,"Logan County", "CO", "Colorado", "8", -7:1, 17890, 4647, 1, 0, 0);
add (17069,"Hardin County", "IL", "Illinois", "6", -6:1, 4902, 1148, 1, 0, 0);
add (02170,"Matanuska-Susitna Borough", "AK", "Alaska", "9", -9:1, 56258, 1174, 0, 0, 1);
add (28117,"Prentiss County", "MS", "Missippi", "5", -6:1, 24295, 1148, 1, 0, 0);
add (31073,"Gosper County", "NE", "Nebraska", "6", -6:1, 2329, 1148, 1, 0, 0);
add (48101,"Cottle County", "TX", "Texas", "7", -6:1, 1922, 1148, 1, 0, 0);
add (05011,"Bradley County", "AR", "Arkansas", "7", -6:1, 11433, 1148, 1, 0, 0);
add (40147,"Washington County", "OK", "Oklahoma", "7", -6:1, 47519, 1148, 1, 0, 0);
add (29063,"DeKalb County", "MO", "Mosourri", "6", -6:1, 11129, 1148, 1, 0, 0);
add (39109,"Miami County", "OH", "Ohio", "4", -5:1, 98147, 1387, 1, 0, 0);
add (05025,"Cleveland County", "AR", "Arkansas", "7", -6:1, 8452, 1148, 1, 0, 0);
add (05041,"Desha County", "AR", "Arkansas", "7", -6:1, 15110, 1148, 1, 0, 0);
add (18083,"Knox County", "IN", "Indiana", "4", -5:1, 39388, 1387, 1, 0, 0);
add (38005,"Benson County", "ND", "North Dakota", "5", -6:1, 6893, 1148, 1, 0, 0);
add (45055,"Kershaw County", "SC", "South Carolina", "2", -5:1, 48593, 1387, 1, 0, 0);
add (47095,"Lake County", "TN", "Tennesee", "3", -6:1, 8171, 1148, 1, 0, 0);
add (08101,"Pueblo County", "CO", "Colorado", "8", -7:1, 134867, 4647, 1, 0, 0);
add (01077,"Lauderdale County", "AL", "Alabama", "3", -6:1, 84325, 1148, 1, 0, 0);
add (30097,"Sweet Grass County", "MT", "Montana", "6", -7:1, 3407, 4647, 1, 0, 0);
add (31043,"Dakota County", "NE", "Nebraska", "6", -6:1, 18792, 1148, 1, 0, 0);
add (48301,"Loving County", "TX", "Texas", "7", -6:1, 114, 1148, 1, 0, 0);
add (22025,"Catahoula Parish", "LA", "Louisiana", "7", -6:1, 11064, 1148, 1, 0, 0);
add (02016,"Aleutians West Census Area", "AK", "Alaska", "9", -9:1, 3879, 1174, 0, 0, 1);
add (30079,"Prairie County", "MT", "Montana", "6", -7:1, 1333, 4647, 1, 0, 0);
add (31135,"Perkins County", "NE", "Nebraska", "6", -7:1, 3171, 4647, 1, 0, 0);
add (06039,"Madera County", "CA", "California", "9", -8:1, 114748, 3240, 0, 1, 0);
add (18119,"Owen County", "IN", "Indiana", "4", -5:1, 20419, 1387, 1, 0, 0);
add (21149,"McLean County", "KY", "Kentucky", "4", -5:1, 9845, 1387, 1, 0, 0);
add (12119,"Sumter County", "FL", "Florida", "3", -5:1, 40426, 1387, 1, 0, 0);
add (17181,"Union County", "IL", "Illinois", "6", -6:1, 17996, 1148, 1, 0, 0);
add (28149,"Warren County", "MS", "Missippi", "5", -6:1, 49404, 1148, 1, 0, 0);
add (22121,"West Baton Rouge Parish", "LA", "Louisiana", "7", -6:1, 20683, 1148, 1, 0, 0);
add (06001,"Alameda County", "CA", "California", "9", -8:1, 1400322, 3240, 0, 1, 0);
add (18019,"Clark County", "IN", "Indiana", "4", -5:1, 93805, 1387, 1, 0, 0);
add (27173,"Yellow Medicine County", "MN", "Minnesota", "5", -6:1, 11416, 1148, 1, 0, 0);
add (17013,"Calhoun County", "IL", "Illinois", "6", -6:1, 4981, 1148, 1, 0, 0);
add (01017,"Chambers County", "AL", "Alabama", "3", -6:1, 36713, 1148, 1, 0, 0);
add (48131,"Duval County", "TX", "Texas", "7", -6:1, 13662, 1148, 1, 0, 0);
add (06051,"Mono County", "CA", "California", "9", -8:1, 10288, 3240, 0, 1, 0);
add (35003,"Catron County", "NM", "New Mexico", "8", -7:1, 2845, 4647, 1, 0, 0);
add (05129,"Searcy County", "AR", "Arkansas", "7", -6:1, 7761, 1148, 1, 0, 0);
add (55097,"Portage County", "WI", "Wisconnsin", "5", -6:1, 64752, 1148, 1, 0, 0);
add (21157,"Marshall County", "KY", "Kentucky", "4", -5:1, 30312, 1387, 1, 0, 0);
add (31053,"Dodge County", "NE", "Nebraska", "6", -6:1, 35333, 1148, 1, 0, 0);
add (40063,"Hughes County", "OK", "Oklahoma", "7", -6:1, 14081, 1148, 1, 0, 0);
add (05019,"Clark County", "AR", "Arkansas", "7", -6:1, 21933, 1148, 1, 0, 0);
add (41031,"Jefferson County", "OR", "Oregon", "9", -8:1, 16627, 3240, 0, 1, 0);
add (23021,"Piscataquis County", "ME", "Maine", "0", -5:1, 18282, 1387, 1, 0, 0);
add (13119,"Franklin County", "GA", "Georgia", "3", -5:1, 19080, 1387, 1, 0, 0);
add (31165,"Sioux County", "NE", "Nebraska", "6", -7:1, 1486, 4647, 1, 0, 0);
add (29177,"Ray County", "MO", "Mosourri", "6", -6:1, 23708, 1148, 1, 0, 0);
add (23011,"Kennebec County", "ME", "Maine", "0", -5:1, 115207, 1387, 1, 0, 0);
add (27025,"Chisago County", "MN", "Minnesota", "5", -6:1, 40852, 1148, 1, 0, 0);
add (40075,"Kiowa County", "OK", "Oklahoma", "7", -6:1, 10613, 1148, 1, 0, 0);
add (55085,"Oneida County", "WI", "Wisconnsin", "5", -6:1, 35672, 1148, 1, 0, 0);
add (19113,"Linn County", "IA", "Iowa", "5", -6:1, 182651, 1148, 1, 0, 0);
add (53039,"Klickitat County", "WA", "Washiington", "9", -8:1, 19295, 3240, 0, 1, 0);
add (54023,"Grant County", "WV", "West Virginia", "2", -5:1, 11098, 1387, 1, 0, 0);
add (38055,"McLean County", "ND", "North Dakota", "5", -6:1, 9704, 1148, 1, 0, 0);
add (01023,"Choctaw County", "AL", "Alabama", "3", -6:1, 15917, 1148, 1, 0, 0);
add (21051,"Clay County", "KY", "Kentucky", "4", -6:1, 22799, 1148, 1, 0, 0);
add (09005,"Litchfield County", "CT", "Connecticut", "0", -5:1, 181277, 1387, 1, 0, 0);
add (40025,"Cimarron County", "OK", "Oklahoma", "7", -6:1, 2959, 1148, 1, 0, 0);
add (18015,"Carroll County", "IN", "Indiana", "4", -5:1, 20010, 1387, 1, 0, 0);
add (21119,"Knott County", "KY", "Kentucky", "4", -5:1, 17989, 1387, 1, 0, 0);
add (17077,"Jackson County", "IL", "Illinois", "6", -6:1, 60410, 1148, 1, 0, 0);
add (47075,"Haywood County", "TN", "Tennesee", "3", -5:1, 19525, 1387, 1, 0, 0);
add (53029,"Island County", "WA", "Washiington", "9", -8:1, 70319, 3240, 0, 1, 0);
add (35019,"Guadalupe County", "NM", "New Mexico", "8", -7:1, 4050, 4647, 1, 0, 0);
add (31047,"Dawson County", "NE", "Nebraska", "6", -6:1, 23183, 1148, 1, 0, 0);
add (35061,"Valencia County", "NM", "New Mexico", "8", -7:1, 64626, 4647, 1, 0, 0);
add (27053,"Hennepin County", "MN", "Minnesota", "5", -6:1, 1059669, 1148, 1, 0, 0);
add (39151,"Stark County", "OH", "Ohio", "4", -5:1, 373112, 1387, 1, 0, 0);
add (36015,"Chemung County", "NY", "New York", "1", -5:1, 92021, 1387, 1, 0, 0);
add (37011,"Avery County", "NC", "North Carolina", "2", -5:1, 15742, 1387, 1, 0, 0);
add (47179,"Washington County", "TN", "Tennesee", "3", -6:1, 102211, 1148, 1, 0, 0);
add (05075,"Lawrence County", "AR", "Arkansas", "7", -6:1, 17304, 1148, 1, 0, 0);
add (26097,"Mackinac County", "MI", "Michigan", "4", -5:1, 11097, 1387, 1, 0, 0);
add (54031,"Hardy County", "WV", "West Virginia", "2", -5:1, 11829, 1387, 1, 0, 0);
add (45049,"Hampton County", "SC", "South Carolina", "2", -5:1, 19200, 1387, 1, 0, 0);
add (47085,"Humphreys County", "TN", "Tennesee", "3", -5:1, 17059, 1387, 1, 0, 0);
add (28075,"Lauderdale County", "MS", "Missippi", "5", -6:1, 76143, 1148, 1, 0, 0);
add (48129,"Donley County", "TX", "Texas", "7", -6:1, 3822, 1148, 1, 0, 0);
add (09001,"Fairfield County", "CT", "Connecticut", "0", -5:1, 838362, 1148, 1, 0, 0);
add (39107,"Mercer County", "OH", "Ohio", "4", -5:1, 41198, 1387, 1, 0, 0);
add (31045,"Dawes County", "NE", "Nebraska", "6", -6:1, 8979, 1148, 1, 0, 0);
add (38061,"Mountrail County", "ND", "North Dakota", "5", -6:1, 6633, 1148, 1, 0, 0);
add (31167,"Stanton County", "NE", "Nebraska", "6", -7:1, 6215, 4647, 1, 0, 0);
add (26115,"Monroe County", "MI", "Michigan", "4", -5:1, 143499, 1387, 1, 0, 0);
add (21231,"Wayne County", "KY", "Kentucky", "4", -5:1, 19107, 1387, 1, 0, 0);
add (29057,"Dade County", "MO", "Mosourri", "6", -6:1, 7892, 1148, 1, 0, 0);
add (51149,"Prince George County", "VA", "Virginia", "2", -5:1, 30135, 1387, 1, 0, 0);
add (12019,"Clay County", "FL", "Florida", "3", -5:1, 137455, 1387, 1, 0, 0);
add (18101,"Martin County", "IN", "Indiana", "4", -5:1, 10531, 1387, 1, 0, 0);
add (49041,"Sevier County", "UT", "Utah", "8", -7:1, 18452, 4647, 1, 0, 0);
add (22059,"La Salle Parish", "LA", "Louisiana", "7", -6:1, 13661, 1148, 1, 0, 0);
add (27027,"Clay County", "MN", "Minnesota", "5", -6:1, 51599, 1148, 1, 0, 0);
add (22077,"Pointe Coupee Parish", "LA", "Louisiana", "7", -6:1, 23565, 1148, 1, 0, 0);
add (17103,"Lee County", "IL", "Illinois", "6", -6:1, 36021, 1148, 1, 0, 0);
add (01047,"Dallas County", "AL", "Alabama", "3", -6:1, 46768, 1148, 1, 0, 0);
add (47135,"Perry County", "TN", "Tennesee", "3", -6:1, 7508, 1148, 1, 0, 0);
add (05107,"Phillips County", "AR", "Arkansas", "7", -6:1, 27363, 1148, 1, 0, 0);
add (37107,"Lenoir County", "NC", "North Carolina", "2", -5:1, 59046, 1387, 1, 0, 0);
add (28001,"Adams County", "MS", "Missippi", "5", -6:1, 34225, 1148, 1, 0, 0);
add (21117,"Kenton County", "KY", "Kentucky", "4", -5:1, 146732, 1387, 1, 0, 0);
add (48057,"Calhoun County", "TX", "Texas", "7", -6:1, 20596, 1148, 1, 0, 0);
add (46079,"Lake County", "SD", "South Dakota", "5", -7:1, 11139, 4647, 1, 0, 0);
add (48345,"Motley County", "TX", "Texas", "7", -6:1, 1305, 1148, 1, 0, 0);
add (10001,"Kent County", "DE", "Delaware", "1", -5:1, 124089, 1387, 1, 0, 0);
add (39013,"Belmont County", "OH", "Ohio", "4", -5:1, 69175, 1387, 1, 0, 0);
add (29197,"Schuyler County", "MO", "Mosourri", "6", -6:1, 4443, 1148, 1, 0, 0);
add (46017,"Buffalo County", "SD", "South Dakota", "5", -6:1, 1738, 1148, 1, 0, 0);
add (13303,"Washington County", "GA", "Georgia", "3", -5:1, 20033, 1387, 1, 0, 0);
add (47007,"Bledsoe County", "TN", "Tennesee", "3", -5:1, 10795, 1387, 1, 0, 0);
add (26083,"Keweenaw County", "MI", "Michigan", "4", -5:1, 2077, 1387, 1, 0, 0);
add (48411,"San Saba County", "TX", "Texas", "7", -6:1, 5615, 1148, 1, 0, 0);
add (51515,"Bedford city", "VA", "Virginia", "2", -5:1, 6317, 1387, 1, 0, 0);
add (48399,"Runnels County", "TX", "Texas", "7", -6:1, 11507, 1148, 1, 0, 0);
add (51760,"Richmond city", "VA", "Virginia", "2", -5:1, 194173, 1387, 1, 0, 0);
add (31063,"Frontier County", "NE", "Nebraska", "6", -6:1, 3082, 1148, 1, 0, 0);
add (31033,"Cheyenne County", "NE", "Nebraska", "6", -6:1, 9476, 1148, 1, 0, 0);
add (28039,"George County", "MS", "Missippi", "5", -6:1, 19645, 1148, 1, 0, 0);
add (29055,"Crawford County", "MO", "Mosourri", "6", -6:1, 22165, 1148, 1, 0, 0);
add (48179,"Gray County", "TX", "Texas", "7", -6:1, 23603, 1148, 1, 0, 0);
add (48337,"Montague County", "TX", "Texas", "7", -6:1, 18539, 1148, 1, 0, 0);
add (31109,"Lancaster County", "NE", "Nebraska", "6", -7:1, 235589, 4647, 1, 0, 0);
add (13099,"Early County", "GA", "Georgia", "3", -5:1, 12197, 1387, 1, 0, 0);
add (29201,"Scott County", "MO", "Mosourri", "6", -6:1, 40262, 1148, 1, 0, 0);
add (22017,"Caddo Parish", "LA", "Louisiana", "7", -6:1, 242471, 1148, 1, 0, 0);
add (48139,"Ellis County", "TX", "Texas", "7", -6:1, 103638, 1148, 1, 0, 0);
add (48175,"Goliad County", "TX", "Texas", "7", -6:1, 6998, 1148, 1, 0, 0);
add (36075,"Oswego County", "NY", "New York", "1", -5:1, 124006, 1387, 1, 0, 0);
add (21041,"Carroll County", "KY", "Kentucky", "4", -6:1, 9603, 1148, 1, 0, 0);
add (48405,"San Augustine County", "TX", "Texas", "7", -6:1, 8086, 1148, 1, 0, 0);
add (17043,"DuPage County", "IL", "Illinois", "6", -6:1, 880491, 1148, 1, 0, 0);
add (27163,"Washington County", "MN", "Minnesota", "5", -6:1, 196486, 1148, 1, 0, 0);
add (46103,"Pennington County", "SD", "South Dakota", "5", -7:1, 87702, 4647, 1, 0, 0);
add (48389,"Reeves County", "TX", "Texas", "7", -6:1, 14478, 1148, 1, 0, 0);
add (39041,"Delaware County", "OH", "Ohio", "4", -5:1, 92209, 1387, 1, 0, 0);
add (48425,"Somervell County", "TX", "Texas", "7", -6:1, 6421, 1148, 1, 0, 0);
add (21101,"Henderson County", "KY", "Kentucky", "4", -6:1, 44457, 1148, 1, 0, 0);
add (37119,"Mecklenburg County", "NC", "North Carolina", "2", -5:1, 630848, 1387, 1, 0, 0);
add (31069,"Garden County", "NE", "Nebraska", "6", -6:1, 2138, 1148, 1, 0, 0);
add (42051,"Fayette County", "PA", "Pennsylvania", "1", -5:1, 144847, 1387, 1, 0, 0);
add (36025,"Delaware County", "NY", "New York", "1", -5:1, 46086, 1387, 1, 0, 0);
add (28071,"Lafayette County", "MS", "Missippi", "5", -6:1, 34555, 1148, 1, 0, 0);
add (48201,"Harris County", "TX", "Texas", "7", -6:1, 3206063, 1148, 1, 0, 0);
add (28045,"Hancock County", "MS", "Missippi", "5", -6:1, 40327, 1148, 1, 0, 0);
add (37197,"Yadkin County", "NC", "North Carolina", "2", -5:1, 34955, 1387, 1, 0, 0);
add (19173,"Taylor County", "IA", "Iowa", "5", -6:1, 7153, 1148, 1, 0, 0);
add (13223,"Paulding County", "GA", "Georgia", "3", -5:1, 73534, 1387, 1, 0, 0);
add (47155,"Sevier County", "TN", "Tennesee", "3", -6:1, 64505, 1148, 1, 0, 0);
add (34003,"Bergen County", "NJ", "New Jersey", "0", -5:1, 858529, 1387, 1, 0, 0);
add (05071,"Johnson County", "AR", "Arkansas", "7", -6:1, 21403, 1148, 1, 0, 0);
add (48295,"Lipscomb County", "TX", "Texas", "7", -6:1, 2973, 1148, 1, 0, 0);
add (45079,"Richland County", "SC", "South Carolina", "2", -5:1, 307056, 1387, 1, 0, 0);
add (30109,"Wibaux County", "MT", "Montana", "6", -7:1, 1148, 4647, 1, 0, 0);
add (51735,"Poquoson city", "VA", "Virginia", "2", -5:1, 11455, 1387, 1, 0, 0);
add (17083,"Jersey County", "IL", "Illinois", "6", -6:1, 21373, 1148, 1, 0, 0);
add (45029,"Colleton County", "SC", "South Carolina", "2", -5:1, 37364, 1387, 1, 0, 0);
add (21105,"Hickman County", "KY", "Kentucky", "4", -6:1, 5247, 1148, 1, 0, 0);
add (45031,"Darlington County", "SC", "South Carolina", "2", -5:1, 66366, 1387, 1, 0, 0);
add (20047,"Edwards County", "KS", "Kansas", "6", -6:1, 3312, 1148, 1, 0, 0);
add (17179,"Tazewell County", "IL", "Illinois", "6", -6:1, 127958, 1148, 1, 0, 0);
add (08095,"Phillips County", "CO", "Colorado", "8", -7:1, 4325, 4647, 1, 0, 0);
add (51683,"Manassas city", "VA", "Virginia", "2", -5:1, 35336, 1387, 1, 0, 0);
add (27013,"Blue Earth County", "MN", "Minnesota", "5", -6:1, 53767, 1148, 1, 0, 0);
add (53057,"Skagit County", "WA", "Washiington", "9", -8:1, 99357, 3240, 0, 1, 0);
add (19081,"Hancock County", "IA", "Iowa", "5", -6:1, 12044, 1148, 1, 0, 0);
add (13131,"Grady County", "GA", "Georgia", "3", -5:1, 21501, 1387, 1, 0, 0);
add (48379,"Rains County", "TX", "Texas", "7", -6:1, 8618, 1148, 1, 0, 0);
add (37117,"Martin County", "NC", "North Carolina", "2", -5:1, 26192, 1387, 1, 0, 0);
add (21233,"Webster County", "KY", "Kentucky", "4", -5:1, 13482, 1387, 1, 0, 0);
add (37081,"Guilford County", "NC", "North Carolina", "2", -5:1, 387722, 1387, 1, 0, 0);
add (08107,"Routt County", "CO", "Colorado", "8", -7:1, 17514, 4647, 1, 0, 0);
add (21191,"Pendleton County", "KY", "Kentucky", "4", -5:1, 13703, 1387, 1, 0, 0);
add (21115,"Johnson County", "KY", "Kentucky", "4", -6:1, 24022, 1148, 1, 0, 0);
add (27099,"Mower County", "MN", "Minnesota", "5", -6:1, 37039, 1148, 1, 0, 0);
add (08121,"Washington County", "CO", "Colorado", "8", -7:1, 4576, 4647, 1, 0, 0);
add (08007,"Archuleta County", "CO", "Colorado", "8", -7:1, 9113, 4647, 1, 0, 0);
add (36099,"Seneca County", "NY", "New York", "1", -5:1, 31943, 1387, 1, 0, 0);
add (51065,"Fluvanna County", "VA", "Virginia", "2", -5:1, 18575, 1387, 1, 0, 0);
add (37087,"Haywood County", "NC", "North Carolina", "2", -5:1, 51422, 1387, 1, 0, 0);
add (55049,"Iowa County", "WI", "Wisconnsin", "5", -6:1, 22422, 1148, 1, 0, 0);
add (27121,"Pope County", "MN", "Minnesota", "5", -6:1, 10886, 1148, 1, 0, 0);
add (46113,"Shannon County", "SD", "South Dakota", "5", -7:1, 12183, 4647, 1, 0, 0);
add (18117,"Orange County", "IN", "Indiana", "4", -5:1, 19606, 1387, 1, 0, 0);
add (46053,"Gregory County", "SD", "South Dakota", "5", -6:1, 4948, 1148, 1, 0, 0);
add (47021,"Cheatham County", "TN", "Tennesee", "3", -5:1, 35344, 1387, 1, 0, 0);
add (08117,"Summit County", "CO", "Colorado", "8", -7:1, 18749, 4647, 1, 0, 0);
add (31003,"Antelope County", "NE", "Nebraska", "6", -6:1, 7181, 1148, 1, 0, 0);
add (46045,"Edmunds County", "SD", "South Dakota", "5", -6:1, 4219, 1148, 1, 0, 0);
add (45051,"Horry County", "SC", "South Carolina", "2", -5:1, 174762, 1387, 1, 0, 0);
add (27039,"Dodge County", "MN", "Minnesota", "5", -6:1, 17209, 1148, 1, 0, 0);
add (42015,"Bradford County", "PA", "Pennsylvania", "1", -5:1, 62459, 1387, 1, 0, 0);
add (19025,"Calhoun County", "IA", "Iowa", "5", -6:1, 11378, 1148, 1, 0, 0);
add (47119,"Maury County", "TN", "Tennesee", "3", -6:1, 69633, 1148, 1, 0, 0);
add (48089,"Colorado County", "TX", "Texas", "7", -6:1, 19021, 1148, 1, 0, 0);
add (28147,"Walthall County", "MS", "Missippi", "5", -6:1, 14369, 1148, 1, 0, 0);
add (05059,"Hot Spring County", "AR", "Arkansas", "7", -6:1, 29035, 1148, 1, 0, 0);
add (37059,"Davie County", "NC", "North Carolina", "2", -5:1, 32095, 1387, 1, 0, 0);
add (26055,"Grand Traverse County", "MI", "Michigan", "4", -5:1, 74134, 1387, 1, 0, 0);
add (51001,"Accomack County", "VA", "Virginia", "2", -5:1, 32245, 1387, 1, 0, 0);
add (51036,"Charles City County", "VA", "Virginia", "2", -5:1, 7092, 1387, 1, 0, 0);
add (30023,"Deer Lodge County", "MT", "Montana", "6", -7:1, 9999, 4647, 1, 0, 0);
add (48023,"Baylor County", "TX", "Texas", "7", -6:1, 4152, 1148, 1, 0, 0);
add (22021,"Caldwell Parish", "LA", "Louisiana", "7", -6:1, 10364, 1148, 1, 0, 0);
add (25005,"Bristol County", "MA", "Massachusetts", "0", -5:1, 517543, 1387, 1, 0, 0);
add (12037,"Franklin County", "FL", "Florida", "3", -5:1, 10079, 1387, 1, 0, 0);
add (28163,"Yazoo County", "MS", "Missippi", "5", -6:1, 25510, 1148, 1, 0, 0);
add (01049,"DeKalb County", "AL", "Alabama", "3", -6:1, 58454, 1148, 1, 0, 0);
add (51041,"Chesterfield County", "VA", "Virginia", "2", -5:1, 245915, 1387, 1, 0, 0);
add (22023,"Cameron Parish", "LA", "Louisiana", "7", -6:1, 9063, 1148, 1, 0, 0);
add (20185,"Stafford County", "KS", "Kansas", "6", -6:1, 5000, 1148, 1, 0, 0);
add (30015,"Chouteau County", "MT", "Montana", "6", -7:1, 5187, 4647, 1, 0, 0);
add (48039,"Brazoria County", "TX", "Texas", "7", -6:1, 230335, 1148, 1, 0, 0);
add (10005,"Sussex County", "DE", "Delaware", "1", -5:1, 136707, 1387, 1, 0, 0);
add (56025,"Natrona County", "WY", "Wyoming", "8", -7:1, 63341, 4647, 1, 0, 0);
add (48017,"Bailey County", "TX", "Texas", "7", -6:1, 6907, 1148, 1, 0, 0);
add (19033,"Cerro Gordo County", "IA", "Iowa", "5", -6:1, 46159, 1148, 1, 0, 0);
add (35007,"Colfax County", "NM", "New Mexico", "8", -7:1, 13597, 4647, 1, 0, 0);
add (42031,"Clarion County", "PA", "Pennsylvania", "1", -5:1, 41841, 1387, 1, 0, 0);
add (39029,"Columbiana County", "OH", "Ohio", "4", -5:1, 111521, 1387, 1, 0, 0);
add (36001,"Albany County", "NY", "New York", "1", -5:1, 292586, 1387, 1, 0, 0);
add (27033,"Cottonwood County", "MN", "Minnesota", "5", -6:1, 12045, 1148, 1, 0, 0);
add (42115,"Susquehanna County", "PA", "Pennsylvania", "1", -5:1, 42144, 1387, 1, 0, 0);
add (38081,"Sargent County", "ND", "North Dakota", "5", -6:1, 4457, 1148, 1, 0, 0);
add (48147,"Fannin County", "TX", "Texas", "7", -6:1, 28129, 1148, 1, 0, 0);
add (24003,"Anne Arundel County", "MD", "Maryland", "2", -5:1, 476060, 1387, 1, 0, 0);
add (22085,"Sabine Parish", "LA", "Louisiana", "7", -6:1, 23824, 1148, 1, 0, 0);
add (27171,"Wright County", "MN", "Minnesota", "5", -6:1, 85123, 1148, 1, 0, 0);
add (01117,"Shelby County", "AL", "Alabama", "3", -6:1, 140715, 1148, 1, 0, 0);
add (29135,"Moniteau County", "MO", "Mosourri", "6", -6:1, 13263, 1148, 1, 0, 0);
add (18069,"Huntington County", "IN", "Indiana", "4", -5:1, 37259, 1387, 1, 0, 0);
add (28085,"Lincoln County", "MS", "Missippi", "5", -6:1, 31771, 1148, 1, 0, 0);
add (35055,"Taos County", "NM", "New Mexico", "8", -7:1, 26815, 4647, 1, 0, 0);
add (12011,"Broward County", "FL", "Florida", "3", -5:1, 1503407, 1387, 1, 0, 0);
add (51077,"Grayson County", "VA", "Virginia", "2", -5:1, 16118, 1387, 1, 0, 0);
add (29203,"Shannon County", "MO", "Mosourri", "6", -6:1, 8252, 1148, 1, 0, 0);
add (48171,"Gillespie County", "TX", "Texas", "7", -6:1, 20045, 1148, 1, 0, 0);
add (17131,"Mercer County", "IL", "Illinois", "6", -6:1, 17640, 1148, 1, 0, 0);
add (40041,"Delaware County", "OK", "Oklahoma", "7", -6:1, 34154, 1148, 1, 0, 0);
add (18065,"Henry County", "IN", "Indiana", "4", -5:1, 48785, 1387, 1, 0, 0);
add (54037,"Jefferson County", "WV", "West Virginia", "2", -5:1, 41368, 1387, 1, 0, 0);
add (18135,"Randolph County", "IN", "Indiana", "4", -5:1, 27628, 1387, 1, 0, 0);
add (28121,"Rankin County", "MS", "Missippi", "5", -6:1, 109613, 1148, 1, 0, 0);
add (45041,"Florence County", "SC", "South Carolina", "2", -5:1, 124904, 1387, 1, 0, 0);
add (48431,"Sterling County", "TX", "Texas", "7", -6:1, 1364, 1148, 1, 0, 0);
add (29091,"Howell County", "MO", "Mosourri", "6", -6:1, 35776, 1148, 1, 0, 0);
add (37199,"Yancey County", "NC", "North Carolina", "2", -5:1, 16607, 1387, 1, 0, 0);
add (38065,"Oliver County", "ND", "North Dakota", "5", -6:1, 2215, 1148, 1, 0, 0);
add (46105,"Perkins County", "SD", "South Dakota", "5", -7:1, 3505, 4647, 1, 0, 0);
add (13015,"Bartow County", "GA", "Georgia", "3", -5:1, 71929, 1387, 1, 0, 0);
add (51175,"Southampton County", "VA", "Virginia", "2", -5:1, 17450, 1387, 1, 0, 0);
add (29017,"Bollinger County", "MO", "Mosourri", "6", -6:1, 11513, 1148, 1, 0, 0);
add (31097,"Johnson County", "NE", "Nebraska", "6", -7:1, 4564, 4647, 1, 0, 0);
add (31039,"Cuming County", "NE", "Nebraska", "6", -6:1, 9993, 1148, 1, 0, 0);
add (21125,"Laurel County", "KY", "Kentucky", "4", -5:1, 50734, 1387, 1, 0, 0);
add (38001,"Adams County", "ND", "North Dakota", "5", -6:1, 2714, 1148, 1, 0, 0);
add (39047,"Fayette County", "OH", "Ohio", "4", -5:1, 28493, 1387, 1, 0, 0);
add (06057,"Nevada County", "CA", "California", "9", -8:1, 91334, 3240, 0, 1, 0);
add (20001,"Allen County", "KS", "Kansas", "6", -6:1, 14556, 1148, 1, 0, 0);
add (48353,"Nolan County", "TX", "Texas", "7", -6:1, 16501, 1148, 1, 0, 0);
add (13211,"Morgan County", "GA", "Georgia", "3", -5:1, 15091, 1387, 1, 0, 0);
add (08015,"Chaffee County", "CO", "Colorado", "8", -7:1, 15075, 4647, 1, 0, 0);
add (39007,"Ashtabula County", "OH", "Ohio", "4", -5:1, 103300, 1387, 1, 0, 0);
add (28107,"Panola County", "MS", "Missippi", "5", -6:1, 33400, 1148, 1, 0, 0);
add (56005,"Campbell County", "WY", "Wyoming", "8", -7:1, 32465, 4647, 1, 0, 0);
add (12055,"Highlands County", "FL", "Florida", "3", -5:1, 75206, 1387, 1, 0, 0);
add (45057,"Lancaster County", "SC", "South Carolina", "2", -5:1, 58887, 1387, 1, 0, 0);
add (39119,"Muskingum County", "OH", "Ohio", "4", -5:1, 84470, 1387, 1, 0, 0);
add (31015,"Boyd County", "NE", "Nebraska", "6", -6:1, 2565, 1148, 1, 0, 0);
add (19001,"Adair County", "IA", "Iowa", "5", -6:1, 8064, 1387, 1, 0, 0);
add (25007,"Dukes County", "MA", "Massachusetts", "0", -5:1, 13888, 1387, 1, 0, 0);
add (26141,"Presque Isle County", "MI", "Michigan", "4", -5:1, 14424, 1387, 1, 0, 0);
add (31141,"Platte County", "NE", "Nebraska", "6", -7:1, 30737, 4647, 1, 0, 0);
add (26139,"Ottawa County", "MI", "Michigan", "4", -5:1, 224357, 1387, 1, 0, 0);
add (08113,"San Miguel County", "CO", "Colorado", "8", -7:1, 5437, 4647, 1, 0, 0);
add (06021,"Glenn County", "CA", "California", "9", -8:1, 26234, 3240, 0, 1, 0);
add (23029,"Washington County", "ME", "Maine", "0", -5:1, 35502, 1387, 1, 0, 0);
add (56039,"Teton County", "WY", "Wyoming", "8", -7:1, 14163, 4647, 1, 0, 0);
add (51097,"King and Queen County", "VA", "Virginia", "2", -5:1, 6529, 1387, 1, 0, 0);
add (02220,"Sitka Borough", "AK", "Alaska", "9", -9:1, 8338, 1174, 0, 0, 1);
add (41057,"Tillamook County", "OR", "Oregon", "9", -8:1, 24356, 3240, 0, 1, 0);
add (35005,"Chaves County", "NM", "New Mexico", "8", -7:1, 62505, 4647, 1, 0, 0);
add (39057,"Greene County", "OH", "Ohio", "4", -5:1, 146607, 1387, 1, 0, 0);
add (47053,"Gibson County", "TN", "Tennesee", "3", -5:1, 48186, 1387, 1, 0, 0);
add (30049,"Lewis and Clark County", "MT", "Montana", "6", -7:1, 53655, 4647, 1, 0, 0);
add (20089,"Jewell County", "KS", "Kansas", "6", -6:1, 3867, 1148, 1, 0, 0);
add (05043,"Drew County", "AR", "Arkansas", "7", -6:1, 17575, 1148, 1, 0, 0);
add (24021,"Frederick County", "MD", "Maryland", "2", -5:1, 186777, 1387, 1, 0, 0);
add (17061,"Greene County", "IL", "Illinois", "6", -6:1, 15549, 1148, 1, 0, 0);
add (55017,"Chippewa County", "WI", "Wisconnsin", "5", -6:1, 54574, 1148, 1, 0, 0);
add (48235,"Irion County", "TX", "Texas", "7", -6:1, 1739, 1148, 1, 0, 0);
add (47111,"Macon County", "TN", "Tennesee", "3", -6:1, 18181, 1148, 1, 0, 0);
add (18077,"Jefferson County", "IN", "Indiana", "4", -5:1, 31466, 1387, 1, 0, 0);
add (47177,"Warren County", "TN", "Tennesee", "3", -6:1, 36160, 1148, 1, 0, 0);
add (16017,"Bonner County", "ID", "Idaho", "8", -7:1, 35226, 4647, 1, 0, 0);
add (35057,"Torrance County", "NM", "New Mexico", "8", -7:1, 15433, 4647, 1, 0, 0);
add (26153,"Schoolcraft County", "MI", "Michigan", "4", -5:1, 8805, 1387, 1, 0, 0);
add (05073,"Lafayette County", "AR", "Arkansas", "7", -6:1, 8926, 1148, 1, 0, 0);
add (49053,"Washington County", "UT", "Utah", "8", -7:1, 82115, 4647, 1, 0, 0);
add (51067,"Franklin County", "VA", "Virginia", "2", -5:1, 44538, 1387, 1, 0, 0);
add (17105,"Livingston County", "IL", "Illinois", "6", -6:1, 39702, 1148, 1, 0, 0);
add (08021,"Conejos County", "CO", "Colorado", "8", -7:1, 7972, 4647, 1, 0, 0);
add (31079,"Hall County", "NE", "Nebraska", "6", -6:1, 51851, 1148, 1, 0, 0);
add (35049,"Santa Fe County", "NM", "New Mexico", "8", -7:1, 123386, 4647, 1, 0, 0);
add (48497,"Wise County", "TX", "Texas", "7", -6:1, 44135, 1148, 1, 0, 0);
add (48397,"Rockwall County", "TX", "Texas", "7", -6:1, 37174, 1148, 1, 0, 0);
add (20053,"Ellsworth County", "KS", "Kansas", "6", -6:1, 6285, 1148, 1, 0, 0);
add (22107,"Tensas Parish", "LA", "Louisiana", "7", -6:1, 6631, 1148, 1, 0, 0);
add (31035,"Clay County", "NE", "Nebraska", "6", -6:1, 7147, 1148, 1, 0, 0);
add (19119,"Lyon County", "IA", "Iowa", "5", -6:1, 12012, 1148, 1, 0, 0);
add (28053,"Humphreys County", "MS", "Missippi", "5", -6:1, 11344, 1148, 1, 0, 0);
add (19131,"Mitchell County", "IA", "Iowa", "5", -6:1, 11012, 1148, 1, 0, 0);
add (37039,"Cherokee County", "NC", "North Carolina", "2", -5:1, 22758, 1387, 1, 0, 0);
add (31139,"Pierce County", "NE", "Nebraska", "6", -7:1, 7914, 4647, 1, 0, 0);
add (22083,"Richland Parish", "LA", "Louisiana", "7", -6:1, 21022, 1148, 1, 0, 0);
add (08051,"Gunnison County", "CO", "Colorado", "8", -7:1, 12456, 4647, 1, 0, 0);
add (56033,"Sheridan County", "WY", "Wyoming", "8", -7:1, 25165, 4647, 1, 0, 0);
add (51530,"Buena Vista city", "VA", "Virginia", "2", -5:1, 6288, 1387, 1, 0, 0);
add (26119,"Montmorency County", "MI", "Michigan", "4", -5:1, 10011, 1387, 1, 0, 0);
add (47121,"Meigs County", "TN", "Tennesee", "3", -6:1, 9955, 1148, 1, 0, 0);
add (17125,"Mason County", "IL", "Illinois", "6", -6:1, 16837, 1148, 1, 0, 0);
add (28077,"Lawrence County", "MS", "Missippi", "5", -6:1, 13053, 1148, 1, 0, 0);
add (20107,"Linn County", "KS", "Kansas", "6", -6:1, 9158, 1148, 1, 0, 0);
add (34029,"Ocean County", "NJ", "New Jersey", "0", -5:1, 489819, 1387, 1, 0, 0);
add (31083,"Harlan County", "NE", "Nebraska", "6", -6:1, 3748, 1148, 1, 0, 0);
add (51083,"Halifax County", "VA", "Virginia", "2", -5:1, 36863, 1387, 1, 0, 0);
add (20071,"Greeley County", "KS", "Kansas", "6", -6:1, 1704, 1148, 1, 0, 0);
add (46127,"Union County", "SD", "South Dakota", "5", -7:1, 12213, 4647, 1, 0, 0);
add (19123,"Mahaska County", "IA", "Iowa", "5", -6:1, 21901, 1148, 1, 0, 0);
add (36119,"Westchester County", "NY", "New York", "1", -5:1, 897920, 1387, 1, 0, 0);
add (51610,"Falls Church city", "VA", "Virginia", "2", -5:1, 10042, 1387, 1, 0, 0);
add (28069,"Kemper County", "MS", "Missippi", "5", -6:1, 10575, 1148, 1, 0, 0);
add (21137,"Lincoln County", "KY", "Kentucky", "4", -5:1, 22367, 1387, 1, 0, 0);
add (51103,"Lancaster County", "VA", "Virginia", "2", -5:1, 11373, 1387, 1, 0, 0);
add (13269,"Taylor County", "GA", "Georgia", "3", -5:1, 8306, 1387, 1, 0, 0);
add (36039,"Greene County", "NY", "New York", "1", -5:1, 47807, 1387, 1, 0, 0);
add (26123,"Newaygo County", "MI", "Michigan", "4", -5:1, 45784, 1387, 1, 0, 0);
add (34013,"Essex County", "NJ", "New Jersey", "0", -5:1, 750273, 1387, 1, 0, 0);
add (17199,"Williamson County", "IL", "Illinois", "6", -6:1, 60819, 1148, 1, 0, 0);
add (17155,"Putnam County", "IL", "Illinois", "6", -6:1, 5826, 1148, 1, 0, 0);
add (31177,"Washington County", "NE", "Nebraska", "6", -7:1, 18661, 4647, 1, 0, 0);
add (37193,"Wilkes County", "NC", "North Carolina", "2", -5:1, 62837, 1387, 1, 0, 0);
add (37009,"Ashe County", "NC", "North Carolina", "2", -5:1, 24025, 1387, 1, 0, 0);
add (12121,"Suwannee County", "FL", "Florida", "3", -5:1, 32665, 1387, 1, 0, 0);
add (29221,"Washington County", "MO", "Mosourri", "6", -6:1, 22966, 1148, 1, 0, 0);
add (21037,"Campbell County", "KY", "Kentucky", "4", -6:1, 87381, 1148, 1, 0, 0);
add (53047,"Okanogan County", "WA", "Washiington", "9", -8:1, 38237, 3240, 0, 1, 0);
add (30073,"Pondera County", "MT", "Montana", "6", -7:1, 6402, 4647, 1, 0, 0);
add (01121,"Talladega County", "AL", "Alabama", "3", -6:1, 76633, 1148, 1, 0, 0);
add (13241,"Rabun County", "GA", "Georgia", "3", -5:1, 13406, 1387, 1, 0, 0);
add (05045,"Faulkner County", "AR", "Arkansas", "7", -6:1, 78382, 1148, 1, 0, 0);
add (13109,"Evans County", "GA", "Georgia", "3", -5:1, 9949, 1387, 1, 0, 0);
add (28111,"Perry County", "MS", "Missippi", "5", -6:1, 11798, 1148, 1, 0, 0);
add (54085,"Ritchie County", "WV", "West Virginia", "2", -5:1, 10356, 1387, 1, 0, 0);
add (12021,"Collier County", "FL", "Florida", "3", -5:1, 199436, 1387, 1, 0, 0);
add (19069,"Franklin County", "IA", "Iowa", "5", -6:1, 10863, 1148, 1, 0, 0);
add (48159,"Franklin County", "TX", "Texas", "7", -6:1, 9676, 1148, 1, 0, 0);
add (48339,"Montgomery County", "TX", "Texas", "7", -6:1, 271788, 1148, 1, 0, 0);
add (27119,"Polk County", "MN", "Minnesota", "5", -6:1, 30954, 1148, 1, 0, 0);
add (26037,"Clinton County", "MI", "Michigan", "4", -5:1, 63379, 1387, 1, 0, 0);
add (20121,"Miami County", "KS", "Kansas", "6", -6:1, 26597, 1148, 1, 0, 0);
add (19185,"Wayne County", "IA", "Iowa", "5", -6:1, 6659, 1148, 1, 0, 0);
add (29051,"Cole County", "MO", "Mosourri", "6", -6:1, 69307, 1148, 1, 0, 0);
add (36107,"Tioga County", "NY", "New York", "1", -5:1, 52477, 1387, 1, 0, 0);
add (39145,"Scioto County", "OH", "Ohio", "4", -5:1, 80355, 1387, 1, 0, 0);
add (12115,"Sarasota County", "FL", "Florida", "3", -5:1, 303400, 1387, 1, 0, 0);
add (24017,"Charles County", "MD", "Maryland", "2", -5:1, 117963, 1387, 1, 0, 0);
add (53063,"Spokane County", "WA", "Washiington", "9", -8:1, 408669, 3240, 0, 1, 0);
add (42009,"Bedford County", "PA", "Pennsylvania", "1", -5:1, 49373, 1387, 1, 0, 0);
add (17117,"Macoupin County", "IL", "Illinois", "6", -6:1, 48872, 1148, 1, 0, 0);
add (42037,"Columbia County", "PA", "Pennsylvania", "1", -5:1, 64120, 1387, 1, 0, 0);
add (46049,"Faulk County", "SD", "South Dakota", "5", -6:1, 2521, 1148, 1, 0, 0);
add (54097,"Upshur County", "WV", "West Virginia", "2", -5:1, 23526, 1387, 1, 0, 0);
add (18087,"Lagrange County", "IN", "Indiana", "4", -5:1, 33484, 1387, 1, 0, 0);
add (04027,"Yuma County", "AZ", "Arizona", "8", -7:1, 132259, 4647, 1, 0, 0);
add (49029,"Morgan County", "UT", "Utah", "8", -7:1, 7022, 4647, 1, 0, 0);
add (19013,"Black Hawk County", "IA", "Iowa", "5", -6:1, 121121, 1148, 1, 0, 0);
add (01037,"Coosa County", "AL", "Alabama", "3", -6:1, 11658, 1148, 1, 0, 0);
add (09003,"Hartford County", "CT", "Connecticut", "0", -5:1, 828200, 1387, 1, 0, 0);
add (29175,"Randolph County", "MO", "Mosourri", "6", -6:1, 24024, 1148, 1, 0, 0);
add (28083,"Leflore County", "MS", "Missippi", "5", -6:1, 36951, 1148, 1, 0, 0);
add (39049,"Franklin County", "OH", "Ohio", "4", -5:1, 1021194, 1387, 1, 0, 0);
add (24045,"Wicomico County", "MD", "Maryland", "2", -5:1, 79367, 1387, 1, 0, 0);
add (39037,"Darke County", "OH", "Ohio", "4", -5:1, 54180, 1387, 1, 0, 0);
add (21141,"Logan County", "KY", "Kentucky", "4", -5:1, 26145, 1387, 1, 0, 0);
add (36043,"Herkimer County", "NY", "New York", "1", -5:1, 64049, 1387, 1, 0, 0);
add (13167,"Johnson County", "GA", "Georgia", "3", -5:1, 8316, 1387, 1, 0, 0);
add (08037,"Eagle County", "CO", "Colorado", "8", -7:1, 33538, 4647, 1, 0, 0);
add (18013,"Brown County", "IN", "Indiana", "4", -5:1, 15982, 1387, 1, 0, 0);
add (13151,"Henry County", "GA", "Georgia", "3", -5:1, 104667, 1387, 1, 0, 0);
add (39147,"Seneca County", "OH", "Ohio", "4", -5:1, 60099, 1387, 1, 0, 0);
add (21057,"Cumberland County", "KY", "Kentucky", "4", -6:1, 6828, 1148, 1, 0, 0);
add (53077,"Yakima County", "WA", "Washiington", "9", -8:1, 218062, 3240, 0, 1, 0);
add (13003,"Atkinson County", "GA", "Georgia", "3", -5:1, 7138, 1387, 1, 0, 0);
add (13089,"DeKalb County", "GA", "Georgia", "3", -5:1, 593850, 1387, 1, 0, 0);
add (37079,"Greene County", "NC", "North Carolina", "2", -5:1, 18308, 1387, 1, 0, 0);
add (20017,"Chase County", "KS", "Kansas", "6", -6:1, 2950, 1148, 1, 0, 0);
add (01001,"Autauga County", "AL", "Alabama", "3", -6:1, 42095, 1148, 1, 0, 0);
add (26065,"Ingham County", "MI", "Michigan", "4", -5:1, 285214, 1387, 1, 0, 0);
add (48063,"Camp County", "TX", "Texas", "7", -6:1, 10962, 1148, 1, 0, 0);
add (19093,"Ida County", "IA", "Iowa", "5", -6:1, 7912, 1148, 1, 0, 0);
add (39173,"Wood County", "OH", "Ohio", "4", -5:1, 119498, 1387, 1, 0, 0);
add (13227,"Pickens County", "GA", "Georgia", "3", -5:1, 19679, 1387, 1, 0, 0);
add (26033,"Chippewa County", "MI", "Michigan", "4", -5:1, 37968, 1387, 1, 0, 0);
add (29023,"Butler County", "MO", "Mosourri", "6", -6:1, 40561, 1148, 1, 0, 0);
add (47067,"Hancock County", "TN", "Tennesee", "3", -5:1, 6778, 1387, 1, 0, 0);
add (27091,"Martin County", "MN", "Minnesota", "5", -6:1, 21984, 1148, 1, 0, 0);
add (19139,"Muscatine County", "IA", "Iowa", "5", -6:1, 41126, 1148, 1, 0, 0);
add (54011,"Cabell County", "WV", "West Virginia", "2", -5:1, 94273, 1387, 1, 0, 0);
add (13247,"Rockdale County", "GA", "Georgia", "3", -5:1, 68305, 1387, 1, 0, 0);
add (12061,"Indian River County", "FL", "Florida", "3", -5:1, 99155, 1387, 1, 0, 0);
add (31023,"Butler County", "NE", "Nebraska", "6", -6:1, 8680, 1148, 1, 0, 0);
add (18051,"Gibson County", "IN", "Indiana", "4", -5:1, 32149, 1387, 1, 0, 0);
add (38073,"Ransom County", "ND", "North Dakota", "5", -6:1, 5776, 1148, 1, 0, 0);
add (29089,"Howard County", "MO", "Mosourri", "6", -6:1, 9741, 1148, 1, 0, 0);
add (05023,"Cleburne County", "AR", "Arkansas", "7", -6:1, 22923, 1148, 1, 0, 0);
add (05109,"Pike County", "AR", "Arkansas", "7", -6:1, 10592, 1148, 1, 0, 0);
add (51650,"Hampton city", "VA", "Virginia", "2", -5:1, 136968, 1387, 1, 0, 0);
add (01065,"Hale County", "AL", "Alabama", "3", -6:1, 16744, 1148, 1, 0, 0);
add (39011,"Auglaize County", "OH", "Ohio", "4", -5:1, 47103, 1387, 1, 0, 0);
add (51685,"Manassas Park city", "VA", "Virginia", "2", -5:1, 8711, 1387, 1, 0, 0);
add (27059,"Isanti County", "MN", "Minnesota", "5", -6:1, 30121, 1148, 1, 0, 0);
add (31037,"Colfax County", "NE", "Nebraska", "6", -6:1, 10716, 1148, 1, 0, 0);
add (51141,"Patrick County", "VA", "Virginia", "2", -5:1, 18441, 1387, 1, 0, 0);
add (21153,"Magoffin County", "KY", "Kentucky", "4", -5:1, 13838, 1387, 1, 0, 0);
add (13139,"Hall County", "GA", "Georgia", "3", -5:1, 119210, 1387, 1, 0, 0);
add (01091,"Marengo County", "AL", "Alabama", "3", -6:1, 23378, 1148, 1, 0, 0);
add (27093,"Meeker County", "MN", "Minnesota", "5", -6:1, 21735, 1148, 1, 0, 0);
add (55087,"Outagamie County", "WI", "Wisconnsin", "5", -6:1, 156269, 1148, 1, 0, 0);
add (08041,"El Paso County", "CO", "Colorado", "8", -7:1, 490378, 4647, 1, 0, 0);
add (19129,"Mills County", "IA", "Iowa", "5", -6:1, 14477, 1148, 1, 0, 0);
add (01063,"Greene County", "AL", "Alabama", "3", -6:1, 9880, 1148, 1, 0, 0);
add (17047,"Edwards County", "IL", "Illinois", "6", -6:1, 6950, 1148, 1, 0, 0);
add (18143,"Scott County", "IN", "Indiana", "4", -5:1, 22939, 1387, 1, 0, 0);
add (08115,"Sedgwick County", "CO", "Colorado", "8", -7:1, 2547, 4647, 1, 0, 0);
add (37173,"Swain County", "NC", "North Carolina", "2", -5:1, 12300, 1387, 1, 0, 0);
add (31157,"Scotts Bluff County", "NE", "Nebraska", "6", -7:1, 36109, 4647, 1, 0, 0);
add (42105,"Potter County", "PA", "Pennsylvania", "1", -5:1, 17184, 1387, 1, 0, 0);
add (17001,"Adams County", "IL", "Illinois", "6", -6:1, 67105, 1148, 1, 0, 0);
add (24033,"Prince George County", "MD", "Maryland", "2", -5:1, 777811, 1387, 1, 0, 0);
add (12095,"Orange County", "FL", "Florida", "3", -5:1, 805837, 1387, 1, 0, 0);
add (51169,"Scott County", "VA", "Virginia", "2", -5:1, 22605, 1387, 1, 0, 0);
add (05063,"Independence County", "AR", "Arkansas", "7", -6:1, 33054, 1148, 1, 0, 0);
add (17141,"Ogle County", "IL", "Illinois", "6", -6:1, 50511, 1148, 1, 0, 0);
add (27009,"Benton County", "MN", "Minnesota", "5", -6:1, 34128, 1148, 1, 0, 0);
add (28115,"Pontotoc County", "MS", "Missippi", "5", -6:1, 25397, 1148, 1, 0, 0);
add (21133,"Letcher County", "KY", "Kentucky", "4", -5:1, 26185, 1387, 1, 0, 0);
add (51047,"Culpeper County", "VA", "Virginia", "2", -5:1, 33083, 1387, 1, 0, 0);
add (47019,"Carter County", "TN", "Tennesee", "3", -5:1, 53323, 1387, 1, 0, 0);
add (08049,"Grand County", "CO", "Colorado", "8", -7:1, 10050, 4647, 1, 0, 0);
add (17031,"Cook County", "IL", "Illinois", "6", -6:1, 5189689, 1148, 1, 0, 0);
add (13079,"Crawford County", "GA", "Georgia", "3", -5:1, 10667, 1387, 1, 0, 0);
add (13217,"Newton County", "GA", "Georgia", "3", -5:1, 57847, 1387, 1, 0, 0);
add (17201,"Winnebago County", "IL", "Illinois", "6", -6:1, 267642, 1148, 1, 0, 0);
add (27131,"Rice County", "MN", "Minnesota", "5", -6:1, 54106, 1148, 1, 0, 0);
add (39137,"Putnam County", "OH", "Ohio", "4", -5:1, 35255, 1387, 1, 0, 0);
add (19191,"Winneshiek County", "IA", "Iowa", "5", -6:1, 20934, 1148, 1, 0, 0);
add (20207,"Woodson County", "KS", "Kansas", "6", -6:1, 3983, 1148, 1, 0, 0);
add (02020,"Anchorage Borough", "AK", "Alaska", "9", -9:1, 254982, 1174, 0, 0, 1);
add (28065,"Jefferson Davis County", "MS", "Missippi", "5", -6:1, 13860, 1148, 1, 0, 0);
add (48329,"Midland County", "TX", "Texas", "7", -6:1, 119647, 1148, 1, 0, 0);
add (19121,"Madison County", "IA", "Iowa", "5", -6:1, 13872, 1148, 1, 0, 0);
add (08111,"San Juan County", "CO", "Colorado", "8", -7:1, 530, 4647, 1, 0, 0);
add (37075,"Graham County", "NC", "North Carolina", "2", -5:1, 7647, 1387, 1, 0, 0);
add (37043,"Clay County", "NC", "North Carolina", "2", -5:1, 8575, 1387, 1, 0, 0);
add (06011,"Colusa County", "CA", "California", "9", -8:1, 18572, 3240, 0, 1, 0);
add (37071,"Gaston County", "NC", "North Carolina", "2", -5:1, 184247, 1387, 1, 0, 0);
add (56041,"Uinta County", "WY", "Wyoming", "8", -7:1, 20465, 4647, 1, 0, 0);
add (45043,"Georgetown County", "SC", "South Carolina", "2", -5:1, 53727, 1387, 1, 0, 0);
add (51197,"Wythe County", "VA", "Virginia", "2", -5:1, 26268, 1387, 1, 0, 0);
add (17027,"Clinton County", "IL", "Illinois", "6", -6:1, 35591, 1148, 1, 0, 0);
add (45071,"Newberry County", "SC", "South Carolina", "2", -5:1, 34462, 1387, 1, 0, 0);
add (30013,"Cascade County", "MT", "Montana", "6", -7:1, 78983, 4647, 1, 0, 0);
add (51163,"Rockbridge County", "VA", "Virginia", "2", -5:1, 19557, 1387, 1, 0, 0);
add (27135,"Roseau County", "MN", "Minnesota", "5", -6:1, 16120, 1148, 1, 0, 0);
add (48455,"Trinity County", "TX", "Texas", "7", -6:1, 12613, 1148, 1, 0, 0);
add (27169,"Winona County", "MN", "Minnesota", "5", -6:1, 48080, 1148, 1, 0, 0);
add (47065,"Hamilton County", "TN", "Tennesee", "3", -5:1, 294745, 1387, 1, 0, 0);
add (51027,"Buchanan County", "VA", "Virginia", "2", -5:1, 28929, 1387, 1, 0, 0);
add (48229,"Hudspeth County", "TX", "Texas", "7", -6:1, 3250, 1148, 1, 0, 0);
add (26103,"Marquette County", "MI", "Michigan", "4", -5:1, 61565, 1387, 1, 0, 0);
add (13141,"Hancock County", "GA", "Georgia", "3", -5:1, 9134, 1387, 1, 0, 0);
add (48187,"Guadalupe County", "TX", "Texas", "7", -6:1, 80472, 1148, 1, 0, 0);
add (46083,"Lincoln County", "SD", "South Dakota", "5", -7:1, 20411, 4647, 1, 0, 0);
add (06097,"Sonoma County", "CA", "California", "9", -8:1, 433304, 3240, 0, 1, 0);
add (12009,"Brevard County", "FL", "Florida", "3", -5:1, 466093, 1387, 1, 0, 0);
add (42023,"Cameron County", "PA", "Pennsylvania", "1", -5:1, 5620, 1387, 1, 0, 0);
add (47167,"Tipton County", "TN", "Tennesee", "3", -6:1, 47343, 1148, 1, 0, 0);
add (48327,"Menard County", "TX", "Texas", "7", -6:1, 2336, 1148, 1, 0, 0);
add (15009,"Maui County", "HI", "Hawaii", "9", -10:1, 120711, 6750, 0, 0, 1);
add (55045,"Green County", "WI", "Wisconnsin", "5", -6:1, 33404, 1148, 1, 0, 0);
add (30053,"Lincoln County", "MT", "Montana", "6", -7:1, 18696, 4647, 1, 0, 0);
add (20091,"Johnson County", "KS", "Kansas", "6", -6:1, 429563, 1148, 1, 0, 0);
add (39129,"Pickaway County", "OH", "Ohio", "4", -5:1, 53731, 1387, 1, 0, 0);
add (19159,"Ringgold County", "IA", "Iowa", "5", -6:1, 5354, 1148, 1, 0, 0);
add (22067,"Morehouse Parish", "LA", "Louisiana", "7", -6:1, 31477, 1148, 1, 0, 0);
add (48087,"Collingsworth County", "TX", "Texas", "7", -6:1, 3287, 1148, 1, 0, 0);
add (39055,"Geauga County", "OH", "Ohio", "4", -5:1, 88788, 1387, 1, 0, 0);
add (16075,"Payette County", "ID", "Idaho", "8", -7:1, 20519, 4647, 1, 0, 0);
add (48499,"Wood County", "TX", "Texas", "7", -6:1, 34321, 1148, 1, 0, 0);
add (31011,"Boone County", "NE", "Nebraska", "6", -6:1, 6377, 1148, 1, 0, 0);
add (39035,"Cuyahoga County", "OH", "Ohio", "4", -5:1, 1380696, 1387, 1, 0, 0);
add (22055,"Lafayette Parish", "LA", "Louisiana", "7", -6:1, 186631, 1148, 1, 0, 0);
add (39141,"Ross County", "OH", "Ohio", "4", -5:1, 75473, 1387, 1, 0, 0);
add (02261,"Valdez-Cordova Census Area", "AK", "Alaska", "9", -9:1, 10279, 1174, 0, 0, 1);
add (51730,"Petersburg city", "VA", "Virginia", "2", -5:1, 34724, 1387, 1, 0, 0);
add (42063,"Indiana County", "PA", "Pennsylvania", "1", -5:1, 88567, 1387, 1, 0, 0);
add (24029,"Kent County", "MD", "Maryland", "2", -5:1, 18925, 1387, 1, 0, 0);
add (40107,"Okfuskee County", "OK", "Oklahoma", "7", -6:1, 11402, 1148, 1, 0, 0);
add (28067,"Jones County", "MS", "Missippi", "5", -6:1, 63461, 1148, 1, 0, 0);
add (41045,"Malheur County", "OR", "Oregon", "9", -8:1, 28542, 3240, 0, 1, 0);
add (36081,"Queens County", "NY", "New York", "1", -5:1, 1998853, 1387, 1, 0, 0);
add (16079,"Shoshone County", "ID", "Idaho", "8", -7:1, 13870, 4647, 1, 0, 0);
add (08079,"Mineral County", "CO", "Colorado", "8", -7:1, 694, 4647, 1, 0, 0);
add (21135,"Lewis County", "KY", "Kentucky", "4", -5:1, 13584, 1387, 1, 0, 0);
add (55007,"Bayfield County", "WI", "Wisconnsin", "5", -6:1, 15151, 1148, 1, 0, 0);
add (25021,"Norfolk County", "MA", "Massachusetts", "0", -5:1, 642705, 1387, 1, 0, 0);
add (42081,"Lycoming County", "PA", "Pennsylvania", "1", -5:1, 117308, 1387, 1, 0, 0);
add (12035,"Flagler County", "FL", "Florida", "3", -5:1, 47455, 1387, 1, 0, 0);
add (34017,"Hudson County", "NJ", "New Jersey", "0", -5:1, 557159, 1387, 1, 0, 0);
add (53045,"Mason County", "WA", "Washiington", "9", -8:1, 49867, 3240, 0, 1, 0);
add (31163,"Sherman County", "NE", "Nebraska", "6", -7:1, 3432, 4647, 1, 0, 0);
add (46089,"McPherson County", "SD", "South Dakota", "5", -7:1, 2738, 4647, 1, 0, 0);
add (31007,"Banner County", "NE", "Nebraska", "6", -6:1, 878, 1148, 1, 0, 0);
add (38029,"Emmons County", "ND", "North Dakota", "5", -6:1, 4311, 1148, 1, 0, 0);
add (47181,"Wayne County", "TN", "Tennesee", "3", -6:1, 16495, 1148, 1, 0, 0);
add (51051,"Dickenson County", "VA", "Virginia", "2", -5:1, 16894, 1387, 1, 0, 0);
add (17143,"Peoria County", "IL", "Illinois", "6", -6:1, 181609, 1148, 1, 0, 0);
add (50007,"Chittenden County", "VT", "Vermont", "0", -5:1, 142642, 1387, 1, 0, 0);
add (21047,"Christian County", "KY", "Kentucky", "4", -6:1, 72493, 1148, 1, 0, 0);
add (31143,"Polk County", "NE", "Nebraska", "6", -7:1, 5631, 4647, 1, 0, 0);
add (35059,"Union County", "NM", "New Mexico", "8", -7:1, 3985, 4647, 1, 0, 0);
add (53025,"Grant County", "WA", "Washiington", "9", -8:1, 70545, 3240, 0, 1, 0);
add (20027,"Clay County", "KS", "Kansas", "6", -6:1, 9148, 1148, 1, 0, 0);
add (46099,"Minnehaha County", "SD", "South Dakota", "5", -7:1, 143011, 4647, 1, 0, 0);
add (31171,"Thomas County", "NE", "Nebraska", "6", -7:1, 797, 4647, 1, 0, 0);
add (20167,"Russell County", "KS", "Kansas", "6", -6:1, 7558, 1148, 1, 0, 0);
add (08063,"Kit Carson County", "CO", "Colorado", "8", -7:1, 7313, 4647, 1, 0, 0);
add (37033,"Caswell County", "NC", "North Carolina", "2", -5:1, 22215, 1387, 1, 0, 0);
add (55101,"Racine County", "WI", "Wisconnsin", "5", -6:1, 186119, 1148, 1, 0, 0);
add (48463,"Uvalde County", "TX", "Texas", "7", -6:1, 25565, 1148, 1, 0, 0);
add (22057,"Lafourche Parish", "LA", "Louisiana", "7", -6:1, 89324, 1148, 1, 0, 0);
add (20201,"Washington County", "KS", "Kansas", "6", -6:1, 6490, 1148, 1, 0, 0);
add (13253,"Seminole County", "GA", "Georgia", "3", -5:1, 9788, 1387, 1, 0, 0);
add (22001,"Acadia Parish", "LA", "Louisiana", "7", -6:1, 57721, 1148, 1, 0, 0);
add (39009,"Athens County", "OH", "Ohio", "4", -5:1, 61490, 1387, 1, 0, 0);
add (21093,"Hardin County", "KY", "Kentucky", "4", -6:1, 91462, 1148, 1, 0, 0);
add (42083,"McKean County", "PA", "Pennsylvania", "1", -5:1, 46500, 1387, 1, 0, 0);
add (13169,"Jones County", "GA", "Georgia", "3", -5:1, 23020, 1387, 1, 0, 0);
add (39099,"Mahoning County", "OH", "Ohio", "4", -5:1, 255165, 1387, 1, 0, 0);
add (48093,"Comanche County", "TX", "Texas", "7", -6:1, 13568, 1148, 1, 0, 0);
add (47057,"Grainger County", "TN", "Tennesee", "3", -5:1, 19829, 1387, 1, 0, 0);
add (15003,"Honolulu County", "HI", "Hawaii", "9", -10:1, 872478, 6750, 0, 0, 1);
add (13001,"Appling County", "GA", "Georgia", "3", -5:1, 16493, 1387, 1, 0, 0);
add (30091,"Sheridan County", "MT", "Montana", "6", -7:1, 4269, 4647, 1, 0, 0);
add (05143,"Washington County", "AR", "Arkansas", "7", -6:1, 138454, 1148, 1, 0, 0);
add (08045,"Garfield County", "CO", "Colorado", "8", -7:1, 39301, 4647, 1, 0, 0);
add (13157,"Jackson County", "GA", "Georgia", "3", -5:1, 37641, 1387, 1, 0, 0);
add (29195,"Saline County", "MO", "Mosourri", "6", -6:1, 22703, 1148, 1, 0, 0);
add (16067,"Minidoka County", "ID", "Idaho", "8", -7:1, 20207, 4647, 1, 0, 0);
add (48199,"Hardin County", "TX", "Texas", "7", -6:1, 48758, 1148, 1, 0, 0);
add (16021,"Boundary County", "ID", "Idaho", "8", -7:1, 9800, 4647, 1, 0, 0);
add (21123,"Larue County", "KY", "Kentucky", "4", -5:1, 13058, 1387, 1, 0, 0);
add (23031,"York County", "ME", "Maine", "0", -5:1, 175165, 1387, 1, 0, 0);
add (27133,"Rock County", "MN", "Minnesota", "5", -6:1, 9743, 1148, 1, 0, 0);
add (17119,"Madison County", "IL", "Illinois", "6", -6:1, 259351, 1148, 1, 0, 0);
add (28123,"Scott County", "MS", "Missippi", "5", -6:1, 25001, 1148, 1, 0, 0);
add (37143,"Perquimans County", "NC", "North Carolina", "2", -5:1, 11282, 1387, 1, 0, 0);
add (13145,"Harris County", "GA", "Georgia", "3", -5:1, 22315, 1387, 1, 0, 0);
add (49023,"Juab County", "UT", "Utah", "8", -7:1, 7572, 4647, 1, 0, 0);
add (38087,"Slope County", "ND", "North Dakota", "5", -6:1, 865, 1148, 1, 0, 0);
add (13175,"Laurens County", "GA", "Georgia", "3", -5:1, 43772, 1387, 1, 0, 0);
add (49025,"Kane County", "UT", "Utah", "8", -7:1, 6200, 4647, 1, 0, 0);
add (36055,"Monroe County", "NY", "New York", "1", -5:1, 716072, 1387, 1, 0, 0);
add (26045,"Eaton County", "MI", "Michigan", "4", -5:1, 101090, 1387, 1, 0, 0);
add (05097,"Montgomery County", "AR", "Arkansas", "7", -6:1, 8655, 1148, 1, 0, 0);
add (12015,"Charlotte County", "FL", "Florida", "3", -5:1, 134899, 1387, 1, 0, 0);
add (13049,"Charlton County", "GA", "Georgia", "3", -5:1, 9442, 1387, 1, 0, 0);
add (20159,"Rice County", "KS", "Kansas", "6", -6:1, 10360, 1148, 1, 0, 0);
add (48277,"Lamar County", "TX", "Texas", "7", -6:1, 46045, 1148, 1, 0, 0);
add (29029,"Camden County", "MO", "Mosourri", "6", -6:1, 33952, 1148, 1, 0, 0);
add (12067,"Lafayette County", "FL", "Florida", "3", -5:1, 6325, 1387, 1, 0, 0);
add (29223,"Wayne County", "MO", "Mosourri", "6", -6:1, 13059, 1148, 1, 0, 0);
add (48433,"Stonewall County", "TX", "Texas", "7", -6:1, 1783, 1148, 1, 0, 0);
add (20099,"Labette County", "KS", "Kansas", "6", -6:1, 23030, 1148, 1, 0, 0);
add (51199,"York County", "VA", "Virginia", "2", -5:1, 58789, 1387, 1, 0, 0);
add (39149,"Shelby County", "OH", "Ohio", "4", -5:1, 47457, 1387, 1, 0, 0);
add (22113,"Vermilion Parish", "LA", "Louisiana", "7", -6:1, 52090, 1148, 1, 0, 0);
add (39093,"Lorain County", "OH", "Ohio", "4", -5:1, 282149, 1387, 1, 0, 0);
add (53003,"Asotin County", "WA", "Washiington", "9", -8:1, 21264, 3240, 0, 1, 0);
add (18149,"Starke County", "IN", "Indiana", "4", -5:1, 23968, 1387, 1, 0, 0);
add (26041,"Delta County", "MI", "Michigan", "4", -5:1, 38947, 1387, 1, 0, 0);
add (30005,"Blaine County", "MT", "Montana", "6", -7:1, 7148, 4647, 1, 0, 0);
add (05027,"Columbia County", "AR", "Arkansas", "7", -6:1, 25060, 1148, 1, 0, 0);
add (08011,"Bent County", "CO", "Colorado", "8", -7:1, 5497, 4647, 1, 0, 0);
add (51119,"Middlesex County", "VA", "Virginia", "2", -5:1, 9630, 1387, 1, 0, 0);
add (48471,"Walker County", "TX", "Texas", "7", -6:1, 54972, 1148, 1, 0, 0);
add (12063,"Jackson County", "FL", "Florida", "3", -5:1, 45660, 1387, 1, 0, 0);
add (31101,"Keith County", "NE", "Nebraska", "6", -7:1, 8665, 4647, 1, 0, 0);
add (21035,"Calloway County", "KY", "Kentucky", "4", -6:1, 33478, 1148, 1, 0, 0);
add (28125,"Sharkey County", "MS", "Missippi", "5", -6:1, 6650, 1148, 1, 0, 0);
add (05127,"Scott County", "AR", "Arkansas", "7", -6:1, 10686, 1148, 1, 0, 0);
add (08093,"Park County", "CO", "Colorado", "8", -7:1, 13399, 4647, 1, 0, 0);
add (27115,"Pine County", "MN", "Minnesota", "5", -6:1, 23916, 1148, 1, 0, 0);
add (53069,"Wahkiakum County", "WA", "Washiington", "9", -8:1, 3857, 3240, 0, 1, 0);
add (02180,"Nome Census Area", "AK", "Alaska", "9", -9:1, 9016, 1174, 0, 0, 1);
add (21205,"Rowan County", "KY", "Kentucky", "4", -5:1, 22196, 1387, 1, 0, 0);
add (37057,"Davidson County", "NC", "North Carolina", "2", -5:1, 141178, 1387, 1, 0, 0);
add (37013,"Beaufort County", "NC", "North Carolina", "2", -5:1, 44531, 1387, 1, 0, 0);
add (50005,"Caledonia County", "VT", "Vermont", "0", -5:1, 28529, 1387, 1, 0, 0);
add (26043,"Dickinson County", "MI", "Michigan", "4", -5:1, 27074, 1387, 1, 0, 0);
add (06015,"Del Norte County", "CA", "California", "9", -8:1, 27000, 3240, 0, 1, 0);
add (55107,"Rusk County", "WI", "Wisconnsin", "5", -6:1, 15252, 1148, 1, 0, 0);
add (22061,"Lincoln Parish", "LA", "Louisiana", "7", -6:1, 41560, 1148, 1, 0, 0);
add (48117,"Deaf Smith County", "TX", "Texas", "7", -6:1, 19061, 1148, 1, 0, 0);
add (27079,"Le Sueur County", "MN", "Minnesota", "5", -6:1, 25320, 1148, 1, 0, 0);
add (39033,"Crawford County", "OH", "Ohio", "4", -5:1, 47217, 1387, 1, 0, 0);
add (37003,"Alexander County", "NC", "North Carolina", "2", -5:1, 31280, 1387, 1, 0, 0);
add (21011,"Bath County", "KY", "Kentucky", "4", -6:1, 10570, 1148, 1, 0, 0);
add (08005,"Arapahoe County", "CO", "Colorado", "8", -7:1, 473168, 4647, 1, 0, 0);
add (51775,"Salem city", "VA", "Virginia", "2", -5:1, 24679, 1387, 1, 0, 0);
add (36061,"New York County", "NY", "New York", "1", -5:1, 1550649, 1387, 1, 0, 0);
add (26001,"Alcona County", "MI", "Michigan", "4", -5:1, 11108, 1387, 1, 0, 0);
add (12041,"Gilchrist County", "FL", "Florida", "3", -5:1, 13791, 1387, 1, 0, 0);
add (33015,"Rockingham County", "NH", "New Hampshire", "0", -5:1, 271152, 1387, 1, 0, 0);
add (15007,"Kauai County", "HI", "Hawaii", "9", -10:1, 56603, 6750, 0, 0, 1);
add (47163,"Sullivan County", "TN", "Tennesee", "3", -6:1, 150617, 1148, 1, 0, 0);
add (51191,"Washington County", "VA", "Virginia", "2", -5:1, 49168, 1387, 1, 0, 0);
add (55117,"Sheboygan County", "WI", "Wisconnsin", "5", -6:1, 110170, 1148, 1, 0, 0);
add (12051,"Hendry County", "FL", "Florida", "3", -5:1, 29357, 1387, 1, 0, 0);
add (51510,"Alexandria city", "VA", "Virginia", "2", -5:1, 118300, 1387, 1, 0, 0);
add (21045,"Casey County", "KY", "Kentucky", "4", -6:1, 14773, 1148, 1, 0, 0);
add (31125,"Nance County", "NE", "Nebraska", "6", -7:1, 4099, 4647, 1, 0, 0);
add (27155,"Traverse County", "MN", "Minnesota", "5", -6:1, 4248, 1148, 1, 0, 0);
add (34005,"Burlington County", "NJ", "New Jersey", "0", -5:1, 420323, 1387, 1, 0, 0);
add (12005,"Bay County", "FL", "Florida", "3", -5:1, 146999, 1387, 1, 0, 0);
add (47017,"Carroll County", "TN", "Tennesee", "3", -5:1, 29115, 1387, 1, 0, 0);
add (19035,"Cherokee County", "IA", "Iowa", "5", -6:1, 13191, 1148, 1, 0, 0);
add (13243,"Randolph County", "GA", "Georgia", "3", -5:1, 7881, 1387, 1, 0, 0);
add (05135,"Sharp County", "AR", "Arkansas", "7", -6:1, 16993, 1148, 1, 0, 0);
add (27017,"Carlton County", "MN", "Minnesota", "5", -6:1, 30817, 1148, 1, 0, 0);
add (48435,"Sutton County", "TX", "Texas", "7", -6:1, 4463, 1148, 1, 0, 0);
add (33009,"Grafton County", "NH", "New Hampshire", "0", -5:1, 78277, 1387, 1, 0, 0);
add (39017,"Butler County", "OH", "Ohio", "4", -5:1, 330428, 1387, 1, 0, 0);
add (36021,"Columbia County", "NY", "New York", "1", -5:1, 63221, 1387, 1, 0, 0);
add (41061,"Union County", "OR", "Oregon", "9", -8:1, 24829, 3240, 0, 1, 0);
add (13059,"Clarke County", "GA", "Georgia", "3", -5:1, 90630, 1387, 1, 0, 0);
add (48243,"Jeff Davis County", "TX", "Texas", "7", -6:1, 2356, 1148, 1, 0, 0);
add (55055,"Jefferson County", "WI", "Wisconnsin", "5", -6:1, 73550, 1148, 1, 0, 0);
add (36003,"Allegany County", "NY", "New York", "1", -5:1, 50997, 1387, 1, 0, 0);
add (51159,"Richmond County", "VA", "Virginia", "2", -5:1, 8665, 1387, 1, 0, 0);
add (38043,"Kidder County", "ND", "North Dakota", "5", -6:1, 2877, 1148, 1, 0, 0);
add (48215,"Hidalgo County", "TX", "Texas", "7", -6:1, 522204, 1148, 1, 0, 0);
add (27073,"Lac qui Parle County", "MN", "Minnesota", "5", -6:1, 8022, 1148, 1, 0, 0);
add (55015,"Calumet County", "WI", "Wisconnsin", "5", -6:1, 38377, 1148, 1, 0, 0);
add (48119,"Delta County", "TX", "Texas", "7", -6:1, 4945, 1148, 1, 0, 0);
add (13095,"Dougherty County", "GA", "Georgia", "3", -5:1, 95309, 1387, 1, 0, 0);
add (19165,"Shelby County", "IA", "Iowa", "5", -6:1, 12978, 1148, 1, 0, 0);
add (53017,"Douglas County", "WA", "Washiington", "9", -8:1, 33631, 3240, 0, 1, 0);
add (19089,"Howard County", "IA", "Iowa", "5", -6:1, 9689, 1148, 1, 0, 0);
add (01073,"Jefferson County", "AL", "Alabama", "3", -6:1, 659524, 1148, 1, 0, 0);
add (48047,"Brooks County", "TX", "Texas", "7", -6:1, 8501, 1148, 1, 0, 0);
add (13179,"Liberty County", "GA", "Georgia", "3", -5:1, 59162, 1387, 1, 0, 0);
add (20055,"Finney County", "KS", "Kansas", "6", -6:1, 36514, 1148, 1, 0, 0);
add (27161,"Waseca County", "MN", "Minnesota", "5", -6:1, 18178, 1148, 1, 0, 0);
add (19187,"Webster County", "IA", "Iowa", "5", -6:1, 38705, 1148, 1, 0, 0);
add (28005,"Amite County", "MS", "Missippi", "5", -6:1, 13752, 1148, 1, 0, 0);
add (35015,"Eddy County", "NM", "New Mexico", "8", -7:1, 53543, 4647, 1, 0, 0);
add (48415,"Scurry County", "TX", "Texas", "7", -6:1, 18073, 1148, 1, 0, 0);
add (20035,"Cowley County", "KS", "Kansas", "6", -6:1, 36319, 1148, 1, 0, 0);
add (51093,"Isle of Wight County", "VA", "Virginia", "2", -5:1, 29252, 1387, 1, 0, 0);
add (29143,"New Madrid County", "MO", "Mosourri", "6", -6:1, 20370, 1148, 1, 0, 0);
add (48013,"Atascosa County", "TX", "Texas", "7", -6:1, 36471, 1148, 1, 0, 0);
add (01069,"Houston County", "AL", "Alabama", "3", -6:1, 85877, 1148, 1, 0, 0);
add (19151,"Pocahontas County", "IA", "Iowa", "5", -6:1, 8777, 1148, 1, 0, 0);
add (46027,"Clay County", "SD", "South Dakota", "5", -6:1, 15167, 1148, 1, 0, 0);
add (06107,"Tulare County", "CA", "California", "9", -8:1, 355240, 3240, 0, 1, 0);
add (01079,"Lawrence County", "AL", "Alabama", "3", -6:1, 33447, 1148, 1, 0, 0);
add (48377,"Presidio County", "TX", "Texas", "7", -6:1, 8636, 1148, 1, 0, 0);
add (40115,"Ottawa County", "OK", "Oklahoma", "7", -6:1, 30944, 1148, 1, 0, 0);
add (18091,"La Porte County", "IN", "Indiana", "4", -5:1, 109461, 1387, 1, 0, 0);
add (12057,"Hillsborough County", "FL", "Florida", "3", -5:1, 925277, 1387, 1, 0, 0);
add (38085,"Sioux County", "ND", "North Dakota", "5", -6:1, 4192, 1148, 1, 0, 0);
add (42133,"York County", "PA", "Pennsylvania", "1", -5:1, 373255, 1387, 1, 0, 0);
add (08059,"Jefferson County", "CO", "Colorado", "8", -7:1, 501591, 4647, 1, 0, 0);
add (13209,"Montgomery County", "GA", "Georgia", "3", -5:1, 7741, 1387, 1, 0, 0);
add (35041,"Roosevelt County", "NM", "New Mexico", "8", -7:1, 18185, 4647, 1, 0, 0);
add (55039,"Fond du Lac County", "WI", "Wisconnsin", "5", -6:1, 94690, 1148, 1, 0, 0);
add (13309,"Wheeler County", "GA", "Georgia", "3", -5:1, 4875, 1387, 1, 0, 0);
add (13299,"Ware County", "GA", "Georgia", "3", -5:1, 35364, 1387, 1, 0, 0);
add (27069,"Kittson County", "MN", "Minnesota", "5", -6:1, 5322, 1148, 1, 0, 0);
add (06055,"Napa County", "CA", "California", "9", -8:1, 119288, 3240, 0, 1, 0);
add (54035,"Jackson County", "WV", "West Virginia", "2", -5:1, 27972, 1387, 1, 0, 0);
add (29033,"Carroll County", "MO", "Mosourri", "6", -6:1, 10217, 1148, 1, 0, 0);
add (35017,"Grant County", "NM", "New Mexico", "8", -7:1, 31612, 4647, 1, 0, 0);
add (48021,"Bastrop County", "TX", "Texas", "7", -6:1, 50390, 1148, 1, 0, 0);
add (17003,"Alexander County", "IL", "Illinois", "6", -6:1, 9745, 1148, 1, 0, 0);
add (21071,"Floyd County", "KY", "Kentucky", "4", -6:1, 43340, 1148, 1, 0, 0);
add (20137,"Norton County", "KS", "Kansas", "6", -6:1, 5752, 1148, 1, 0, 0);
add (48447,"Throckmorton County", "TX", "Texas", "7", -6:1, 1727, 1148, 1, 0, 0);
add (16007,"Bear Lake County", "ID", "Idaho", "8", -7:1, 6539, 4647, 1, 0, 0);
add (39079,"Jackson County", "OH", "Ohio", "4", -5:1, 32563, 1387, 1, 0, 0);
add (06093,"Siskiyou County", "CA", "California", "9", -8:1, 44044, 3240, 0, 1, 0);
add (36059,"Nassau County", "NY", "New York", "1", -5:1, 1302220, 1387, 1, 0, 0);
add (36097,"Schuyler County", "NY", "New York", "1", -5:1, 19125, 1387, 1, 0, 0);
add (31057,"Dundy County", "NE", "Nebraska", "6", -6:1, 2302, 1148, 1, 0, 0);
add (37147,"Pitt County", "NC", "North Carolina", "2", -5:1, 126630, 1387, 1, 0, 0);
add (51520,"Bristol city", "VA", "Virginia", "2", -5:1, 17486, 1387, 1, 0, 0);
add (54087,"Roane County", "WV", "West Virginia", "2", -5:1, 15342, 1387, 1, 0, 0);
add (54103,"Wetzel County", "WV", "West Virginia", "2", -5:1, 18256, 1387, 1, 0, 0);
add (05147,"Woodruff County", "AR", "Arkansas", "7", -6:1, 8888, 1148, 1, 0, 0);
add (29119,"McDonald County", "MO", "Mosourri", "6", -6:1, 19887, 1148, 1, 0, 0);
add (40135,"Sequoyah County", "OK", "Oklahoma", "7", -6:1, 37531, 1148, 1, 0, 0);
add (13245,"Richmond County", "GA", "Georgia", "3", -5:1, 191329, 1387, 1, 0, 0);
add (26027,"Cass County", "MI", "Michigan", "4", -5:1, 49693, 1387, 1, 0, 0);
add (46077,"Kingsbury County", "SD", "South Dakota", "5", -7:1, 5712, 4647, 1, 0, 0);
add (30001,"Beaverhead County", "MT", "Montana", "6", -7:1, 8867, 4647, 1, 0, 0);
add (06041,"Marin County", "CA", "California", "9", -8:1, 236770, 3240, 0, 1, 0);
add (48219,"Hockley County", "TX", "Texas", "7", -6:1, 23788, 1148, 1, 0, 0);
add (34001,"Atlantic County", "NJ", "New Jersey", "0", -5:1, 238047, 1387, 1, 0, 0);
add (48029,"Bexar County", "TX", "Texas", "7", -6:1, 1353052, 1148, 1, 0, 0);
add (46101,"Moody County", "SD", "South Dakota", "5", -7:1, 6505, 4647, 1, 0, 0);
add (20153,"Rawlins County", "KS", "Kansas", "6", -6:1, 3125, 1148, 1, 0, 0);
add (05005,"Baxter County", "AR", "Arkansas", "7", -6:1, 36402, 1148, 1, 0, 0);
add (32021,"Mineral County", "NV", "Nevada", "8", -8:1, 5463, 3240, 0, 1, 0);
add (13275,"Thomas County", "GA", "Georgia", "3", -5:1, 42953, 1387, 1, 0, 0);
add (20123,"Mitchell County", "KS", "Kansas", "6", -6:1, 6936, 1148, 1, 0, 0);
add (29097,"Jasper County", "MO", "Mosourri", "6", -6:1, 99532, 1148, 1, 0, 0);
add (45081,"Saluda County", "SC", "South Carolina", "2", -5:1, 17025, 1387, 1, 0, 0);
add (17101,"Lawrence County", "IL", "Illinois", "6", -6:1, 15343, 1148, 1, 0, 0);
add (26087,"Lapeer County", "MI", "Michigan", "4", -5:1, 88270, 1387, 1, 0, 0);
add (32017,"Lincoln County", "NV", "Nevada", "8", -8:1, 4220, 3240, 0, 1, 0);
add (46003,"Aurora County", "SD", "South Dakota", "5", -6:1, 2975, 1148, 1, 0, 0);
add (21055,"Crittenden County", "KY", "Kentucky", "4", -6:1, 9574, 1148, 1, 0, 0);
add (13229,"Pierce County", "GA", "Georgia", "3", -5:1, 15794, 1387, 1, 0, 0);
add (48115,"Dawson County", "TX", "Texas", "7", -6:1, 14700, 1148, 1, 0, 0);
add (37085,"Harnett County", "NC", "North Carolina", "2", -5:1, 82391, 1387, 1, 0, 0);
add (39167,"Washington County", "OH", "Ohio", "4", -5:1, 63413, 1387, 1, 0, 0);
add (47189,"Wilson County", "TN", "Tennesee", "3", -5:1, 83923, 1387, 1, 0, 0);
add (05053,"Grant County", "AR", "Arkansas", "7", -6:1, 15897, 1148, 1, 0, 0);
add (46067,"Hutchinson County", "SD", "South Dakota", "5", -7:1, 8045, 4647, 1, 0, 0);
add (48351,"Newton County", "TX", "Texas", "7", -6:1, 14243, 1148, 1, 0, 0);
add (48045,"Briscoe County", "TX", "Texas", "7", -6:1, 1888, 1148, 1, 0, 0);
add (37019,"Brunswick County", "NC", "North Carolina", "2", -5:1, 68416, 1387, 1, 0, 0);
add (13313,"Whitfield County", "GA", "Georgia", "3", -5:1, 82039, 1387, 1, 0, 0);
add (54003,"Berkeley County", "WV", "West Virginia", "2", -5:1, 70970, 1387, 1, 0, 0);
add (13291,"Union County", "GA", "Georgia", "3", -5:1, 16519, 1387, 1, 0, 0);
add (36047,"Kings County", "NY", "New York", "1", -5:1, 2267942, 1387, 1, 0, 0);
add (42127,"Wayne County", "PA", "Pennsylvania", "1", -5:1, 45226, 1387, 1, 0, 0);
add (31145,"Red Willow County", "NE", "Nebraska", "6", -7:1, 11255, 4647, 1, 0, 0);
add (28061,"Jasper County", "MS", "Missippi", "5", -6:1, 17672, 1148, 1, 0, 0);
add (08119,"Teller County", "CO", "Colorado", "8", -7:1, 20606, 4647, 1, 0, 0);
add (19085,"Harrison County", "IA", "Iowa", "5", -6:1, 15364, 1148, 1, 0, 0);
add (19137,"Montgomery County", "IA", "Iowa", "5", -6:1, 11910, 1148, 1, 0, 0);
add (41005,"Clackamas County", "OR", "Oregon", "9", -8:1, 334732, 3240, 0, 1, 0);
add (42011,"Berks County", "PA", "Pennsylvania", "1", -5:1, 355956, 1387, 1, 0, 0);
add (26009,"Antrim County", "MI", "Michigan", "4", -5:1, 21522, 1387, 1, 0, 0);
add (08013,"Boulder County", "CO", "Colorado", "8", -7:1, 267274, 4647, 1, 0, 0);
add (26151,"Sanilac County", "MI", "Michigan", "4", -5:1, 42975, 1387, 1, 0, 0);
add (30031,"Gallatin County", "MT", "Montana", "6", -7:1, 62545, 4647, 1, 0, 0);
add (48009,"Archer County", "TX", "Texas", "7", -6:1, 8333, 1148, 1, 0, 0);
add (08065,"Lake County", "CO", "Colorado", "8", -7:1, 6391, 4647, 1, 0, 0);
add (47031,"Coffee County", "TN", "Tennesee", "3", -5:1, 45767, 1387, 1, 0, 0);
add (30087,"Rosebud County", "MT", "Montana", "6", -7:1, 10050, 4647, 1, 0, 0);
add (40053,"Grant County", "OK", "Oklahoma", "7", -6:1, 5338, 1148, 1, 0, 0);
add (38083,"Sheridan County", "ND", "North Dakota", "5", -6:1, 1694, 1148, 1, 0, 0);
add (21095,"Harlan County", "KY", "Kentucky", "4", -6:1, 34950, 1148, 1, 0, 0);
add (20029,"Cloud County", "KS", "Kansas", "6", -6:1, 10027, 1148, 1, 0, 0);
add (55035,"Eau Claire County", "WI", "Wisconnsin", "5", -6:1, 89287, 1148, 1, 0, 0);
add (21111,"Jefferson County", "KY", "Kentucky", "4", -6:1, 672104, 1148, 1, 0, 0);
add (36121,"Wyoming County", "NY", "New York", "1", -5:1, 44049, 1387, 1, 0, 0);
add (24047,"Worcester County", "MD", "Maryland", "2", -5:1, 42789, 1387, 1, 0, 0);
add (20209,"Wyandotte County", "KS", "Kansas", "6", -6:1, 152355, 1148, 1, 0, 0);
add (30103,"Treasure County", "MT", "Montana", "6", -7:1, 870, 4647, 1, 0, 0);
add (01019,"Cherokee County", "AL", "Alabama", "3", -6:1, 21833, 1148, 1, 0, 0);
add (20197,"Wabaunsee County", "KS", "Kansas", "6", -6:1, 6651, 1148, 1, 0, 0);
add (56045,"Weston County", "WY", "Wyoming", "8", -7:1, 6472, 4647, 1, 0, 0);
add (47173,"Union County", "TN", "Tennesee", "3", -6:1, 16260, 1148, 1, 0, 0);
add (12101,"Pasco County", "FL", "Florida", "3", -5:1, 325824, 1387, 1, 0, 0);
add (28081,"Lee County", "MS", "Missippi", "5", -6:1, 74637, 1148, 1, 0, 0);
add (29047,"Clay County", "MO", "Mosourri", "6", -6:1, 176206, 1148, 1, 0, 0);
add (30111,"Yellowstone County", "MT", "Montana", "6", -7:1, 126158, 4647, 1, 0, 0);
add (47139,"Polk County", "TN", "Tennesee", "3", -6:1, 14883, 1148, 1, 0, 0);
add (37109,"Lincoln County", "NC", "North Carolina", "2", -5:1, 58093, 1387, 1, 0, 0);
add (27129,"Renville County", "MN", "Minnesota", "5", -6:1, 16923, 1148, 1, 0, 0);
add (54005,"Boone County", "WV", "West Virginia", "2", -5:1, 26118, 1387, 1, 0, 0);
add (42033,"Clearfield County", "PA", "Pennsylvania", "1", -5:1, 80752, 1387, 1, 0, 0);
add (32013,"Humboldt County", "NV", "Nevada", "8", -8:1, 18145, 3240, 0, 1, 0);
add (35023,"Hidalgo County", "NM", "New Mexico", "8", -7:1, 6210, 4647, 1, 0, 0);
add (39157,"Tuscarawas County", "OH", "Ohio", "4", -5:1, 88608, 1387, 1, 0, 0);
add (20173,"Sedgwick County", "KS", "Kansas", "6", -6:1, 448050, 1148, 1, 0, 0);
add (13295,"Walker County", "GA", "Georgia", "3", -5:1, 63082, 1387, 1, 0, 0);
add (49043,"Summit County", "UT", "Utah", "8", -7:1, 26746, 4647, 1, 0, 0);
add (53065,"Stevens County", "WA", "Washiington", "9", -8:1, 39464, 3240, 0, 1, 0);
add (49007,"Carbon County", "UT", "Utah", "8", -7:1, 20966, 4647, 1, 0, 0);
add (16063,"Lincoln County", "ID", "Idaho", "8", -7:1, 3792, 4647, 1, 0, 0);
add (20037,"Crawford County", "KS", "Kansas", "6", -6:1, 36360, 1148, 1, 0, 0);
add (37025,"Cabarrus County", "NC", "North Carolina", "2", -5:1, 120057, 1387, 1, 0, 0);
add (38069,"Pierce County", "ND", "North Dakota", "5", -6:1, 4623, 1148, 1, 0, 0);
add (27097,"Morrison County", "MN", "Minnesota", "5", -6:1, 30543, 1148, 1, 0, 0);
add (51840,"Winchester city", "VA", "Virginia", "2", -5:1, 22659, 1387, 1, 0, 0);
add (54071,"Pendleton County", "WV", "West Virginia", "2", -5:1, 8062, 1387, 1, 0, 0);
add (05033,"Crawford County", "AR", "Arkansas", "7", -6:1, 50334, 1148, 1, 0, 0);
add (39073,"Hocking County", "OH", "Ohio", "4", -5:1, 29004, 1387, 1, 0, 0);
add (08031,"Denver County", "CO", "Colorado", "8", -7:1, 499055, 4647, 1, 0, 0);
add (48247,"Jim Hogg County", "TX", "Texas", "7", -6:1, 5007, 1148, 1, 0, 0);
add (13143,"Haralson County", "GA", "Georgia", "3", -5:1, 24653, 1387, 1, 0, 0);
add (26015,"Barry County", "MI", "Michigan", "4", -5:1, 54535, 1387, 1, 0, 0);
add (06113,"Yolo County", "CA", "California", "9", -8:1, 153849, 3240, 0, 1, 0);
add (19039,"Clarke County", "IA", "Iowa", "5", -6:1, 8362, 1148, 1, 0, 0);
add (17123,"Marshall County", "IL", "Illinois", "6", -6:1, 12882, 1148, 1, 0, 0);
add (29095,"Jackson County", "MO", "Mosourri", "6", -6:1, 654986, 1148, 1, 0, 0);
add (48227,"Howard County", "TX", "Texas", "7", -6:1, 32051, 1148, 1, 0, 0);
add (01103,"Morgan County", "AL", "Alabama", "3", -6:1, 109369, 1148, 1, 0, 0);
add (02050,"Bethel Census Area", "AK", "Alaska", "9", -9:1, 15967, 1174, 0, 0, 1);
add (55073,"Marathon County", "WI", "Wisconnsin", "5", -6:1, 123223, 1148, 1, 0, 0);
add (51560,"Clifton Forge city", "VA", "Virginia", "2", -5:1, 4342, 1387, 1, 0, 0);
add (13047,"Catoosa County", "GA", "Georgia", "3", -5:1, 50547, 1387, 1, 0, 0);
add (50023,"Washington County", "VT", "Vermont", "0", -5:1, 56308, 1387, 1, 0, 0);
add (56037,"Sweetwater County", "WY", "Wyoming", "8", -7:1, 39780, 4647, 1, 0, 0);
add (21175,"Morgan County", "KY", "Kentucky", "4", -5:1, 13559, 1387, 1, 0, 0);
add (48505,"Zapata County", "TX", "Texas", "7", -6:1, 11491, 1148, 1, 0, 0);
add (04005,"Coconino County", "AZ", "Arizona", "8", -7:1, 114171, 4647, 1, 0, 0);
add (36037,"Genesee County", "NY", "New York", "1", -5:1, 60654, 1387, 1, 0, 0);
add (26007,"Alpena County", "MI", "Michigan", "4", -5:1, 30405, 1387, 1, 0, 0);
add (17063,"Grundy County", "IL", "Illinois", "6", -6:1, 36686, 1148, 1, 0, 0);
add (18049,"Fulton County", "IN", "Indiana", "4", -5:1, 20620, 1387, 1, 0, 0);
add (13205,"Mitchell County", "GA", "Georgia", "3", -5:1, 21176, 1387, 1, 0, 0);
add (48273,"Kleberg County", "TX", "Texas", "7", -6:1, 30163, 1148, 1, 0, 0);
add (38077,"Richland County", "ND", "North Dakota", "5", -6:1, 18272, 1148, 1, 0, 0);
add (42029,"Chester County", "PA", "Pennsylvania", "1", -5:1, 421686, 1387, 1, 0, 0);
add (13231,"Pike County", "GA", "Georgia", "3", -5:1, 12645, 1387, 1, 0, 0);
add (53071,"Walla Walla County", "WA", "Washiington", "9", -8:1, 53702, 3240, 0, 1, 0);
add (21061,"Edmonson County", "KY", "Kentucky", "4", -6:1, 11353, 1148, 1, 0, 0);
add (34009,"Cape May County", "NJ", "New Jersey", "0", -5:1, 98069, 1387, 1, 0, 0);
add (40027,"Cleveland County", "OK", "Oklahoma", "7", -6:1, 201110, 1148, 1, 0, 0);
add (47027,"Clay County", "TN", "Tennesee", "3", -5:1, 7255, 1387, 1, 0, 0);
add (06049,"Modoc County", "CA", "California", "9", -8:1, 9398, 3240, 0, 1, 0);
add (45023,"Chester County", "SC", "South Carolina", "2", -5:1, 34401, 1387, 1, 0, 0);
add (42113,"Sullivan County", "PA", "Pennsylvania", "1", -5:1, 6107, 1387, 1, 0, 0);
add (54105,"Wirt County", "WV", "West Virginia", "2", -5:1, 5669, 1387, 1, 0, 0);
add (06007,"Butte County", "CA", "California", "9", -8:1, 194597, 3240, 0, 1, 0);
add (16037,"Custer County", "ID", "Idaho", "8", -7:1, 4107, 4647, 1, 0, 0);
add (13219,"Oconee County", "GA", "Georgia", "3", -5:1, 23737, 1387, 1, 0, 0);
add (54057,"Mineral County", "WV", "West Virginia", "2", -5:1, 26737, 1387, 1, 0, 0);
add (26049,"Genesee County", "MI", "Michigan", "4", -5:1, 436084, 1387, 1, 0, 0);
add (13127,"Glynn County", "GA", "Georgia", "3", -5:1, 67320, 1387, 1, 0, 0);
add (29025,"Caldwell County", "MO", "Mosourri", "6", -6:1, 8838, 1148, 1, 0, 0);
add (13105,"Elbert County", "GA", "Georgia", "3", -5:1, 19335, 1387, 1, 0, 0);
add (42121,"Venango County", "PA", "Pennsylvania", "1", -5:1, 57844, 1387, 1, 0, 0);
add (28003,"Alcorn County", "MS", "Missippi", "5", -6:1, 32716, 1148, 1, 0, 0);
add (55051,"Iron County", "WI", "Wisconnsin", "5", -6:1, 6350, 1148, 1, 0, 0);
add (51115,"Mathews County", "VA", "Virginia", "2", -5:1, 9073, 1387, 1, 0, 0);
add (27065,"Kanabec County", "MN", "Minnesota", "5", -6:1, 14173, 1148, 1, 0, 0);
add (20149,"Pottawatomie County", "KS", "Kansas", "6", -6:1, 18691, 1148, 1, 0, 0);
add (25019,"Nantucket County", "MA", "Massachusetts", "0", -5:1, 7844, 1387, 1, 0, 0);
add (19117,"Lucas County", "IA", "Iowa", "5", -6:1, 9152, 1148, 1, 0, 0);
add (38015,"Burleigh County", "ND", "North Dakota", "5", -6:1, 66867, 1148, 1, 0, 0);
add (41041,"Lincoln County", "OR", "Oregon", "9", -8:1, 45368, 3240, 0, 1, 0);
add (50013,"Grand Isle County", "VT", "Vermont", "0", -5:1, 6236, 1387, 1, 0, 0);
add (47055,"Giles County", "TN", "Tennesee", "3", -5:1, 28925, 1387, 1, 0, 0);
add (01007,"Bibb County", "AL", "Alabama", "3", -6:1, 18926, 1148, 1, 0, 0);
add (51143,"Pittsylvania County", "VA", "Virginia", "2", -5:1, 57384, 1387, 1, 0, 0);
add (51061,"Fauquier County", "VA", "Virginia", "2", -5:1, 54109, 1387, 1, 0, 0);
add (48069,"Castro County", "TX", "Texas", "7", -6:1, 8357, 1148, 1, 0, 0);
add (39071,"Highland County", "OH", "Ohio", "4", -5:1, 40364, 1387, 1, 0, 0);
add (02185,"North Slope Borough", "AK", "Alaska", "9", -9:1, 7152, 1174, 0, 0, 1);
add (56015,"Goshen County", "WY", "Wyoming", "8", -7:1, 12886, 4647, 1, 0, 0);
add (17165,"Saline County", "IL", "Illinois", "6", -6:1, 26149, 1148, 1, 0, 0);
add (27167,"Wilkin County", "MN", "Minnesota", "5", -6:1, 7312, 1148, 1, 0, 0);
add (28009,"Benton County", "MS", "Missippi", "5", -6:1, 8140, 1148, 1, 0, 0);
add (28013,"Calhoun County", "MS", "Missippi", "5", -6:1, 14822, 1148, 1, 0, 0);
add (12107,"Putnam County", "FL", "Florida", "3", -5:1, 70419, 1387, 1, 0, 0);
add (31041,"Custer County", "NE", "Nebraska", "6", -6:1, 12026, 1148, 1, 0, 0);
add (17099,"La Salle County", "IL", "Illinois", "6", -6:1, 110189, 1148, 1, 0, 0);
add (47175,"Van Buren County", "TN", "Tennesee", "3", -6:1, 5071, 1148, 1, 0, 0);
add (13065,"Clinch County", "GA", "Georgia", "3", -5:1, 6660, 1387, 1, 0, 0);
add (17111,"McHenry County", "IL", "Illinois", "6", -6:1, 240945, 1148, 1, 0, 0);
add (22115,"Vernon Parish", "LA", "Louisiana", "7", -6:1, 51570, 1148, 1, 0, 0);
add (48349,"Navarro County", "TX", "Texas", "7", -6:1, 41738, 1148, 1, 0, 0);
add (17115,"Macon County", "IL", "Illinois", "6", -6:1, 113772, 1148, 1, 0, 0);
add (51003,"Albemarle County", "VA", "Virginia", "2", -5:1, 78401, 1387, 1, 0, 0);
add (01087,"Macon County", "AL", "Alabama", "3", -6:1, 22951, 1148, 1, 0, 0);
add (54007,"Braxton County", "WV", "West Virginia", "2", -5:1, 13185, 1387, 1, 0, 0);
add (39113,"Montgomery County", "OH", "Ohio", "4", -5:1, 558427, 1387, 1, 0, 0);
add (12053,"Hernando County", "FL", "Florida", "3", -5:1, 127227, 1387, 1, 0, 0);
add (37189,"Watauga County", "NC", "North Carolina", "2", -5:1, 40965, 1387, 1, 0, 0);
add (40009,"Beckham County", "OK", "Oklahoma", "7", -6:1, 19584, 1148, 1, 0, 0);
add (18063,"Hendricks County", "IN", "Indiana", "4", -5:1, 95146, 1387, 1, 0, 0);
add (42069,"Lackawanna County", "PA", "Pennsylvania", "1", -5:1, 208455, 1387, 1, 0, 0);
add (56027,"Niobrara County", "WY", "Wyoming", "8", -7:1, 2706, 4647, 1, 0, 0);
add (19099,"Jasper County", "IA", "Iowa", "5", -6:1, 35961, 1148, 1, 0, 0);
add (55089,"Ozaukee County", "WI", "Wisconnsin", "5", -6:1, 81076, 1148, 1, 0, 0);
add (37133,"Onslow County", "NC", "North Carolina", "2", -5:1, 142358, 1387, 1, 0, 0);
add (38047,"Logan County", "ND", "North Dakota", "5", -6:1, 2355, 1148, 1, 0, 0);
add (30061,"Mineral County", "MT", "Montana", "6", -7:1, 3748, 4647, 1, 0, 0);
add (48077,"Clay County", "TX", "Texas", "7", -6:1, 10567, 1148, 1, 0, 0);
add (48343,"Morris County", "TX", "Texas", "7", -6:1, 13358, 1148, 1, 0, 0);
add (13263,"Talbot County", "GA", "Georgia", "3", -5:1, 6935, 1387, 1, 0, 0);
add (38035,"Grand Forks County", "ND", "North Dakota", "5", -6:1, 66869, 1148, 1, 0, 0);
add (28109,"Pearl River County", "MS", "Missippi", "5", -6:1, 46862, 1148, 1, 0, 0);
add (48307,"McCulloch County", "TX", "Texas", "7", -6:1, 8751, 1148, 1, 0, 0);
add (41023,"Grant County", "OR", "Oregon", "9", -8:1, 8075, 3240, 0, 1, 0);
add (42005,"Armstrong County", "PA", "Pennsylvania", "1", -5:1, 73181, 1387, 1, 0, 0);
add (31181,"Webster County", "NE", "Nebraska", "6", -7:1, 4019, 4647, 1, 0, 0);
add (37171,"Surry County", "NC", "North Carolina", "2", -5:1, 67052, 1387, 1, 0, 0);
add (31025,"Cass County", "NE", "Nebraska", "6", -6:1, 24486, 1148, 1, 0, 0);
add (16023,"Butte County", "ID", "Idaho", "8", -7:1, 3033, 4647, 1, 0, 0);
add (40087,"McClain County", "OK", "Oklahoma", "7", -6:1, 26224, 1148, 1, 0, 0);
add (21151,"Madison County", "KY", "Kentucky", "4", -5:1, 66502, 1387, 1, 0, 0);
add (32019,"Lyon County", "NV", "Nevada", "8", -8:1, 30072, 3240, 0, 1, 0);
add (21197,"Powell County", "KY", "Kentucky", "4", -5:1, 12945, 1387, 1, 0, 0);
add (13019,"Berrien County", "GA", "Georgia", "3", -5:1, 16353, 1387, 1, 0, 0);
add (29147,"Nodaway County", "MO", "Mosourri", "6", -6:1, 20777, 1148, 1, 0, 0);
add (13023,"Bleckley County", "GA", "Georgia", "3", -5:1, 11185, 1387, 1, 0, 0);
add (42097,"Northumberland County", "PA", "Pennsylvania", "1", -5:1, 94017, 1387, 1, 0, 0);
add (42123,"Warren County", "PA", "Pennsylvania", "1", -5:1, 43910, 1387, 1, 0, 0);
add (31107,"Knox County", "NE", "Nebraska", "6", -7:1, 9216, 4647, 1, 0, 0);
add (48473,"Waller County", "TX", "Texas", "7", -6:1, 27218, 1148, 1, 0, 0);
add (48403,"Sabine County", "TX", "Texas", "7", -6:1, 10551, 1148, 1, 0, 0);
add (28113,"Pike County", "MS", "Missippi", "5", -6:1, 37920, 1148, 1, 0, 0);
add (08103,"Rio Blanco County", "CO", "Colorado", "8", -7:1, 6265, 4647, 1, 0, 0);
add (39053,"Gallia County", "OH", "Ohio", "4", -5:1, 33422, 1387, 1, 0, 0);
add (29209,"Stone County", "MO", "Mosourri", "6", -6:1, 26807, 1148, 1, 0, 0);
add (46087,"McCook County", "SD", "South Dakota", "5", -7:1, 5598, 4647, 1, 0, 0);
add (13107,"Emanuel County", "GA", "Georgia", "3", -5:1, 21023, 1387, 1, 0, 0);
add (39125,"Paulding County", "OH", "Ohio", "4", -5:1, 20078, 1387, 1, 0, 0);
add (06079,"San Luis Obispo County", "CA", "California", "9", -8:1, 234366, 3240, 0, 1, 0);
add (12129,"Wakulla County", "FL", "Florida", "3", -5:1, 18652, 1387, 1, 0, 0);
add (53059,"Skamania County", "WA", "Washiington", "9", -8:1, 9805, 3240, 0, 1, 0);
add (13123,"Gilmer County", "GA", "Georgia", "3", -5:1, 18672, 1387, 1, 0, 0);
add (45047,"Greenwood County", "SC", "South Carolina", "2", -5:1, 63623, 1387, 1, 0, 0);
add (32007,"Elko County", "NV", "Nevada", "8", -8:1, 46084, 3240, 0, 1, 0);
add (55019,"Clark County", "WI", "Wisconnsin", "5", -6:1, 33147, 1148, 1, 0, 0);
add (13161,"Jeff Davis County", "GA", "Georgia", "3", -5:1, 12751, 1387, 1, 0, 0);
add (26005,"Allegan County", "MI", "Michigan", "4", -5:1, 101662, 1387, 1, 0, 0);
add (28161,"Yalobusha County", "MS", "Missippi", "5", -6:1, 12366, 1148, 1, 0, 0);
add (47047,"Fayette County", "TN", "Tennesee", "3", -5:1, 30457, 1387, 1, 0, 0);
add (36045,"Jefferson County", "NY", "New York", "1", -5:1, 111050, 1387, 1, 0, 0);
add (29121,"Macon County", "MO", "Mosourri", "6", -6:1, 15278, 1148, 1, 0, 0);
add (01043,"Cullman County", "AL", "Alabama", "3", -6:1, 74994, 1148, 1, 0, 0);
add (17175,"Stark County", "IL", "Illinois", "6", -6:1, 6290, 1148, 1, 0, 0);
add (47153,"Sequatchie County", "TN", "Tennesee", "3", -6:1, 10367, 1148, 1, 0, 0);
add (48451,"Tom Green County", "TX", "Texas", "7", -6:1, 102775, 1148, 1, 0, 0);
add (18139,"Rush County", "IN", "Indiana", "4", -5:1, 18307, 1387, 1, 0, 0);
add (51680,"Lynchburg city", "VA", "Virginia", "2", -5:1, 65473, 1387, 1, 0, 0);
add (23001,"Androscoggin County", "ME", "Maine", "0", -5:1, 101280, 1387, 1, 0, 0);
add (38095,"Towner County", "ND", "North Dakota", "5", -6:1, 3018, 1148, 1, 0, 0);
add (46019,"Butte County", "SD", "South Dakota", "5", -6:1, 9018, 1148, 1, 0, 0);
add (32510,"Carson City", "NV", "Nevada", "8", -8:1, 49301, 3240, 0, 1, 0);
add (46069,"Hyde County", "SD", "South Dakota", "5", -7:1, 1605, 4647, 1, 0, 0);
add (05001,"Arkansas County", "AR", "Arkansas", "7", -6:1, 20787, 1148, 1, 0, 0);
add (40015,"Caddo County", "OK", "Oklahoma", "7", -6:1, 30981, 1148, 1, 0, 0);
add (26035,"Clare County", "MI", "Michigan", "4", -5:1, 29578, 1387, 1, 0, 0);
add (21155,"Marion County", "KY", "Kentucky", "4", -5:1, 17018, 1387, 1, 0, 0);
add (48127,"Dimmit County", "TX", "Texas", "7", -6:1, 10364, 1148, 1, 0, 0);
add (37023,"Burke County", "NC", "North Carolina", "2", -5:1, 82687, 1387, 1, 0, 0);
add (21187,"Owen County", "KY", "Kentucky", "4", -5:1, 10264, 1387, 1, 0, 0);
add (42039,"Crawford County", "PA", "Pennsylvania", "1", -5:1, 89415, 1387, 1, 0, 0);
add (53011,"Clark County", "WA", "Washiington", "9", -8:1, 326943, 3240, 0, 1, 0);
add (39121,"Noble County", "OH", "Ohio", "4", -5:1, 12343, 1387, 1, 0, 0);
add (38039,"Griggs County", "ND", "North Dakota", "5", -6:1, 2842, 1148, 1, 0, 0);
add (20147,"Phillips County", "KS", "Kansas", "6", -6:1, 6080, 1148, 1, 0, 0);
add (38063,"Nelson County", "ND", "North Dakota", "5", -6:1, 3716, 1148, 1, 0, 0);
add (50017,"Orange County", "VT", "Vermont", "0", -5:1, 27924, 1387, 1, 0, 0);
add (13293,"Upson County", "GA", "Georgia", "3", -5:1, 27075, 1387, 1, 0, 0);
add (30081,"Ravalli County", "MT", "Montana", "6", -7:1, 35156, 4647, 1, 0, 0);
add (48099,"Coryell County", "TX", "Texas", "7", -6:1, 77981, 1148, 1, 0, 0);
add (26099,"Macomb County", "MI", "Michigan", "4", -5:1, 787698, 1387, 1, 0, 0);
add (47013,"Campbell County", "TN", "Tennesee", "3", -5:1, 38241, 1387, 1, 0, 0);
add (34039,"Union County", "NJ", "New Jersey", "0", -5:1, 500608, 1387, 1, 0, 0);
add (21079,"Garrard County", "KY", "Kentucky", "4", -6:1, 13916, 1148, 1, 0, 0);
add (41003,"Benton County", "OR", "Oregon", "9", -8:1, 77755, 3240, 0, 1, 0);
add (29109,"Lawrence County", "MO", "Mosourri", "6", -6:1, 33122, 1148, 1, 0, 0);
add (56017,"Hot Springs County", "WY", "Wyoming", "8", -7:1, 4727, 4647, 1, 0, 0);
add (51161,"Roanoke County", "VA", "Virginia", "2", -5:1, 80839, 1387, 1, 0, 0);
add (18079,"Jennings County", "IN", "Indiana", "4", -5:1, 27789, 1387, 1, 0, 0);
add (47071,"Hardin County", "TN", "Tennesee", "3", -5:1, 24961, 1387, 1, 0, 0);
add (48489,"Willacy County", "TX", "Texas", "7", -6:1, 19622, 1148, 1, 0, 0);
add (27011,"Big Stone County", "MN", "Minnesota", "5", -6:1, 5654, 1148, 1, 0, 0);
add (37125,"Moore County", "NC", "North Carolina", "2", -5:1, 71394, 1387, 1, 0, 0);
add (13045,"Carroll County", "GA", "Georgia", "3", -5:1, 83021, 1387, 1, 0, 0);
add (36087,"Rockland County", "NY", "New York", "1", -5:1, 281338, 1387, 1, 0, 0);
add (22073,"Ouachita Parish", "LA", "Louisiana", "7", -6:1, 146979, 1148, 1, 0, 0);
add (16011,"Bingham County", "ID", "Idaho", "8", -7:1, 41820, 4647, 1, 0, 0);
add (48053,"Burnet County", "TX", "Texas", "7", -6:1, 32195, 1148, 1, 0, 0);
add (48095,"Concho County", "TX", "Texas", "7", -6:1, 3119, 1148, 1, 0, 0);
add (46091,"Marshall County", "SD", "South Dakota", "5", -7:1, 4563, 4647, 1, 0, 0);
add (30043,"Jefferson County", "MT", "Montana", "6", -7:1, 10087, 4647, 1, 0, 0);
add (29013,"Bates County", "MO", "Mosourri", "6", -6:1, 15770, 1148, 1, 0, 0);
add (40003,"Alfalfa County", "OK", "Oklahoma", "7", -6:1, 6044, 1148, 1, 0, 0);
add (21109,"Jackson County", "KY", "Kentucky", "4", -6:1, 12908, 1148, 1, 0, 0);
add (40117,"Pawnee County", "OK", "Oklahoma", "7", -6:1, 16438, 1148, 1, 0, 0);
add (06029,"Kern County", "CA", "California", "9", -8:1, 631459, 3240, 0, 1, 0);
add (56031,"Platte County", "WY", "Wyoming", "8", -7:1, 8626, 4647, 1, 0, 0);
add (28021,"Claiborne County", "MS", "Missippi", "5", -6:1, 11662, 1148, 1, 0, 0);
add (13177,"Lee County", "GA", "Georgia", "3", -5:1, 22767, 1387, 1, 0, 0);
add (28079,"Leake County", "MS", "Missippi", "5", -6:1, 19372, 1148, 1, 0, 0);
add (36111,"Ulster County", "NY", "New York", "1", -5:1, 166351, 1387, 1, 0, 0);
add (31019,"Buffalo County", "NE", "Nebraska", "6", -6:1, 40596, 1148, 1, 0, 0);
add (54049,"Marion County", "WV", "West Virginia", "2", -5:1, 56318, 1387, 1, 0, 0);
add (27101,"Murray County", "MN", "Minnesota", "5", -6:1, 9517, 1148, 1, 0, 0);
add (19149,"Plymouth County", "IA", "Iowa", "5", -6:1, 24825, 1148, 1, 0, 0);
add (16029,"Caribou County", "ID", "Idaho", "8", -7:1, 7426, 4647, 1, 0, 0);
add (41053,"Polk County", "OR", "Oregon", "9", -8:1, 61560, 3240, 0, 1, 0);
add (01059,"Franklin County", "AL", "Alabama", "3", -6:1, 29682, 1148, 1, 0, 0);
add (16087,"Washington County", "ID", "Idaho", "8", -7:1, 10171, 4647, 1, 0, 0);
