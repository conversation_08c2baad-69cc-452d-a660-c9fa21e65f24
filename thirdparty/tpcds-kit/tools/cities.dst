--
-- Legal Notice
--
-- This document and associated source code (the "Work") is a part of a
-- benchmark specification maintained by the TPC.
--
-- The TPC reserves all right, title, and interest to the Work as provided
-- under U.S. and international laws, including without limitation all patent
-- and trademark rights therein.
--
-- No Warranty
--
-- 1.1 TO THE MAXIMUM EXTENT PERMITTED BY APPLICABLE LAW, THE INFORMATION
--     CONTAINED HEREIN IS PROVIDED "AS IS" AND WITH ALL FAULTS, AND THE
--     AUTHORS AND DEVELOPERS OF THE WORK HEREBY DISCLAIM ALL OTHER
--     WARRANTIES AND CONDITIONS, EITHER EXPRESS, IMPLIED OR STATUTORY,
--     INCLUDING, BUT NOT LIMITED TO, ANY (IF ANY) IMPLIED WARRANTIES,
--     DUTIES OR CONDITIONS OF MERCHANTABILITY, OF FITNESS FOR A PARTICULAR
--     PURPOSE, OF ACCURACY OR COMPLETENESS OF RESPONSES, OF RESULTS, OF
--     W<PERSON><PERSON><PERSON><PERSON>IKE EFFORT, OF LACK OF VIRUSES, AND OF LACK OF NEGLIGENCE.
--     ALSO, THERE IS NO WARRANTY OR CONDITION OF TITLE, QUIET ENJOYMENT,
--     QUIET POSSESSION, CORRESPONDENCE TO DESCRIPTION OR NON-INFRINGEMENT
--     WITH REGARD TO THE WORK.
-- 1.2 IN NO EVENT WILL ANY AUTHOR OR DEVELOPER OF THE WORK BE LIABLE TO
--     ANY OTHER PARTY FOR ANY DAMAGES, INCLUDING BUT NOT LIMITED TO THE
--     COST OF PROCURING SUBSTITUTE GOODS OR SERVICES, LOST PROFITS, LOSS
--     OF USE, LOSS OF DATA, OR ANY INCIDENTAL, CONSEQUENTIAL, DIRECT,
--     INDIRECT, OR SPECIAL DAMAGES WHETHER UNDER CONTRACT, TORT, WARRANTY,
--     OR OTHERWISE, ARISING IN ANY WAY OUT OF THIS OR ANY OTHER AGREEMENT
--     RELATING TO THE WORK, WHETHER OR NOT SUCH AUTHOR OR DEVELOPER HAD
--     ADVANCE NOTICE OF THE POSSIBILITY OF SUCH DAMAGES.
--
-- Contributors:
-- Gradient Systems
--
--
--
--
--
------
-- cities.dst
-- the 1000 most common place names, from the USGS list of populated places
--   second level used to populate the item hierarchy
-- values			weights
-- -----------------------
--	1. city name	1. skewed from USGS (usgs)
--			2. uniform (uniform)
--			3. large cities (large)
--			4. medium step (medium)
--			5. small cities (small)
--			6. unified step function
------
create cities;
set types = (varchar);
set weights = 6;
set names = (name:usgs, uniform, large, medium, small, unified);
add ("Midway":212, 1, 1, 0, 0, 600);
add ("Fairview":199, 1, 1, 0, 0, 600);
add ("Oak Grove":160, 1, 1, 0, 0, 600);
add ("Five Points":147, 1, 1, 0, 0, 600);
add ("Pleasant Hill":119, 1, 1, 0, 0, 600);
add ("Riverside":117, 1, 1, 0, 0, 600);
add ("Mount Pleasant":116, 1, 1, 0, 0, 600);
add ("Centerville":108, 1, 1, 0, 0, 600);
add ("Bethel":108, 1, 1, 0, 0, 600);
add ("New Hope":105, 1, 1, 0, 0, 600);
add ("Liberty":98, 1, 1, 0, 0, 600);
add ("Union":94, 1, 1, 0, 0, 600);
add ("Pleasant Valley":90, 1, 1, 0, 0, 600);
add ("Oakland":87, 1, 1, 0, 0, 600);
add ("Salem":86, 1, 1, 0, 0, 600);
add ("Pleasant Grove":86, 1, 1, 0, 0, 600);
add ("Greenwood":85, 1, 1, 0, 0, 600);
add ("Shady Grove":84, 1, 1, 0, 0, 600);
add ("Pine Grove":82, 1, 1, 0, 0, 600);
add ("Shiloh":78, 1, 1, 0, 0, 600);
add ("Oak Hill":77, 1, 1, 0, 0, 600);
add ("Concord":74, 1, 1, 0, 0, 600);
add ("Georgetown":73, 1, 1, 0, 0, 600);
add ("Cedar Grove":73, 1, 1, 0, 0, 600);
add ("Lakeview":72, 1, 1, 0, 0, 600);
add ("Antioch":71, 1, 1, 0, 0, 600);
add ("Glendale":70, 1, 1, 0, 0, 600);
add ("Hopewell":67, 1, 1, 0, 0, 600);
add ("Friendship":67, 1, 1, 0, 0, 600);
add ("Sunnyside":66, 1, 1, 0, 0, 600);
add ("Spring Hill":65, 1, 1, 0, 0, 600);
add ("Lakewood":65, 1, 1, 0, 0, 600);
add ("Springfield":64, 1, 1, 0, 0, 600);
add ("Stringtown":62, 1, 1, 0, 0, 600);
add ("Harmony":62, 1, 1, 0, 0, 600);
add ("Riverview":60, 1, 1, 0, 0, 600);
add ("Buena Vista":60, 1, 1, 0, 0, 600);
add ("Highland":59, 1, 1, 0, 0, 600);
add ("Highland Park":58, 1, 1, 0, 0, 600);
add ("Franklin":58, 1, 1, 0, 0, 600);
add ("Woodlawn":56, 1, 1, 0, 0, 600);
add ("Mount Vernon":56, 1, 1, 0, 0, 600);
add ("Lakeside":56, 1, 1, 0, 0, 600);
add ("Glenwood":55, 1, 1, 0, 0, 600);
add ("Fairfield":55, 1, 1, 0, 0, 600);
add ("Oakdale":54, 1, 1, 0, 0, 600);
add ("Spring Valley":53, 1, 1, 0, 0, 600);
add ("Walnut Grove":52, 1, 1, 0, 0, 600);
add ("Providence":52, 1, 1, 0, 0, 600);
add ("Mount Zion":52, 1, 1, 0, 0, 600);
add ("Greenville":52, 1, 1, 0, 0, 600);
add ("Mount Olive":51, 1, 1, 0, 0, 600);
add ("Wildwood":50, 1, 1, 0, 0, 600);
add ("Hillcrest":50, 1, 1, 0, 0, 600);
add ("Crossroads":50, 1, 1, 0, 0, 600);
add ("Belmont":50, 1, 1, 0, 0, 600);
add ("Wilson":49, 1, 1, 0, 0, 600);
add ("Riverdale":49, 1, 1, 0, 0, 600);
add ("Newport":49, 1, 1, 0, 0, 600);
add ("Springdale":48, 1, 1, 0, 0, 600);
add ("Mountain View":48, 1, 1, 0, 0, 600);
add ("Forest Hills":48, 1, 1, 0, 0, 600);
add ("Bridgeport":48, 1, 1, 0, 0, 600);
add ("White Oak":47, 1, 1, 0, 0, 600);
add ("Oakwood":47, 1, 1, 0, 0, 600);
add ("Newtown":47, 1, 1, 0, 0, 600);
add ("Macedonia":47, 1, 1, 0, 0, 600);
add ("Five Forks":47, 1, 1, 0, 0, 600);
add ("Edgewood":47, 1, 1, 0, 0, 600);
add ("Arlington":47, 1, 1, 0, 0, 600);
add ("Unionville":46, 1, 1, 0, 0, 600);
add ("Red Hill":46, 1, 1, 0, 0, 600);
add ("Clinton":46, 1, 1, 0, 0, 600);
add ("Woodville":45, 1, 1, 0, 0, 600);
add ("Summit":45, 1, 1, 0, 0, 600);
add ("Jamestown":45, 1, 1, 0, 0, 600);
add ("Hamilton":45, 1, 1, 0, 0, 600);
add ("Clifton":45, 1, 1, 0, 0, 600);
add ("Union Hill":44, 1, 1, 0, 0, 600);
add ("Sulphur Springs":44, 1, 1, 0, 0, 600);
add ("Lincoln":44, 1, 1, 0, 0, 600);
add ("Lebanon":44, 1, 1, 0, 0, 600);
add ("Farmington":44, 1, 1, 0, 0, 600);
add ("Enterprise":43, 1, 1, 0, 0, 600);
add ("Brownsville":43, 1, 1, 0, 0, 600);
add ("Ashland":43, 1, 1, 0, 0, 600);
add ("Woodland":42, 1, 1, 0, 0, 600);
add ("Waterloo":42, 1, 1, 0, 0, 600);
add ("Valley View":42, 1, 1, 0, 0, 600);
add ("Oak Ridge":42, 1, 1, 0, 0, 600);
add ("Maple Grove":42, 1, 1, 0, 0, 600);
add ("Kingston":42, 1, 1, 0, 0, 600);
add ("Jackson":42, 1, 1, 0, 0, 600);
add ("Greenfield":42, 1, 1, 0, 0, 600);
add ("Green Acres":42, 1, 1, 0, 0, 600);
add ("Plainview":41, 1, 1, 0, 0, 600);
add ("Marion":41, 1, 1, 0, 0, 600);
add ("Florence":41, 1, 1, 0, 0, 600);
add ("Deerfield":41, 1, 1, 0, 0, 600);
add ("Bunker Hill":41, 1, 1, 0, 0, 600);
add ("Smithville":40, 1, 0, 0, 1, 1);
add ("Rockville":40, 1, 0, 0, 1, 1);
add ("Melrose":40, 1, 0, 0, 1, 1);
add ("Magnolia":40, 1, 0, 0, 1, 1);
add ("Jefferson":40, 1, 0, 0, 1, 1);
add ("Jacksonville":40, 1, 0, 0, 1, 1);
add ("Hebron":40, 1, 0, 0, 1, 1);
add ("Avondale":40, 1, 0, 0, 1, 1);
add ("Petersburg":39, 1, 0, 0, 1, 1);
add ("Needmore":39, 1, 0, 0, 1, 1);
add ("Mount Carmel":39, 1, 0, 0, 1, 1);
add ("Eureka":39, 1, 0, 0, 1, 1);
add ("Eden":39, 1, 0, 0, 1, 1);
add ("Dover":39, 1, 0, 0, 1, 1);
add ("Corinth":39, 1, 0, 0, 1, 1);
add ("Buffalo":39, 1, 0, 0, 1, 1);
add ("Rosedale":38, 1, 0, 0, 1, 1);
add ("Pleasant View":38, 1, 0, 0, 1, 1);
add ("Milton":38, 1, 0, 0, 1, 1);
add ("Independence":38, 1, 0, 0, 1, 1);
add ("Grandview":38, 1, 0, 0, 1, 1);
add ("Four Corners":38, 1, 0, 0, 1, 1);
add ("Cloverdale":38, 1, 0, 0, 1, 1);
add ("Westwood":37, 1, 0, 0, 1, 1);
add ("Vernon":37, 1, 0, 0, 1, 1);
add ("Sharon":37, 1, 0, 0, 1, 1);
add ("Richland":37, 1, 0, 0, 1, 1);
add ("Beulah":37, 1, 0, 0, 1, 1);
add ("Williamsburg":36, 1, 0, 0, 1, 1);
add ("Hollywood":36, 1, 0, 0, 1, 1);
add ("Hillsdale":36, 1, 0, 0, 1, 1);
add ("Ebenezer":36, 1, 0, 0, 1, 1);
add ("Cross Roads":36, 1, 0, 0, 1, 1);
add ("Winchester":35, 1, 0, 0, 1, 1);
add ("Washington":35, 1, 0, 0, 1, 1);
add ("Troy":35, 1, 0, 0, 1, 1);
add ("Prospect":35, 1, 0, 0, 1, 1);
add ("Oxford":35, 1, 0, 0, 1, 1);
add ("Norwood":35, 1, 0, 0, 1, 1);
add ("Mill Creek":35, 1, 0, 0, 1, 1);
add ("Middletown":35, 1, 0, 0, 1, 1);
add ("Jericho":35, 1, 0, 0, 1, 1);
add ("Forest Hills":35, 1, 0, 0, 1, 1);
add ("Columbia":35, 1, 0, 0, 1, 1);
add ("Bethlehem":35, 1, 0, 0, 1, 1);
add ("Anderson":35, 1, 0, 0, 1, 1);
add ("Waverly":34, 1, 0, 0, 1, 1);
add ("Watson":34, 1, 0, 0, 1, 1);
add ("Rose Hill":34, 1, 0, 0, 1, 1);
add ("Pine Hill":34, 1, 0, 0, 1, 1);
add ("Monroe":34, 1, 0, 0, 1, 1);
add ("Milltown":34, 1, 0, 0, 1, 1);
add ("Manchester":34, 1, 0, 0, 1, 1);
add ("Hilltop":34, 1, 0, 0, 1, 1);
add ("Harrisburg":34, 1, 0, 0, 1, 1);
add ("Dixie":34, 1, 0, 0, 1, 1);
add ("Bellevue":34, 1, 0, 0, 1, 1);
add ("Smyrna":33, 1, 0, 0, 1, 1);
add ("Sherwood Forest":33, 1, 0, 0, 1, 1);
add ("Pleasant Ridge":33, 1, 0, 0, 1, 1);
add ("Forest Park":33, 1, 0, 0, 1, 1);
add ("Dayton":33, 1, 0, 0, 1, 1);
add ("Clayton":33, 1, 0, 0, 1, 1);
add ("Chester":33, 1, 0, 0, 1, 1);
add ("Webster":32, 1, 0, 0, 1, 1);
add ("Warren":32, 1, 0, 0, 1, 1);
add ("Somerset":32, 1, 0, 0, 1, 1);
add ("Richmond":32, 1, 0, 0, 1, 1);
add ("Milford":32, 1, 0, 0, 1, 1);
add ("Johnson":32, 1, 0, 0, 1, 1);
add ("Evergreen":32, 1, 0, 0, 1, 1);
add ("Elmwood":32, 1, 0, 0, 1, 1);
add ("Bloomfield":32, 1, 0, 0, 1, 1);
add ("Bethany":32, 1, 0, 0, 1, 1);
add ("Auburn":32, 1, 0, 0, 1, 1);
add ("Williams":31, 1, 0, 0, 1, 1);
add ("Walker":31, 1, 0, 0, 1, 1);
add ("Rosemont":31, 1, 0, 0, 1, 1);
add ("Millville":31, 1, 0, 0, 1, 1);
add ("Mechanicsville":31, 1, 0, 0, 1, 1);
add ("Liberty Hill":31, 1, 0, 0, 1, 1);
add ("Huntington":31, 1, 0, 0, 1, 1);
add ("Hillsboro":31, 1, 0, 0, 1, 1);
add ("Hickory Grove":31, 1, 0, 0, 1, 1);
add ("Germantown":31, 1, 0, 0, 1, 1);
add ("Flat Rock":31, 1, 0, 0, 1, 1);
add ("Douglas":31, 1, 0, 0, 1, 1);
add ("Cleveland":31, 1, 0, 0, 1, 1);
add ("Central":31, 1, 0, 0, 1, 1);
add ("Brooklyn":31, 1, 0, 0, 1, 1);
add ("Brighton":31, 1, 0, 0, 1, 1);
add ("Alpine":31, 1, 0, 0, 1, 1);
add ("Weston":30, 1, 0, 0, 1, 1);
add ("Taylor":30, 1, 0, 0, 1, 1);
add ("Sherwood Forest":30, 1, 0, 0, 1, 1);
add ("Rockdale":30, 1, 0, 0, 1, 1);
add ("Princeton":30, 1, 0, 0, 1, 1);
add ("Preston":30, 1, 0, 0, 1, 1);
add ("Nelson":30, 1, 0, 0, 1, 1);
add ("Martin":30, 1, 0, 0, 1, 1);
add ("Madison":30, 1, 0, 0, 1, 1);
add ("Logan":30, 1, 0, 0, 1, 1);
add ("Linwood":30, 1, 0, 0, 1, 1);
add ("Hope":30, 1, 0, 0, 1, 1);
add ("Holland":30, 1, 0, 0, 1, 1);
add ("High Point":30, 1, 0, 0, 1, 1);
add ("Goshen":30, 1, 0, 0, 1, 1);
add ("Danville":30, 1, 0, 0, 1, 1);
add ("Burlington":30, 1, 0, 0, 1, 1);
add ("Wallace":29, 1, 0, 0, 1, 1);
add ("Rock Creek":29, 1, 0, 0, 1, 1);
add ("Oak Park":29, 1, 0, 0, 1, 1);
add ("Meadowbrook":29, 1, 0, 0, 1, 1);
add ("Locust Grove":29, 1, 0, 0, 1, 1);
add ("Lexington":29, 1, 0, 0, 1, 1);
add ("Howard":29, 1, 0, 0, 1, 1);
add ("Henderson":29, 1, 0, 0, 1, 1);
add ("Hanover":29, 1, 0, 0, 1, 1);
add ("Hampton":29, 1, 0, 0, 1, 1);
add ("Hamburg":29, 1, 0, 0, 1, 1);
add ("Five Corners":29, 1, 0, 0, 1, 1);
add ("Fairmount":29, 1, 0, 0, 1, 1);
add ("Chapel Hill":29, 1, 0, 0, 1, 1);
add ("Canton":29, 1, 0, 0, 1, 1);
add ("Campbell":29, 1, 0, 0, 1, 1);
add ("Berlin":29, 1, 0, 0, 1, 1);
add ("Baker":29, 1, 0, 0, 1, 1);
add ("Avon":29, 1, 0, 0, 1, 1);
add ("Arcadia":29, 1, 0, 0, 1, 1);
add ("Windsor":28, 1, 0, 0, 1, 1);
add ("Westfield":28, 1, 0, 0, 1, 1);
add ("Sardis":28, 1, 0, 0, 1, 1);
add ("Russellville":28, 1, 0, 0, 1, 1);
add ("Rogers":28, 1, 0, 0, 1, 1);
add ("Rock Springs":28, 1, 0, 0, 1, 1);
add ("Riverton":28, 1, 0, 0, 1, 1);
add ("Randolph":28, 1, 0, 0, 1, 1);
add ("Plymouth":28, 1, 0, 0, 1, 1);
add ("Piney Grove":28, 1, 0, 0, 1, 1);
add ("Pinehurst":28, 1, 0, 0, 1, 1);
add ("Mount Hope":28, 1, 0, 0, 1, 1);
add ("Montrose":28, 1, 0, 0, 1, 1);
add ("Miller":28, 1, 0, 0, 1, 1);
add ("Marshall":28, 1, 0, 0, 1, 1);
add ("Lowell":28, 1, 0, 0, 1, 1);
add ("Keystone":28, 1, 0, 0, 1, 1);
add ("Indian Hills":28, 1, 0, 0, 1, 1);
add ("Hudson":28, 1, 0, 0, 1, 1);
add ("Geneva":28, 1, 0, 0, 1, 1);
add ("Englewood":28, 1, 0, 0, 1, 1);
add ("Cottonwood":28, 1, 0, 0, 1, 1);
add ("Clyde":28, 1, 0, 0, 1, 1);
add ("Cedar Hill":28, 1, 0, 0, 1, 1);
add ("Bristol":28, 1, 0, 0, 1, 1);
add ("Beechwood":28, 1, 0, 0, 1, 1);
add ("Aurora":28, 1, 0, 0, 1, 1);
add ("West Point":27, 1, 0, 0, 1, 1);
add ("Trenton":27, 1, 0, 0, 1, 1);
add ("Sunset":27, 1, 0, 0, 1, 1);
add ("Sunrise":27, 1, 0, 0, 1, 1);
add ("Spring Creek":27, 1, 0, 0, 1, 1);
add ("Rolling Hills":27, 1, 0, 0, 1, 1);
add ("Portland":27, 1, 0, 0, 1, 1);
add ("Pine Ridge":27, 1, 0, 0, 1, 1);
add ("Paradise":27, 1, 0, 0, 1, 1);
add ("Monticello":27, 1, 0, 0, 1, 1);
add ("Monterey":27, 1, 0, 0, 1, 1);
add ("Mitchell":27, 1, 0, 0, 1, 1);
add ("Midland":27, 1, 0, 0, 1, 1);
add ("Jordan":27, 1, 0, 0, 1, 1);
add ("Green Valley":27, 1, 0, 0, 1, 1);
add ("Garfield":27, 1, 0, 0, 1, 1);
add ("Garden City":27, 1, 0, 0, 1, 1);
add ("Cold Spring":27, 1, 0, 0, 1, 1);
add ("Clarksville":27, 1, 0, 0, 1, 1);
add ("Cameron":27, 1, 0, 0, 1, 1);
add ("Cambridge":27, 1, 0, 0, 1, 1);
add ("Asbury":27, 1, 0, 0, 1, 1);
add ("Adams":27, 1, 0, 0, 1, 1);
add ("York":26, 1, 0, 0, 1, 1);
add ("White Rock":26, 1, 0, 0, 1, 1);
add ("Wakefield":26, 1, 0, 0, 1, 1);
add ("Victoria":26, 1, 0, 0, 1, 1);
add ("Unity":26, 1, 0, 0, 1, 1);
add ("Twin Lakes":26, 1, 0, 0, 1, 1);
add ("Three Forks":26, 1, 0, 0, 1, 1);
add ("Sycamore":26, 1, 0, 0, 1, 1);
add ("Springville":26, 1, 0, 0, 1, 1);
add ("Russell":26, 1, 0, 0, 1, 1);
add ("Ridgeway":26, 1, 0, 0, 1, 1);
add ("Pisgah":26, 1, 0, 0, 1, 1);
add ("Morgan":26, 1, 0, 0, 1, 1);
add ("Mayfield":26, 1, 0, 0, 1, 1);
add ("Maplewood":26, 1, 0, 0, 1, 1);
add ("Longview":26, 1, 0, 0, 1, 1);
add ("Linden":26, 1, 0, 0, 1, 1);
add ("Lancaster":26, 1, 0, 0, 1, 1);
add ("Harris":26, 1, 0, 0, 1, 1);
add ("Good Hope":26, 1, 0, 0, 1, 1);
add ("Gibson":26, 1, 0, 0, 1, 1);
add ("Fulton":26, 1, 0, 0, 1, 1);
add ("Deer Park":26, 1, 0, 0, 1, 1);
add ("Charleston":26, 1, 0, 0, 1, 1);
add ("Center":26, 1, 0, 0, 1, 1);
add ("Canaan":26, 1, 0, 0, 1, 1);
add ("Boston":26, 1, 0, 0, 1, 1);
add ("Beverly Hills":26, 1, 0, 0, 1, 1);
add ("Benton":26, 1, 0, 0, 1, 1);
add ("Woodstock":25, 1, 0, 0, 1, 1);
add ("White City":25, 1, 0, 0, 1, 1);
add ("Westville":25, 1, 0, 0, 1, 1);
add ("Waterford":25, 1, 0, 0, 1, 1);
add ("Walnut Hill":25, 1, 0, 0, 1, 1);
add ("Verona":25, 1, 0, 0, 1, 1);
add ("Turner":25, 1, 0, 0, 1, 1);
add ("Sterling":25, 1, 0, 0, 1, 1);
add ("Sheridan":25, 1, 0, 0, 1, 1);
add ("Sand Hill":25, 1, 0, 0, 1, 1);
add ("Pumpkin Center":25, 1, 0, 0, 1, 1);
add ("Powell":25, 1, 0, 0, 1, 1);
add ("Poplar Grove":25, 1, 0, 0, 1, 1);
add ("Perry":25, 1, 0, 0, 1, 1);
add ("Parker":25, 1, 0, 0, 1, 1);
add ("Old Town":25, 1, 0, 0, 1, 1);
add ("Oakley":25, 1, 0, 0, 1, 1);
add ("Newton":25, 1, 0, 0, 1, 1);
add ("New Salem":25, 1, 0, 0, 1, 1);
add ("Nashville":25, 1, 0, 0, 1, 1);
add ("Lyons":25, 1, 0, 0, 1, 1);
add ("Lone Star":25, 1, 0, 0, 1, 1);
add ("Klondike":25, 1, 0, 0, 1, 1);
add ("Johnstown":25, 1, 0, 0, 1, 1);
add ("Holly Springs":25, 1, 0, 0, 1, 1);
add ("Harrison":25, 1, 0, 0, 1, 1);
add ("Garland":25, 1, 0, 0, 1, 1);
add ("Foster":25, 1, 0, 0, 1, 1);
add ("Egypt":25, 1, 0, 0, 1, 1);
add ("Davis":25, 1, 0, 0, 1, 1);
add ("Chestnut Hill":25, 1, 0, 0, 1, 1);
add ("Camden":25, 1, 0, 0, 1, 1);
add ("Buckeye":25, 1, 0, 0, 1, 1);
add ("Brookside":25, 1, 0, 0, 1, 1);
add ("Brentwood":25, 1, 0, 0, 1, 1);
add ("Austin":25, 1, 0, 0, 1, 1);
add ("Allen":25, 1, 0, 0, 1, 1);
add ("Woodside":24, 1, 0, 0, 1, 1);
add ("Stony Point":24, 1, 0, 0, 1, 1);
add ("Stanley":24, 1, 0, 0, 1, 1);
add ("Simpson":24, 1, 0, 0, 1, 1);
add ("Silver Lake":24, 1, 0, 0, 1, 1);
add ("Saint Paul":24, 1, 0, 0, 1, 1);
add ("Rome":24, 1, 0, 0, 1, 1);
add ("Rockland":24, 1, 0, 0, 1, 1);
add ("Ridgewood":24, 1, 0, 0, 1, 1);
add ("Raymond":24, 1, 0, 0, 1, 1);
add ("Piedmont":24, 1, 0, 0, 1, 1);
add ("Paris":24, 1, 0, 0, 1, 1);
add ("Palmyra":24, 1, 0, 0, 1, 1);
add ("Orange":24, 1, 0, 0, 1, 1);
add ("Oakville":24, 1, 0, 0, 1, 1);
add ("Montgomery":24, 1, 0, 0, 1, 1);
add ("Lincoln Park":24, 1, 0, 0, 1, 1);
add ("Laurel":24, 1, 0, 0, 1, 1);
add ("Lake City":24, 1, 0, 0, 1, 1);
add ("Kenwood":24, 1, 0, 0, 1, 1);
add ("Jonesville":24, 1, 0, 0, 1, 1);
add ("Huntsville":24, 1, 0, 0, 1, 1);
add ("Hickory Hill":24, 1, 0, 0, 1, 1);
add ("Grant":24, 1, 0, 0, 1, 1);
add ("Fernwood":24, 1, 0, 0, 1, 1);
add ("Enon":24, 1, 0, 0, 1, 1);
add ("Columbus":24, 1, 0, 0, 1, 1);
add ("Carlisle":24, 1, 0, 0, 1, 1);
add ("Brookfield":24, 1, 0, 0, 1, 1);
add ("Bradford":24, 1, 0, 0, 1, 1);
add ("Boyd":24, 1, 0, 0, 1, 1);
add ("Baldwin":24, 1, 0, 0, 1, 1);
add ("Alton":24, 1, 0, 0, 1, 1);
add ("Allendale":24, 1, 0, 0, 1, 1);
add ("Whitehall":23, 1, 0, 0, 1, 1);
add ("Westwood":23, 1, 0, 0, 1, 1);
add ("Wayne":23, 1, 0, 0, 1, 1);
add ("Utica":23, 1, 0, 0, 1, 1);
add ("Union Grove":23, 1, 0, 0, 1, 1);
add ("Thornton":23, 1, 0, 0, 1, 1);
add ("Shirley":23, 1, 0, 0, 1, 1);
add ("Robinson":23, 1, 0, 0, 1, 1);
add ("Patterson":23, 1, 0, 0, 1, 1);
add ("Palestine":23, 1, 0, 0, 1, 1);
add ("Norton":23, 1, 0, 0, 1, 1);
add ("Northwood":23, 1, 0, 0, 1, 1);
add ("New Haven":23, 1, 0, 0, 1, 1);
add ("Moscow":23, 1, 0, 0, 1, 1);
add ("Lakeland":23, 1, 0, 0, 1, 1);
add ("Indian Springs":23, 1, 0, 0, 1, 1);
add ("Hillside":23, 1, 0, 0, 1, 1);
add ("Helena":23, 1, 0, 0, 1, 1);
add ("Harrisville":23, 1, 0, 0, 1, 1);
add ("Green Acres":23, 1, 0, 0, 1, 1);
add ("Fairfax":23, 1, 0, 0, 1, 1);
add ("Ellis":23, 1, 0, 0, 1, 1);
add ("Easton":23, 1, 0, 0, 1, 1);
add ("Durham":23, 1, 0, 0, 1, 1);
add ("Diamond":23, 1, 0, 0, 1, 1);
add ("Dale":23, 1, 0, 0, 1, 1);
add ("Cuba":23, 1, 0, 0, 1, 1);
add ("Cooper":23, 1, 0, 0, 1, 1);
add ("Cold Springs":23, 1, 0, 0, 1, 1);
add ("Cherry Hill":23, 1, 0, 0, 1, 1);
add ("Center Point":23, 1, 0, 0, 1, 1);
add ("Cairo":23, 1, 0, 0, 1, 1);
add ("Butler":23, 1, 0, 0, 1, 1);
add ("Buckhorn":23, 1, 0, 0, 1, 1);
add ("Beech Grove":23, 1, 0, 0, 1, 1);
add ("Augusta":23, 1, 0, 0, 1, 1);
add ("Athens":23, 1, 0, 0, 1, 1);
add ("Albion":23, 1, 0, 0, 1, 1);
add ("Winona":22, 1, 0, 0, 1, 1);
add ("Willard":22, 1, 0, 0, 1, 1);
add ("Westport":22, 1, 0, 0, 1, 1);
add ("West End":22, 1, 0, 0, 1, 1);
add ("Wellington":22, 1, 0, 0, 1, 1);
add ("Ward":22, 1, 0, 0, 1, 1);
add ("Victor":22, 1, 0, 0, 1, 1);
add ("Sunset Beach":22, 1, 0, 0, 1, 1);
add ("Stanton":22, 1, 0, 0, 1, 1);
add ("Spencer":22, 1, 0, 0, 1, 1);
add ("Smithfield":22, 1, 0, 0, 1, 1);
add ("Slabtown":22, 1, 0, 0, 1, 1);
add ("Sidney":22, 1, 0, 0, 1, 1);
add ("Sherwood":22, 1, 0, 0, 1, 1);
add ("Rock Hill":22, 1, 0, 0, 1, 1);
add ("Ramsey":22, 1, 0, 0, 1, 1);
add ("Porter":22, 1, 0, 0, 1, 1);
add ("Poplar Springs":22, 1, 0, 0, 1, 1);
add ("Perryville":22, 1, 0, 0, 1, 1);
add ("Palmer":22, 1, 0, 0, 1, 1);
add ("Newburg":22, 1, 0, 0, 1, 1);
add ("Middleton":22, 1, 0, 0, 1, 1);
add ("Mapleton":22, 1, 0, 0, 1, 1);
add ("Lawrence":22, 1, 0, 0, 1, 1);
add ("Lafayette":22, 1, 0, 0, 1, 1);
add ("Jonestown":22, 1, 0, 0, 1, 1);
add ("Jimtown":22, 1, 0, 0, 1, 1);
add ("Hunter":22, 1, 0, 0, 1, 1);
add ("Houston":22, 1, 0, 0, 1, 1);
add ("Happy Valley":22, 1, 0, 0, 1, 1);
add ("Hammond":22, 1, 0, 0, 1, 1);
add ("Green Hill":22, 1, 0, 0, 1, 1);
add ("Gordon":22, 1, 0, 0, 1, 1);
add ("Glencoe":22, 1, 0, 0, 1, 1);
add ("Elgin":22, 1, 0, 0, 1, 1);
add ("Dundee":22, 1, 0, 0, 1, 1);
add ("Duncan":22, 1, 0, 0, 1, 1);
add ("Delta":22, 1, 0, 0, 1, 1);
add ("Dalton":22, 1, 0, 0, 1, 1);
add ("Crawford":22, 1, 0, 0, 1, 1);
add ("Country Club Estates":22, 1, 0, 0, 1, 1);
add ("Cedar Springs":22, 1, 0, 0, 1, 1);
add ("Briarwood":22, 1, 0, 0, 1, 1);
add ("Bradley":22, 1, 0, 0, 1, 1);
add ("Bloomington":22, 1, 0, 0, 1, 1);
add ("Arnold":22, 1, 0, 0, 1, 1);
add ("Albany":22, 1, 0, 0, 1, 1);
add ("Adamsville":22, 1, 0, 0, 1, 1);
add ("Zion":21, 1, 0, 0, 1, 1);
add ("Westover":21, 1, 0, 0, 1, 1);
add ("Waterville":21, 1, 0, 0, 1, 1);
add ("Upton":21, 1, 0, 0, 1, 1);
add ("Trinity":21, 1, 0, 0, 1, 1);
add ("Thompson":21, 1, 0, 0, 1, 1);
add ("Thomas":21, 1, 0, 0, 1, 1);
add ("Tanglewood":21, 1, 0, 0, 1, 1);
add ("Sunshine":21, 1, 0, 0, 1, 1);
add ("Sugar Grove":21, 1, 0, 0, 1, 1);
add ("Stockton":21, 1, 0, 0, 1, 1);
add ("Snow Hill":21, 1, 0, 0, 1, 1);
add ("Sherman":21, 1, 0, 0, 1, 1);
add ("Scotland":21, 1, 0, 0, 1, 1);
add ("Roseville":21, 1, 0, 0, 1, 1);
add ("Roosevelt":21, 1, 0, 0, 1, 1);
add ("Rockport":21, 1, 0, 0, 1, 1);
add ("Rochester":21, 1, 0, 0, 1, 1);
add ("Plainfield":21, 1, 0, 0, 1, 1);
add ("Osceola":21, 1, 0, 0, 1, 1);
add ("Mulberry":21, 1, 0, 0, 1, 1);
add ("Millwood":21, 1, 0, 0, 1, 1);
add ("McDonald":21, 1, 0, 0, 1, 1);
add ("Maysville":21, 1, 0, 0, 1, 1);
add ("Mason":21, 1, 0, 0, 1, 1);
add ("Marysville":21, 1, 0, 0, 1, 1);
add ("Mansfield":21, 1, 0, 0, 1, 1);
add ("Lodi":21, 1, 0, 0, 1, 1);
add ("Livingston":21, 1, 0, 0, 1, 1);
add ("Kirkwood":21, 1, 0, 0, 1, 1);
add ("Kent":21, 1, 0, 0, 1, 1);
add ("Ingleside":21, 1, 0, 0, 1, 1);
add ("Hyde Park":21, 1, 0, 0, 1, 1);
add ("Homer":21, 1, 0, 0, 1, 1);
add ("Hickory Hills":21, 1, 0, 0, 1, 1);
add ("Hawthorne":21, 1, 0, 0, 1, 1);
add ("Hartford":21, 1, 0, 0, 1, 1);
add ("Fredonia":21, 1, 0, 0, 1, 1);
add ("Evansville":21, 1, 0, 0, 1, 1);
add ("Etna":21, 1, 0, 0, 1, 1);
add ("Edgemont":21, 1, 0, 0, 1, 1);
add ("Dexter":21, 1, 0, 0, 1, 1);
add ("Crestwood":21, 1, 0, 0, 1, 1);
add ("Crescent":21, 1, 0, 0, 1, 1);
add ("Covington":21, 1, 0, 0, 1, 1);
add ("Country Club Estates":21, 1, 0, 0, 1, 1);
add ("Coleman":21, 1, 0, 0, 1, 1);
add ("Chelsea":21, 1, 0, 0, 1, 1);
add ("Chapman":21, 1, 0, 0, 1, 1);
add ("Cedarville":21, 1, 0, 0, 1, 1);
add ("Burton":21, 1, 0, 0, 1, 1);
add ("Bryant":21, 1, 0, 0, 1, 1);
add ("Browntown":21, 1, 0, 0, 1, 1);
add ("Beverly":21, 1, 0, 0, 1, 1);
add ("Beaver":21, 1, 0, 0, 1, 1);
add ("Baxter":21, 1, 0, 0, 1, 1);
add ("Barton":21, 1, 0, 0, 1, 1);
add ("Ashton":21, 1, 0, 0, 1, 1);
add ("Armstrong":21, 1, 0, 0, 1, 1);
add ("Afton":21, 1, 0, 0, 1, 1);
add ("Warsaw":20, 1, 0, 0, 1, 1);
add ("Viola":20, 1, 0, 0, 1, 1);
add ("Uniontown":20, 1, 0, 0, 1, 1);
add ("Twin Oaks":20, 1, 0, 0, 1, 1);
add ("Sweetwater":20, 1, 0, 0, 1, 1);
add ("Stonewall":20, 1, 0, 0, 1, 1);
add ("Spring Lake":20, 1, 0, 0, 1, 1);
add ("Sparta":20, 1, 0, 0, 1, 1);
add ("Ruby":20, 1, 0, 0, 1, 1);
add ("Roseland":20, 1, 0, 0, 1, 1);
add ("Pleasantville":20, 1, 0, 0, 1, 1);
add ("Pittsburg":20, 1, 0, 0, 1, 1);
add ("Pioneer":20, 1, 0, 0, 1, 1);
add ("Pineville":20, 1, 0, 0, 1, 1);
add ("Oak Forest":20, 1, 0, 0, 1, 1);
add ("Northfield":20, 1, 0, 0, 1, 1);
add ("Mount Airy":20, 1, 0, 0, 1, 1);
add ("Maxwell":20, 1, 0, 0, 1, 1);
add ("Marietta":20, 1, 0, 0, 1, 1);
add ("Lamont":20, 1, 0, 0, 1, 1);
add ("Lake View":20, 1, 0, 0, 1, 1);
add ("Knoxville":20, 1, 0, 0, 1, 1);
add ("Idlewild":20, 1, 0, 0, 1, 1);
add ("Holt":20, 1, 0, 0, 1, 1);
add ("Hastings":20, 1, 0, 0, 1, 1);
add ("Hancock":20, 1, 0, 0, 1, 1);
add ("Gilbert":20, 1, 0, 0, 1, 1);
add ("Gardner":20, 1, 0, 0, 1, 1);
add ("Freedom":20, 1, 0, 0, 1, 1);
add ("Emerson":20, 1, 0, 0, 1, 1);
add ("Echo":20, 1, 0, 0, 1, 1);
add ("Dixon":20, 1, 0, 0, 1, 1);
add ("Denver":20, 1, 0, 0, 1, 1);
add ("Denton":20, 1, 0, 0, 1, 1);
add ("Curtis":20, 1, 0, 0, 1, 1);
add ("Creston":20, 1, 0, 0, 1, 1);
add ("Clark":20, 1, 0, 0, 1, 1);
add ("Chesterfield":20, 1, 0, 0, 1, 1);
add ("Cedar Point":20, 1, 0, 0, 1, 1);
add ("Cedar Creek":20, 1, 0, 0, 1, 1);
add ("Cascade":20, 1, 0, 0, 1, 1);
add ("Brandon":20, 1, 0, 0, 1, 1);
add ("Blaine":20, 1, 0, 0, 1, 1);
add ("Bancroft":20, 1, 0, 0, 1, 1);
add ("Avalon":20, 1, 0, 0, 1, 1);
add ("Atwood":20, 1, 0, 0, 1, 1);
add ("Alma":20, 1, 0, 0, 1, 1);
add ("Wheatland":19, 1, 0, 0, 1, 1);
add ("Wells":19, 1, 0, 0, 1, 1);
add ("Sun Valley":19, 1, 0, 0, 1, 1);
add ("State Line":19, 1, 0, 0, 1, 1);
add ("Silver City":19, 1, 0, 0, 1, 1);
add ("Seneca":19, 1, 0, 0, 1, 1);
add ("Selma":19, 1, 0, 0, 1, 1);
add ("Saint Joseph":19, 1, 0, 0, 1, 1);
add ("Saint John":19, 1, 0, 0, 1, 1);
add ("Ross":19, 1, 0, 0, 1, 1);
add ("Roberts":19, 1, 0, 0, 1, 1);
add ("Reynolds":19, 1, 0, 0, 1, 1);
add ("Red Rock":19, 1, 0, 0, 1, 1);
add ("Ogden":19, 1, 0, 0, 1, 1);
add ("Newark":19, 1, 0, 0, 1, 1);
add ("New London":19, 1, 0, 0, 1, 1);
add ("Mineral Springs":19, 1, 0, 0, 1, 1);
add ("Meridian":19, 1, 0, 0, 1, 1);
add ("Lynn":19, 1, 0, 0, 1, 1);
add ("Lisbon":19, 1, 0, 0, 1, 1);
add ("Lamar":19, 1, 0, 0, 1, 1);
add ("Knollwood":19, 1, 0, 0, 1, 1);
add ("Kensington":19, 1, 0, 0, 1, 1);
add ("Horton":19, 1, 0, 0, 1, 1);
add ("Homewood":19, 1, 0, 0, 1, 1);
add ("Homestead":19, 1, 0, 0, 1, 1);
add ("Holiday Hills":19, 1, 0, 0, 1, 1);
add ("Henry":19, 1, 0, 0, 1, 1);
add ("Harper":19, 1, 0, 0, 1, 1);
add ("Greenbriar":19, 1, 0, 0, 1, 1);
add ("Granville":19, 1, 0, 0, 1, 1);
add ("Graham":19, 1, 0, 0, 1, 1);
add ("Grafton":19, 1, 0, 0, 1, 1);
add ("Genoa":19, 1, 0, 0, 1, 1);
add ("Fruitland":19, 1, 0, 0, 1, 1);
add ("Fremont":19, 1, 0, 0, 1, 1);
add ("Forest Hill":19, 1, 0, 0, 1, 1);
add ("Forest Grove":19, 1, 0, 0, 1, 1);
add ("Folsom":19, 1, 0, 0, 1, 1);
add ("Flint Hill":19, 1, 0, 0, 1, 1);
add ("Fillmore":19, 1, 0, 0, 1, 1);
add ("Ferndale":19, 1, 0, 0, 1, 1);
add ("Fayette":19, 1, 0, 0, 1, 1);
add ("Fairmont":19, 1, 0, 0, 1, 1);
add ("Eastwood":19, 1, 0, 0, 1, 1);
add ("Dudley":19, 1, 0, 0, 1, 1);
add ("Dublin":19, 1, 0, 0, 1, 1);
add ("Dogtown":19, 1, 0, 0, 1, 1);
add ("Dawson":19, 1, 0, 0, 1, 1);
add ("Cunningham":19, 1, 0, 0, 1, 1);
add ("Conway":19, 1, 0, 0, 1, 1);
add ("Collins":19, 1, 0, 0, 1, 1);
add ("College Park":19, 1, 0, 0, 1, 1);
add ("Chestnut Grove":19, 1, 0, 0, 1, 1);
add ("Cherry Grove":19, 1, 0, 0, 1, 1);
add ("Cherokee":19, 1, 0, 0, 1, 1);
add ("Chatham":19, 1, 0, 0, 1, 1);
add ("Camelot":19, 1, 0, 0, 1, 1);
add ("Brooks":19, 1, 0, 0, 1, 1);
add ("Big Springs":19, 1, 0, 0, 1, 1);
add ("Big Creek":19, 1, 0, 0, 1, 1);
add ("Benson":19, 1, 0, 0, 1, 1);
add ("Bedford":19, 1, 0, 0, 1, 1);
add ("Bartlett":19, 1, 0, 0, 1, 1);
add ("Bailey":19, 1, 0, 0, 1, 1);
add ("Atlanta":19, 1, 0, 0, 1, 1);
add ("Alpha":19, 1, 0, 0, 1, 1);
add ("Woodland Hills":18, 1, 0, 0, 1, 1);
add ("Winfield":18, 1, 0, 0, 1, 1);
add ("Willow Springs":18, 1, 0, 0, 1, 1);
add ("Willow Grove":18, 1, 0, 0, 1, 1);
add ("Williamstown":18, 1, 0, 0, 1, 1);
add ("Wheeler":18, 1, 0, 0, 1, 1);
add ("Westchester":18, 1, 0, 0, 1, 1);
add ("Walton":18, 1, 0, 0, 1, 1);
add ("Tyler":18, 1, 0, 0, 1, 1);
add ("Taylorsville":18, 1, 0, 0, 1, 1);
add ("Stillwater":18, 1, 0, 0, 1, 1);
add ("Shawnee":18, 1, 0, 0, 1, 1);
add ("Shamrock":18, 1, 0, 0, 1, 1);
add ("Scott":18, 1, 0, 0, 1, 1);
add ("Sanford":18, 1, 0, 0, 1, 1);
add ("Saint Charles":18, 1, 0, 0, 1, 1);
add ("Rocky Hill":18, 1, 0, 0, 1, 1);
add ("Rockford":18, 1, 0, 0, 1, 1);
add ("Ripley":18, 1, 0, 0, 1, 1);
add ("Pulaski":18, 1, 0, 0, 1, 1);
add ("Phillips":18, 1, 0, 0, 1, 1);
add ("Pearl":18, 1, 0, 0, 1, 1);
add ("New Market":18, 1, 0, 0, 1, 1);
add ("New Boston":18, 1, 0, 0, 1, 1);
add ("Nebo":18, 1, 0, 0, 1, 1);
add ("Mount Tabor":18, 1, 0, 0, 1, 1);
add ("Morton":18, 1, 0, 0, 1, 1);
add ("Moore":18, 1, 0, 0, 1, 1);
add ("Long Branch":18, 1, 0, 0, 1, 1);
add ("London":18, 1, 0, 0, 1, 1);
add ("Lewisville":18, 1, 0, 0, 1, 1);
add ("Lewiston":18, 1, 0, 0, 1, 1);
add ("Jerusalem":18, 1, 0, 0, 1, 1);
add ("Jasper":18, 1, 0, 0, 1, 1);
add ("Hadley":18, 1, 0, 0, 1, 1);
add ("Grove":18, 1, 0, 0, 1, 1);
add ("Greenbrier":18, 1, 0, 0, 1, 1);
add ("Gray":18, 1, 0, 0, 1, 1);
add ("Glasgow":18, 1, 0, 0, 1, 1);
add ("Fletcher":18, 1, 0, 0, 1, 1);
add ("Fair Oaks":18, 1, 0, 0, 1, 1);
add ("Essex":18, 1, 0, 0, 1, 1);
add ("Elwood":18, 1, 0, 0, 1, 1);
add ("Eldorado":18, 1, 0, 0, 1, 1);
add ("Dunlap":18, 1, 0, 0, 1, 1);
add ("Dunbar":18, 1, 0, 0, 1, 1);
add ("Decatur":18, 1, 0, 0, 1, 1);
add ("Darlington":18, 1, 0, 0, 1, 1);
add ("Damascus":18, 1, 0, 0, 1, 1);
add ("Crystal Springs":18, 1, 0, 0, 1, 1);
add ("Crestview":18, 1, 0, 0, 1, 1);
add ("Coldwater":18, 1, 0, 0, 1, 1);
add ("Climax":18, 1, 0, 0, 1, 1);
add ("Center Hill":18, 1, 0, 0, 1, 1);
add ("Carrollton":18, 1, 0, 0, 1, 1);
add ("Carlton":18, 1, 0, 0, 1, 1);
add ("Caldwell":18, 1, 0, 0, 1, 1);
add ("Byron":18, 1, 0, 0, 1, 1);
add ("Browns Corner":18, 1, 0, 0, 1, 1);
add ("Bridgewater":18, 1, 0, 0, 1, 1);
add ("Bolton":18, 1, 0, 0, 1, 1);
add ("Blue Springs":18, 1, 0, 1, 0, 30);
add ("Bloomingdale":18, 1, 0, 1, 0, 30);
add ("Blair":18, 1, 0, 1, 0, 30);
add ("Bethesda":18, 1, 0, 1, 0, 30);
add ("Belleville":18, 1, 0, 1, 0, 30);
add ("Bear Creek":18, 1, 0, 1, 0, 30);
add ("Bayview":18, 1, 0, 1, 0, 30);
add ("Avoca":18, 1, 0, 1, 0, 30);
add ("Argyle":18, 1, 0, 1, 0, 30);
add ("Wyoming":17, 1, 0, 1, 0, 30);
add ("Woodland Park":17, 1, 0, 1, 0, 30);
add ("Wilton":17, 1, 0, 1, 0, 30);
add ("White Hall":17, 1, 0, 1, 0, 30);
add ("Watkins":17, 1, 0, 1, 0, 30);
add ("Warwick":17, 1, 0, 1, 0, 30);
add ("Walnut":17, 1, 0, 1, 0, 30);
add ("Vienna":17, 1, 0, 1, 0, 30);
add ("Union City":17, 1, 0, 1, 0, 30);
add ("Texas":17, 1, 0, 1, 0, 30);
add ("Sumner":17, 1, 0, 1, 0, 30);
add ("Summerfield":17, 1, 0, 1, 0, 30);
add ("Sugar Hill":17, 1, 0, 1, 0, 30);
add ("Stratford":17, 1, 0, 1, 0, 30);
add ("Springtown":17, 1, 0, 1, 0, 30);
add ("Saratoga":17, 1, 0, 1, 0, 30);
add ("San Jose":17, 1, 0, 1, 0, 30);
add ("Saint Johns":17, 1, 0, 1, 0, 30);
add ("Royal":17, 1, 0, 1, 0, 30);
add ("Rosebud":17, 1, 0, 1, 0, 30);
add ("Rockwood":17, 1, 0, 1, 0, 30);
add ("Riley":17, 1, 0, 1, 0, 30);
add ("Red Oak":17, 1, 0, 1, 0, 30);
add ("Quincy":17, 1, 0, 1, 0, 30);
add ("Pomona":17, 1, 0, 1, 0, 30);
add ("Point Pleasant":17, 1, 0, 1, 0, 30);
add ("Philadelphia":17, 1, 0, 1, 0, 30);
add ("Peoria":17, 1, 0, 1, 0, 30);
add ("Murray":17, 1, 0, 1, 0, 30);
add ("Millbrook":17, 1, 0, 1, 0, 30);
add ("Maywood":17, 1, 0, 1, 0, 30);
add ("Macon":17, 1, 0, 1, 0, 30);
add ("Lone Oak":17, 1, 0, 1, 0, 30);
add ("Lewis":17, 1, 0, 1, 0, 30);
add ("Leon":17, 1, 0, 1, 0, 30);
add ("Lee":17, 1, 0, 1, 0, 30);
add ("Jones":17, 1, 0, 1, 0, 30);
add ("Hubbard":17, 1, 0, 1, 0, 30);
add ("Gum Springs":17, 1, 0, 1, 0, 30);
add ("Guilford":17, 1, 0, 1, 0, 30);
add ("Galena":17, 1, 0, 1, 0, 30);
add ("Frenchtown":17, 1, 0, 1, 0, 30);
add ("Freeport":17, 1, 0, 1, 0, 30);
add ("Frankfort":17, 1, 0, 1, 0, 30);
add ("Fowler":17, 1, 0, 1, 0, 30);
add ("Floyd":17, 1, 0, 1, 0, 30);
add ("Fisher":17, 1, 0, 1, 0, 30);
add ("Fairbanks":17, 1, 0, 1, 0, 30);
add ("Evans":17, 1, 0, 1, 0, 30);
add ("Empire":17, 1, 0, 1, 0, 30);
add ("Elm Grove":17, 1, 0, 1, 0, 30);
add ("Ellsworth":17, 1, 0, 1, 0, 30);
add ("Edgewater":17, 1, 0, 1, 0, 30);
add ("Dewey":17, 1, 0, 1, 0, 30);
add ("Derby":17, 1, 0, 1, 0, 30);
add ("Denmark":17, 1, 0, 1, 0, 30);
add ("Cordova":17, 1, 0, 1, 0, 30);
add ("Colonial Heights":17, 1, 0, 1, 0, 30);
add ("Colfax":17, 1, 0, 1, 0, 30);
add ("Clearview":17, 1, 0, 1, 0, 30);
add ("Chestnut Ridge":17, 1, 0, 1, 0, 30);
add ("Carthage":17, 1, 0, 1, 0, 30);
add ("Carpenter":17, 1, 0, 1, 0, 30);
add ("Calhoun":17, 1, 0, 1, 0, 30);
add ("Brookwood":17, 1, 0, 1, 0, 30);
add ("Brookville":17, 1, 0, 1, 0, 30);
add ("Brentwood":17, 1, 0, 1, 0, 30);
add ("Birmingham":17, 1, 0, 1, 0, 30);
add ("Arcola":17, 1, 0, 1, 0, 30);
add ("Andover":17, 1, 0, 1, 0, 30);
add ("Aberdeen":17, 1, 0, 1, 0, 30);
add ("Yorktown":16, 1, 0, 1, 0, 30);
add ("Wright":16, 1, 0, 1, 0, 30);
add ("Woodrow":16, 1, 0, 1, 0, 30);
add ("Woodbury":16, 1, 0, 1, 0, 30);
add ("Winslow":16, 1, 0, 1, 0, 30);
add ("Whitney":16, 1, 0, 1, 0, 30);
add ("Whispering Pines":16, 1, 0, 1, 0, 30);
add ("Welcome":16, 1, 0, 1, 0, 30);
add ("Webb":16, 1, 0, 1, 0, 30);
add ("Washington Heights":16, 1, 0, 1, 0, 30);
add ("Summerville":16, 1, 0, 1, 0, 30);
add ("Sullivan":16, 1, 0, 1, 0, 30);
add ("Stewart":16, 1, 0, 1, 0, 30);
add ("Spring Grove":16, 1, 0, 1, 0, 30);
add ("Silver Springs":16, 1, 0, 1, 0, 30);
add ("Sheffield":16, 1, 0, 1, 0, 30);
add ("Shannon":16, 1, 0, 1, 0, 30);
add ("Scottsville":16, 1, 0, 1, 0, 30);
add ("Saint George":16, 1, 0, 1, 0, 30);
add ("Ryan":16, 1, 0, 1, 0, 30);
add ("Ruth":16, 1, 0, 1, 0, 30);
add ("Roy":16, 1, 0, 1, 0, 30);
add ("Roxbury":16, 1, 0, 1, 0, 30);
add ("Rosewood":16, 1, 0, 1, 0, 30);
add ("Roscoe":16, 1, 0, 1, 0, 30);
add ("Rocky Point":16, 1, 0, 1, 0, 30);
add ("Richfield":16, 1, 0, 1, 0, 30);
add ("Richardson":16, 1, 0, 1, 0, 30);
add ("Proctor":16, 1, 0, 1, 0, 30);
add ("Pinecrest":16, 1, 0, 1, 0, 30);
add ("Pine Valley":16, 1, 0, 1, 0, 30);
add ("Pierce":16, 1, 0, 1, 0, 30);
add ("Perkins":16, 1, 0, 1, 0, 30);
add ("Paxton":16, 1, 0, 1, 0, 30);
add ("Omega":16, 1, 0, 1, 0, 30);
add ("Nottingham":16, 1, 0, 1, 0, 30);
add ("Montezuma":16, 1, 0, 1, 0, 30);
add ("Montague":16, 1, 0, 1, 0, 30);
add ("Milo":16, 1, 0, 1, 0, 30);
add ("Milan":16, 1, 0, 1, 0, 30);
add ("Martinsville":16, 1, 0, 1, 0, 30);
add ("Maple Hill":16, 1, 0, 1, 0, 30);
add ("Ludlow":16, 1, 0, 1, 0, 30);
add ("Louisville":16, 1, 0, 1, 0, 30);
add ("Longwood":16, 1, 0, 1, 0, 30);
add ("Lewisburg":16, 1, 0, 1, 0, 30);
add ("Lenox":16, 1, 0, 1, 0, 30);
add ("Leesville":16, 1, 0, 1, 0, 30);
add ("Leesburg":16, 1, 0, 1, 0, 30);
add ("Lawrenceville":16, 1, 0, 1, 0, 30);
add ("Kirkland":16, 1, 0, 1, 0, 30);
add ("Kelly":16, 1, 0, 1, 0, 30);
add ("Jerome":16, 1, 0, 1, 0, 30);
add ("Jenkins":16, 1, 0, 1, 0, 30);
add ("Indian Village":16, 1, 0, 1, 0, 30);
add ("Hurricane":16, 1, 0, 1, 0, 30);
add ("Howell":16, 1, 0, 1, 0, 30);
add ("Hillcrest":16, 1, 0, 1, 0, 30);
add ("Hidden Valley":16, 1, 0, 1, 0, 30);
add ("Harvey":16, 1, 0, 1, 0, 30);
add ("Harmon":16, 1, 0, 1, 0, 30);
add ("Greendale":16, 1, 0, 1, 0, 30);
add ("Granite":16, 1, 0, 1, 0, 30);
add ("Glenville":16, 1, 0, 1, 0, 30);
add ("Gladstone":16, 1, 0, 1, 0, 30);
add ("Gilmore":16, 1, 0, 1, 0, 30);
add ("Garrison":16, 1, 0, 1, 0, 30);
add ("Freeman":16, 1, 0, 1, 0, 30);
add ("Fox":16, 1, 0, 1, 0, 30);
add ("Forestville":16, 1, 0, 1, 0, 30);
add ("Flatwoods":16, 1, 0, 1, 0, 30);
add ("Elkton":16, 1, 0, 1, 0, 30);
add ("Elizabeth":16, 1, 0, 1, 0, 30);
add ("Elba":16, 1, 0, 1, 0, 30);
add ("Cumberland":16, 1, 0, 1, 0, 30);
add ("Clearwater":16, 1, 0, 1, 0, 30);
add ("Carter":16, 1, 0, 1, 0, 30);
add ("California":16, 1, 0, 1, 0, 30);
add ("Caledonia":16, 1, 0, 1, 0, 30);
add ("Burns":16, 1, 0, 1, 0, 30);
add ("Buckingham":16, 1, 0, 1, 0, 30);
add ("Brunswick":16, 1, 0, 1, 0, 30);
add ("Bennett":16, 1, 0, 1, 0, 30);
add ("Bay View":16, 1, 0, 1, 0, 30);
add ("Barnes":16, 1, 0, 1, 0, 30);
add ("Arthur":16, 1, 0, 1, 0, 30);
add ("Appleton":16, 1, 0, 1, 0, 30);
add ("Amherst":16, 1, 0, 1, 0, 30);
add ("Allison":16, 1, 0, 1, 0, 30);
add ("Allentown":16, 1, 0, 1, 0, 30);
add ("Acme":16, 1, 0, 1, 0, 30);
add ("Woodbine":15, 1, 0, 1, 0, 30);
add ("Wolf Creek":15, 1, 0, 1, 0, 30);
add ("Williamsville":15, 1, 0, 1, 0, 30);
add ("White Plains":15, 1, 0, 1, 0, 30);
add ("Wesley":15, 1, 0, 1, 0, 30);
add ("Weldon":15, 1, 0, 1, 0, 30);
add ("Wayland":15, 1, 0, 1, 0, 30);
add ("Tyrone":15, 1, 0, 1, 0, 30);
add ("Tremont":15, 1, 0, 1, 0, 30);
add ("Tracy":15, 1, 0, 1, 0, 30);
add ("Tipton":15, 1, 0, 1, 0, 30);
add ("Thompsonville":15, 1, 0, 1, 0, 30);
add ("Tanglewood":15, 1, 0, 1, 0, 30);
add ("Tabor":15, 1, 0, 1, 0, 30);
add ("Sutton":15, 1, 0, 1, 0, 30);
add ("Superior":15, 1, 0, 1, 0, 30);
add ("Star":15, 1, 0, 1, 0, 30);
add ("Stafford":15, 1, 0, 1, 0, 30);
add ("Sleepy Hollow":15, 1, 0, 1, 0, 30);
add ("Siloam":15, 1, 0, 1, 0, 30);
add ("Shelby":15, 1, 0, 1, 0, 30);
add ("Shaw":15, 1, 0, 1, 0, 30);
add ("Sawyer":15, 1, 0, 1, 0, 30);
add ("Saint James":15, 1, 0, 1, 0, 30);
add ("Saint Clair":15, 1, 0, 1, 0, 30);
add ("Red Bank":15, 1, 0, 1, 0, 30);
add ("Rankin":15, 1, 0, 1, 0, 30);
add ("Price":15, 1, 0, 1, 0, 30);
add ("Peru":15, 1, 0, 1, 0, 30);
add ("Page":15, 1, 0, 1, 0, 30);
add ("Owens":15, 1, 0, 1, 0, 30);
add ("Oneida":15, 1, 0, 1, 0, 30);
add ("Northwood":15, 1, 0, 1, 0, 30);
add ("Nichols":15, 1, 0, 1, 0, 30);
add ("New Town":15, 1, 0, 1, 0, 30);
add ("Murphy":15, 1, 0, 1, 0, 30);
add ("Morris":15, 1, 0, 1, 0, 30);
add ("Morgantown":15, 1, 0, 1, 0, 30);
add ("Montpelier":15, 1, 0, 1, 0, 30);
add ("Mechanicsburg":15, 1, 0, 1, 0, 30);
add ("Lucas":15, 1, 0, 1, 0, 30);
add ("Lone Pine":15, 1, 0, 1, 0, 30);
add ("Littleton":15, 1, 0, 1, 0, 30);
add ("Little River":15, 1, 0, 1, 0, 30);
add ("Leland":15, 1, 0, 1, 0, 30);
add ("Langdon":15, 1, 0, 1, 0, 30);
add ("Lakeville":15, 1, 0, 1, 0, 30);
add ("Lake Forest":15, 1, 0, 1, 0, 30);
add ("La Grange":15, 1, 0, 1, 0, 30);
add ("King":15, 1, 0, 1, 0, 30);
add ("Kimball":15, 1, 0, 1, 0, 30);
add ("Johnsonville":15, 1, 0, 1, 0, 30);
add ("Highland Park":15, 1, 0, 1, 0, 30);
add ("Hazelwood":15, 1, 0, 1, 0, 30);
add ("Hartland":15, 1, 0, 1, 0, 30);
add ("Hardy":15, 1, 0, 1, 0, 30);
add ("Guthrie":15, 1, 0, 1, 0, 30);
add ("Griffin":15, 1, 0, 1, 0, 30);
add ("Gravel Hill":15, 1, 0, 1, 0, 30);
add ("Golden":15, 1, 0, 1, 0, 30);
add ("Globe":15, 1, 0, 1, 0, 30);
add ("Gary":15, 1, 0, 1, 0, 30);
add ("Frogtown":15, 1, 0, 1, 0, 30);
add ("Four Points":15, 1, 0, 1, 0, 30);
add ("Forest":15, 1, 0, 1, 0, 30);
add ("Flint":15, 1, 0, 1, 0, 30);
add ("Ferguson":15, 1, 0, 1, 0, 30);
add ("Fayetteville":15, 1, 0, 1, 0, 30);
add ("Farmersville":15, 1, 0, 1, 0, 30);
add ("Ellisville":15, 1, 0, 1, 0, 30);
add ("Edwards":15, 1, 0, 1, 0, 30);
add ("Edgewood":15, 1, 0, 1, 0, 30);
add ("Doyle":15, 1, 0, 1, 0, 30);
add ("Delmar":15, 1, 0, 1, 0, 30);
add ("Dallas":15, 1, 0, 1, 0, 30);
add ("Crystal":15, 1, 0, 1, 0, 30);
add ("Collinsville":15, 1, 0, 1, 0, 30);
add ("Clifford":15, 1, 0, 1, 0, 30);
add ("Church Hill":15, 1, 0, 1, 0, 30);
add ("Cherry Valley":15, 1, 0, 1, 0, 30);
add ("Cedar":15, 1, 0, 1, 0, 30);
add ("Brookwood":15, 1, 0, 1, 0, 30);
add ("Blanchard":15, 1, 0, 1, 0, 30);
add ("Berea":15, 1, 0, 1, 0, 30);
add ("Belleview":15, 1, 0, 1, 0, 30);
add ("Belfast":15, 1, 0, 1, 0, 30);
add ("Bayside":15, 1, 0, 1, 0, 30);
add ("Bath":15, 1, 0, 1, 0, 30);
add ("Avery":15, 1, 0, 1, 0, 30);
add ("Ashley":15, 1, 0, 1, 0, 30);
add ("Amity":15, 1, 0, 1, 0, 30);
add ("Altamont":15, 1, 0, 1, 0, 30);
add ("Adrian":15, 1, 0, 1, 0, 30);
add ("Youngstown":14, 1, 0, 1, 0, 30);
add ("Woodruff":14, 1, 0, 1, 0, 30);
add ("Woodcrest":14, 1, 0, 1, 0, 30);
add ("Willow":14, 1, 0, 1, 0, 30);
add ("Willis":14, 1, 0, 1, 0, 30);
add ("Wildwood":14, 1, 0, 1, 0, 30);
add ("Whitesville":14, 1, 0, 1, 0, 30);
add ("Westminster":14, 1, 0, 1, 0, 30);
add ("Westgate":14, 1, 0, 1, 0, 30);
add ("West Liberty":14, 1, 0, 1, 0, 30);
add ("Vista":14, 1, 0, 1, 0, 30);
add ("Vance":14, 1, 0, 1, 0, 30);
add ("The Meadows":14, 1, 0, 1, 0, 30);
add ("Taft":14, 1, 0, 1, 0, 30);
add ("Springhill":14, 1, 0, 1, 0, 30);
add ("Somerville":14, 1, 0, 1, 0, 30);
add ("Snug Harbor":14, 1, 0, 1, 0, 30);
add ("Smith":14, 1, 0, 1, 0, 30);
add ("Silver Creek":14, 1, 0, 1, 0, 30);
add ("Shore Acres":14, 1, 0, 1, 0, 30);
add ("Rutland":14, 1, 0, 1, 0, 30);
add ("Rossville":14, 1, 0, 1, 0, 30);
add ("Rolling Hills":14, 1, 0, 1, 0, 30);
add ("River Oaks":14, 1, 0, 1, 0, 30);
add ("Ridgeville":14, 1, 0, 1, 0, 30);
add ("Richville":14, 1, 0, 1, 0, 30);
add ("Riceville":14, 1, 0, 1, 0, 30);
add ("Reno":14, 1, 0, 1, 0, 30);
add ("Redland":14, 1, 0, 1, 0, 30);
add ("Prosperity":14, 1, 0, 1, 0, 30);
add ("Post Oak":14, 1, 0, 1, 0, 30);
add ("Plainville":14, 1, 0, 1, 0, 30);
add ("Pinhook":14, 1, 0, 1, 0, 30);
add ("Phoenix":14, 1, 0, 1, 0, 30);
add ("Payne":14, 1, 0, 1, 0, 30);
add ("Parkwood":14, 1, 0, 1, 0, 30);

