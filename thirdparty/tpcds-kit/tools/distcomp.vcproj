<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8.00"
	Name="distcomp"
	ProjectGUID="{851994E6-2458-4263-B651-E50BD4641E18}"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="$(SolutionDir)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="1"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName=".\./distcomp.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				PreprocessorDefinitions="_CRT_SECURE_NO_DEPRECATE;WIN32"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				PrecompiledHeaderFile=".\./junk/distcomp.pch"
				AssemblerListingLocation=".\./junk/"
				ObjectFile=".\./junk/"
				ProgramDataBaseFileName=".\./junk/"
				BrowseInformation="1"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="4"
				CompileAs="1"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="odbc32.lib odbccp32.lib ws2_32.lib"
				OutputFile="$(SolutionDir)$(ProjectName).exe"
				LinkIncremental="2"
				SuppressStartupBanner="true"
				GenerateDebugInformation="true"
				ProgramDatabaseFile=".\./distcomp.pdb"
				SubSystem="1"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\./distcomp.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCWebDeploymentTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="$(SolutionDir)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="1"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="./distcomp.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				PreprocessorDefinitions="_CRT_SECURE_NO_DEPRECATE;WIN32"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				PrecompiledHeaderFile=".\junk/distcomp.pch"
				AssemblerListingLocation=".\junk/"
				ObjectFile=".\junk/"
				ProgramDataBaseFileName=".\junk/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				CompileAs="1"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="ws2_32.lib"
				OutputFile="./distcomp.exe"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				ProgramDatabaseFile="./distcomp.pdb"
				SubSystem="1"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="./distcomp.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCWebDeploymentTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
			>
			<File
				RelativePath="date.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="dcgram.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="dcomp.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="decimal.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="dist.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="error_msg.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="genrand.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="grammar.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="r_params.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="StringBuffer.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl"
			>
			<File
				RelativePath="address.h"
				>
			</File>
			<File
				RelativePath="columns.h"
				>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCustomBuildTool"
						Description="Rebulding columns.h"
						CommandLine="mkheader column_list.txt"
						AdditionalDependencies="column_list.txt"
						Outputs="columns.h streams.h"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="config.h"
				>
			</File>
			<File
				RelativePath="constants.h"
				>
			</File>
			<File
				RelativePath="date.h"
				>
			</File>
			<File
				RelativePath="dcgram.h"
				>
			</File>
			<File
				RelativePath="dcomp.h"
				>
			</File>
			<File
				RelativePath="dcomp_params.h"
				>
			</File>
			<File
				RelativePath="decimal.h"
				>
			</File>
			<File
				RelativePath="dist.h"
				>
			</File>
			<File
				RelativePath="error_msg.h"
				>
			</File>
			<File
				RelativePath="eval.h"
				>
			</File>
			<File
				RelativePath="expr.h"
				>
			</File>
			<File
				RelativePath="genrand.h"
				>
			</File>
			<File
				RelativePath="grammar.h"
				>
			</File>
			<File
				RelativePath="grammar_support.h"
				>
			</File>
			<File
				RelativePath="list.h"
				>
			</File>
			<File
				RelativePath="mathops.h"
				>
			</File>
			<File
				RelativePath="params.h"
				>
			</File>
			<File
				RelativePath="porting.h"
				>
			</File>
			<File
				RelativePath="r_params.h"
				>
			</File>
			<File
				RelativePath="release.h"
				>
			</File>
			<File
				RelativePath="streams.h"
				>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCustomBuildTool"
						Description="remaking streams.h"
						CommandLine="mkheader column_list.txt"
						AdditionalDependencies="column_list.txt"
						Outputs="columns.h streams.h"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="StringBuffer.h"
				>
			</File>
			<File
				RelativePath="substitution.h"
				>
			</File>
			<File
				RelativePath="tables.h"
				>
			</File>
			<File
				RelativePath="tdefs.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe"
			>
		</Filter>
		<File
			RelativePath="column_list.txt"
			>
			<FileConfiguration
				Name="Debug|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					CommandLine="mkheader column_list.txt&#x0D;&#x0A;"
					Outputs="columns.h;streams.h"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="Release|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCCustomBuildTool"
				/>
			</FileConfiguration>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
