#!/bin/bash

# Simple verification script to check if the compilation fix is correct

echo "=== Compilation Fix Verification ==="
echo ""

echo "1. Checking genData method signature in main code..."
echo "Expected signature:"
echo "  def genData(location: String, format: String, overwrite: Boolean, iceberg: Boolean, partitionTables: <PERSON><PERSON><PERSON>, ...)"

# Extract the genData method signature from main code
echo ""
echo "Actual signature in TPCDSDatagen.scala:"
grep -A 10 "def genData(" src/main/scala/org/apache/spark/sql/execution/benchmark/TPCDSDatagen.scala | head -12

echo ""
echo "2. Checking genData method call in test code..."
echo "Expected call should include 'iceberg = false' parameter"

echo ""
echo "Actual call in TPCDSDatagenSuite.scala:"
grep -A 15 "tpcdsTables.genData(" src/test/scala/org/apache/spark/sql/execution/benchmark/TPCDSDatagenSuite.scala

echo ""
echo "3. Parameter count verification:"
echo "Main method parameters (Tables.genData):"
main_params=$(sed -n '237,248p' src/main/scala/org/apache/spark/sql/execution/benchmark/TPCDSDatagen.scala | grep -E "^\s*[a-zA-Z].*:" | wc -l)
echo "  Count: $main_params"

echo "Test call parameters:"
test_params=$(sed -n '30,41p' src/test/scala/org/apache/spark/sql/execution/benchmark/TPCDSDatagenSuite.scala | grep -E "^\s*[a-zA-Z].*=" | wc -l)
echo "  Count: $test_params"

echo ""
if [ "$main_params" -eq "$test_params" ]; then
    echo "✅ Parameter count matches! Compilation should succeed."
else
    echo "❌ Parameter count mismatch! Compilation may fail."
fi

echo ""
echo "4. Checking for iceberg parameter in test call..."
if grep -q "iceberg = false" src/test/scala/org/apache/spark/sql/execution/benchmark/TPCDSDatagenSuite.scala; then
    echo "✅ 'iceberg = false' parameter found in test call."
else
    echo "❌ 'iceberg = false' parameter missing in test call."
fi

echo ""
echo "=== Verification Complete ==="
