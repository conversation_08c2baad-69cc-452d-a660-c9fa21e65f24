# PB级 Iceberg Spark TPC-DS 基准测试指南

本指南介绍如何使用增强版TPC-DS基准测试来测试Apache Iceberg在PB级规模下的性能。

## 概述

项目已针对PB级测试增强了以下功能：

- **规模因子支持**: 支持10,000+规模因子（10TB+）
- **优化的Spark配置**: 针对大规模数据处理进行调优
- **Iceberg优化**: 针对PB级Iceberg表的增强设置
- **集群模式支持**: 自动配置YARN/Kubernetes集群
- **监控工具**: 内置性能监控和分析
- **自动化脚本**: 简化PB级数据生成和基准测试

## 前置条件

### 硬件要求（PB级）
- **集群**: 推荐50+节点
- **内存**: 每节点64GB+
- **存储**: 高性能分布式存储（HDFS、S3等）
- **网络**: 10Gbps+互联

### 软件要求
- Apache Spark 3.2.1+
- Apache Iceberg 1.4.2+
- Hadoop/YARN集群或Kubernetes
- Java 8或11

## 快速开始

### 1. 构建项目
```bash
mvn clean package -DskipTests
```

### 2. 配置环境
```bash
# 设置Spark路径
export SPARK_HOME=/path/to/spark

# 加载PB级配置
source conf/pb-scale-config.sh
```

### 3. 生成PB级数据
```bash
# 生成1TB数据（规模因子1000）
./bin/generate-pb-scale-data --scale-factor 1000 --output-location /data/tpcds-1tb

# 生成10TB数据（规模因子10000）- 完整PB级测试
./bin/generate-pb-scale-data --scale-factor 10000 --output-location /data/tpcds-10tb
```

### 4. 运行基准测试
```bash
# 在1TB数据上运行所有查询
./bin/run-pb-scale-benchmark --data-location /data/tpcds-1tb

# 运行特定查询并输出到文件
./bin/run-pb-scale-benchmark \
  --data-location /data/tpcds-10tb \
  --query-filter "q1,q3,q7,q19,q42" \
  --output-file results/pb-scale-results.log
```

## Iceberg Catalog配置

项目支持多种Iceberg catalog类型，提供灵活的元数据管理：

### 支持的Catalog类型

1. **Hadoop Catalog**（默认）- 基于文件的元数据存储
2. **Hive Catalog** - 使用Hive Metastore管理元数据

### Iceberg表格式配置

项目支持可配置的Iceberg表格式参数：

- **目标文件大小**: 控制数据文件大小（默认：1GB）
- **压缩编解码器**: 选择压缩算法（zstd、snappy、gzip、lz4）
- **分布模式**: 控制数据分布（hash、range、none）
- **向量化**: 启用/禁用向量化读取以提升性能
- **写入-审计-发布（WAP）**: 启用事务性写入

### 快速Catalog设置

```bash
# 加载catalog配置示例
source conf/iceberg-catalog-examples.sh

# 配置Hive catalog
configure_hive_catalog

# 配置Hadoop catalog
configure_hadoop_catalog

# 验证配置
./conf/iceberg-catalog-examples.sh validate hive
```

### Catalog特定示例

#### Hadoop Catalog（基于文件）
```bash
./bin/generate-pb-scale-data \
  --scale-factor 1000 \
  --iceberg-catalog-type hadoop \
  --iceberg-catalog-name my_hadoop_catalog \
  --warehouse-location /data/iceberg-warehouse
```

#### Hive Catalog（Hive Metastore）
```bash
./bin/generate-pb-scale-data \
  --scale-factor 1000 \
  --iceberg-catalog-type hive \
  --iceberg-catalog-name my_hive_catalog \
  --hive-metastore-uri thrift://hive-metastore:9083 \
  --warehouse-location /data/iceberg-warehouse
```

#### Iceberg表格式配置
```bash
# 配置表格式参数
./bin/generate-pb-scale-data \
  --scale-factor 1000 \
  --iceberg-target-file-size 2147483648 \
  --iceberg-compression-codec snappy \
  --iceberg-distribution-mode range \
  --disable-iceberg-vectorization \
  --enable-iceberg-wap
```

#### 环境变量配置
```bash
# 通过环境变量设置catalog配置
export ICEBERG_CATALOG_TYPE="hive"
export ICEBERG_CATALOG_NAME="production_catalog"
export HIVE_METASTORE_URI="thrift://hive-metastore:9083"
export HIVE_WAREHOUSE_DIR="/data/iceberg-warehouse"

# 设置表格式配置
export ICEBERG_TARGET_FILE_SIZE="2147483648"  # 2GB
export ICEBERG_COMPRESSION_CODEC="snappy"
export ICEBERG_DISTRIBUTION_MODE="hash"
export ICEBERG_VECTORIZATION_ENABLED="true"

# 然后运行时无需显式参数
./bin/generate-pb-scale-data --scale-factor 1000
```

## 详细配置

### 规模因子指南
- **规模因子 100**: ~100GB数据（测试/开发）
- **规模因子 1000**: ~1TB数据（中等规模）
- **规模因子 10000**: ~10TB数据（PB级测试）
- **规模因子 100000**: ~100TB数据（完整PB级）

### 存储配置

#### HDFS
```bash
export PB_DATA_LOCATION="hdfs://namenode:9000/data/tpcds-pb"
export PB_WAREHOUSE_LOCATION="hdfs://namenode:9000/warehouse/iceberg"
```

#### S3
```bash
export PB_DATA_LOCATION="s3a://your-bucket/tpcds-pb"
export PB_WAREHOUSE_LOCATION="s3a://your-bucket/iceberg-warehouse"

# 在conf/pb-scale-config.sh中的额外S3配置
```

#### Azure Data Lake
```bash
export PB_DATA_LOCATION="abfss://<EMAIL>/tpcds-pb"
export PB_WAREHOUSE_LOCATION="abfss://<EMAIL>/iceberg-warehouse"
```

### 集群配置

#### YARN集群
```bash
# 更新conf/pb-scale-config.sh
export SPARK_MASTER_URL="yarn"
export SPARK_DEPLOY_MODE="cluster"
export NUM_EXECUTORS="200"
export EXECUTOR_MEMORY="64g"
export EXECUTOR_CORES="16"
```

#### Kubernetes
```bash
# 更新conf/pb-scale-config.sh
export SPARK_MASTER_URL="k8s://https://kubernetes-api-server:443"
export SPARK_DEPLOY_MODE="cluster"
export NUM_EXECUTORS="100"
```

## 高级用法

### 自定义表生成
```bash
# 仅生成特定表
./bin/generate-pb-scale-data \
  --scale-factor 5000 \
  --table-filter "catalog_sales,store_sales,web_sales" \
  --num-partitions 5000
```

### 性能监控
```bash
# 在后台启动监控
./bin/monitor-pb-scale &

# 生成性能报告
./bin/monitor-pb-scale report

# 分析特定日志
./bin/monitor-pb-scale analyze "spark-*.log"
```

### 查询优化
```bash
# 禁用自适应查询执行以获得一致的基准测试
./bin/run-pb-scale-benchmark \
  --data-location /data/tpcds-10tb \
  --disable-cbo \
  --query-filter "q1,q2,q3"

# 带预热的多次基准测试运行
./bin/run-pb-scale-benchmark \
  --data-location /data/tpcds-10tb \
  --warmup-runs 2 \
  --benchmark-runs 5
```

## 性能调优技巧

### 内存优化
- 对于数据密集型工作负载设置`spark.executor.memoryFraction=0.8`
- 使用`spark.sql.adaptive.coalescePartitions.enabled=true`减少小文件
- 配置`spark.sql.adaptive.advisoryPartitionSizeInBytes=256MB`获得最佳分区大小

### I/O优化
- 使用`zstd`压缩获得更好的压缩比和速度
- 设置`spark.sql.files.maxPartitionBytes=2GB`处理大文件
- 启用`spark.sql.parquet.enableVectorizedReader=true`加快读取速度

### Iceberg特定调优
- 设置`write.target-file-size-bytes=1GB`获得最佳文件大小
- 使用`write.distribution-mode=hash`获得更好的数据分布
- 启用`vectorization.enabled=true`加快查询处理速度

## 故障排除

### 常见问题

#### 内存不足错误
```bash
# 增加executor内存
export EXECUTOR_MEMORY="128g"

# 减少分区大小
export SPARK_CONF="$SPARK_CONF --conf spark.sql.adaptive.advisoryPartitionSizeInBytes=128MB"
```

#### 数据生成缓慢
```bash
# 增加并行度
./bin/generate-pb-scale-data --num-partitions 10000

# 使用更多executor
export NUM_EXECUTORS="500"
```

#### 查询超时
```bash
# 增加网络超时时间
export SPARK_CONF="$SPARK_CONF --conf spark.network.timeout=1200s"

# 启用自适应查询执行
export SPARK_CONF="$SPARK_CONF --conf spark.sql.adaptive.enabled=true"
```

### 监控和调试

#### 检查Spark UI
- Driver UI: `http://driver-node:4040`
- History Server: `http://history-server:18080`

#### 监控系统资源
```bash
# CPU和内存
htop

# 磁盘I/O
iostat -x 1

# 网络
iftop
```

#### 分析日志
```bash
# 查找错误模式
grep -r "ERROR\|Exception" /path/to/spark/logs/

# 检查GC活动
grep -r "GC" /path/to/spark/logs/ | tail -20
```

## 最佳实践

1. **从小开始**: 在进行PB级测试之前，先用规模因子100-1000进行测试
2. **监控资源**: 使用监控脚本跟踪系统性能
3. **优化存储**: 使用具有足够带宽的高性能存储
4. **逐步调优**: 基于监控数据逐步调整配置
5. **验证结果**: 比较不同配置下的结果以确保一致性
6. **记录变更**: 跟踪配置变更及其影响

## 支持

如有问题和疑问：
1. 查看上述故障排除部分
2. 查阅Spark和Iceberg文档
3. 在执行期间监控系统资源
4. 分析应用程序日志以获取具体错误信息

## 性能预期

PB级基准测试的典型性能特征：

- **数据生成**: 根据集群大小，1-10 TB/小时
- **查询性能**: 因查询复杂度而显著不同
- **资源使用**: 活跃处理期间80-90%的CPU利用率
- **内存使用**: 分配的executor内存的60-80%

结果会根据硬件、网络、存储和具体配置而有所不同。
