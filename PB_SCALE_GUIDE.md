# PB级 Iceberg Spark TPC-DS 基准测试指南

本指南介绍如何使用增强版TPC-DS基准测试来测试Apache Iceberg在PB级规模下的性能。

## 概述

项目已针对PB级测试增强了以下功能：

- **规模因子支持**: 支持10,000+规模因子（10TB+）
- **优化的Spark配置**: 针对大规模数据处理进行调优
- **Iceberg优化**: 针对PB级Iceberg表的增强设置
- **集群模式支持**: 自动配置YARN/Kubernetes集群
- **监控工具**: 内置性能监控和分析
- **自动化脚本**: 简化PB级数据生成和基准测试

## 前置条件

### 硬件要求（PB级）
- **集群**: 推荐50+节点
- **内存**: 每节点64GB+
- **存储**: 高性能分布式存储（HDFS、S3等）
- **网络**: 10Gbps+互联

### 软件要求
- Apache Spark 3.2.1+
- Apache Iceberg 1.4.2+
- Hadoop/YARN集群或Kubernetes
- Java 8或11

## 快速开始

### 1. 构建项目
```bash
mvn clean package -DskipTests
```

### 2. 配置环境
```bash
# 设置Spark路径
export SPARK_HOME=/path/to/spark

# 加载PB级配置
source conf/pb-scale-config.sh
```

### 3. 生成PB级数据
```bash
# 生成1TB数据（规模因子1000）
./bin/generate-pb-scale-data --scale-factor 1000 --output-location /data/tpcds-1tb

# 生成10TB数据（规模因子10000）- 完整PB级测试
./bin/generate-pb-scale-data --scale-factor 10000 --output-location /data/tpcds-10tb
```

### 4. 运行基准测试
```bash
# 在1TB数据上运行所有查询
./bin/run-pb-scale-benchmark --data-location /data/tpcds-1tb

# 运行特定查询并输出到文件
./bin/run-pb-scale-benchmark \
  --data-location /data/tpcds-10tb \
  --query-filter "q1,q3,q7,q19,q42" \
  --output-file results/pb-scale-results.log
```

## Iceberg Catalog Configuration

The project supports multiple Iceberg catalog types for flexible metadata management:

### Supported Catalog Types

1. **Hadoop Catalog** (Default) - File-based metadata storage
2. **Hive Catalog** - Uses Hive Metastore for metadata

### Iceberg Table Format Configuration

The project supports configurable Iceberg table format parameters:

- **Target File Size**: Control the size of data files (default: 1GB)
- **Compression Codec**: Choose compression algorithm (zstd, snappy, gzip, lz4)
- **Distribution Mode**: Control data distribution (hash, range, none)
- **Vectorization**: Enable/disable vectorized reading for performance
- **Write-Audit-Publish (WAP)**: Enable transactional writes

### Quick Catalog Setup

```bash
# Load catalog configuration examples
source conf/iceberg-catalog-examples.sh

# Configure Hive catalog
configure_hive_catalog

# Configure Hadoop catalog
configure_hadoop_catalog

# Validate configuration
./conf/iceberg-catalog-examples.sh validate hive
```

### Catalog-Specific Examples

#### Hadoop Catalog (File-based)
```bash
./bin/generate-pb-scale-data \
  --scale-factor 1000 \
  --iceberg-catalog-type hadoop \
  --iceberg-catalog-name my_hadoop_catalog \
  --warehouse-location /data/iceberg-warehouse
```

#### Hive Catalog (Hive Metastore)
```bash
./bin/generate-pb-scale-data \
  --scale-factor 1000 \
  --iceberg-catalog-type hive \
  --iceberg-catalog-name my_hive_catalog \
  --hive-metastore-uri thrift://hive-metastore:9083 \
  --warehouse-location /data/iceberg-warehouse
```

#### Iceberg Table Format Configuration
```bash
# Configure table format parameters
./bin/generate-pb-scale-data \
  --scale-factor 1000 \
  --iceberg-target-file-size 2147483648 \
  --iceberg-compression-codec snappy \
  --iceberg-distribution-mode range \
  --disable-iceberg-vectorization \
  --enable-iceberg-wap
```

#### Environment Variable Configuration
```bash
# Set catalog configuration via environment variables
export ICEBERG_CATALOG_TYPE="hive"
export ICEBERG_CATALOG_NAME="production_catalog"
export HIVE_METASTORE_URI="thrift://hive-metastore:9083"
export HIVE_WAREHOUSE_DIR="/data/iceberg-warehouse"

# Set table format configuration
export ICEBERG_TARGET_FILE_SIZE="2147483648"  # 2GB
export ICEBERG_COMPRESSION_CODEC="snappy"
export ICEBERG_DISTRIBUTION_MODE="hash"
export ICEBERG_VECTORIZATION_ENABLED="true"

# Then run without explicit parameters
./bin/generate-pb-scale-data --scale-factor 1000
```

## Detailed Configuration

### Scale Factor Guidelines
- **Scale Factor 100**: ~100GB data (testing/development)
- **Scale Factor 1000**: ~1TB data (medium scale)
- **Scale Factor 10000**: ~10TB data (PB-scale testing)
- **Scale Factor 100000**: ~100TB data (full PB-scale)

### Storage Configuration

#### HDFS
```bash
export PB_DATA_LOCATION="hdfs://namenode:9000/data/tpcds-pb"
export PB_WAREHOUSE_LOCATION="hdfs://namenode:9000/warehouse/iceberg"
```

#### S3
```bash
export PB_DATA_LOCATION="s3a://your-bucket/tpcds-pb"
export PB_WAREHOUSE_LOCATION="s3a://your-bucket/iceberg-warehouse"

# Additional S3 configurations in conf/pb-scale-config.sh
```

#### Azure Data Lake
```bash
export PB_DATA_LOCATION="abfss://<EMAIL>/tpcds-pb"
export PB_WAREHOUSE_LOCATION="abfss://<EMAIL>/iceberg-warehouse"
```

### Cluster Configuration

#### YARN Cluster
```bash
# Update conf/pb-scale-config.sh
export SPARK_MASTER_URL="yarn"
export SPARK_DEPLOY_MODE="cluster"
export NUM_EXECUTORS="200"
export EXECUTOR_MEMORY="64g"
export EXECUTOR_CORES="16"
```

#### Kubernetes
```bash
# Update conf/pb-scale-config.sh
export SPARK_MASTER_URL="k8s://https://kubernetes-api-server:443"
export SPARK_DEPLOY_MODE="cluster"
export NUM_EXECUTORS="100"
```

## Advanced Usage

### Custom Table Generation
```bash
# Generate only specific tables
./bin/generate-pb-scale-data \
  --scale-factor 5000 \
  --table-filter "catalog_sales,store_sales,web_sales" \
  --num-partitions 5000
```

### Performance Monitoring
```bash
# Start monitoring in background
./bin/monitor-pb-scale &

# Generate performance report
./bin/monitor-pb-scale report

# Analyze specific logs
./bin/monitor-pb-scale analyze "spark-*.log"
```

### Query Optimization
```bash
# Disable adaptive query execution for consistent benchmarking
./bin/run-pb-scale-benchmark \
  --data-location /data/tpcds-10tb \
  --disable-cbo \
  --query-filter "q1,q2,q3"

# Multiple benchmark runs with warmup
./bin/run-pb-scale-benchmark \
  --data-location /data/tpcds-10tb \
  --warmup-runs 2 \
  --benchmark-runs 5
```

## Performance Tuning Tips

### Memory Optimization
- Set `spark.executor.memoryFraction=0.8` for data-intensive workloads
- Use `spark.sql.adaptive.coalescePartitions.enabled=true` to reduce small files
- Configure `spark.sql.adaptive.advisoryPartitionSizeInBytes=256MB` for optimal partition size

### I/O Optimization
- Use `zstd` compression for better compression ratio and speed
- Set `spark.sql.files.maxPartitionBytes=2GB` for large file processing
- Enable `spark.sql.parquet.enableVectorizedReader=true` for faster reads

### Iceberg-Specific Tuning
- Set `write.target-file-size-bytes=1GB` for optimal file sizes
- Use `write.distribution-mode=hash` for better data distribution
- Enable `vectorization.enabled=true` for faster query processing

## Troubleshooting

### Common Issues

#### Out of Memory Errors
```bash
# Increase executor memory
export EXECUTOR_MEMORY="128g"

# Reduce partition size
export SPARK_CONF="$SPARK_CONF --conf spark.sql.adaptive.advisoryPartitionSizeInBytes=128MB"
```

#### Slow Data Generation
```bash
# Increase parallelism
./bin/generate-pb-scale-data --num-partitions 10000

# Use more executors
export NUM_EXECUTORS="500"
```

#### Query Timeouts
```bash
# Increase network timeout
export SPARK_CONF="$SPARK_CONF --conf spark.network.timeout=1200s"

# Enable adaptive query execution
export SPARK_CONF="$SPARK_CONF --conf spark.sql.adaptive.enabled=true"
```

### Monitoring and Debugging

#### Check Spark UI
- Driver UI: `http://driver-node:4040`
- History Server: `http://history-server:18080`

#### Monitor System Resources
```bash
# CPU and memory
htop

# Disk I/O
iostat -x 1

# Network
iftop
```

#### Analyze Logs
```bash
# Find error patterns
grep -r "ERROR\|Exception" /path/to/spark/logs/

# Check GC activity
grep -r "GC" /path/to/spark/logs/ | tail -20
```

## Best Practices

1. **Start Small**: Test with scale factor 100-1000 before going to PB-scale
2. **Monitor Resources**: Use the monitoring script to track system performance
3. **Optimize Storage**: Use high-performance storage with adequate bandwidth
4. **Tune Gradually**: Adjust configurations incrementally based on monitoring data
5. **Validate Results**: Compare results across different configurations for consistency
6. **Document Changes**: Keep track of configuration changes and their impact

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review Spark and Iceberg documentation
3. Monitor system resources during execution
4. Analyze application logs for specific error messages

## Performance Expectations

Typical performance characteristics for PB-scale benchmarks:

- **Data Generation**: 1-10 TB/hour depending on cluster size
- **Query Performance**: Varies significantly by query complexity
- **Resource Usage**: 80-90% CPU utilization during active processing
- **Memory Usage**: 60-80% of allocated executor memory

Results will vary based on hardware, network, storage, and specific configurations.
