#!/usr/bin/env bash

#
# Monitoring script for PB-scale Iceberg Spark TPC-DS benchmark
# This script monitors system resources and Spark application metrics
#

# Configuration
MONITOR_INTERVAL=${MONITOR_INTERVAL:-30}  # seconds
OUTPUT_DIR=${OUTPUT_DIR:-"./monitoring"}
SPARK_HISTORY_SERVER=${SPARK_HISTORY_SERVER:-"http://localhost:18080"}
YARN_RESOURCE_MANAGER=${YARN_RESOURCE_MANAGER:-"http://localhost:8088"}

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Function to log with timestamp
log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to collect system metrics
collect_system_metrics() {
  local timestamp=$(date '+%Y-%m-%d_%H-%M-%S')
  local output_file="$OUTPUT_DIR/system_metrics_$timestamp.log"
  
  {
    echo "=== System Metrics at $(date) ==="
    echo "--- CPU Usage ---"
    top -bn1 | grep "Cpu(s)" || echo "CPU info not available"
    
    echo "--- Memory Usage ---"
    free -h || echo "Memory info not available"
    
    echo "--- Disk Usage ---"
    df -h || echo "Disk info not available"
    
    echo "--- Network Usage ---"
    if command -v iftop >/dev/null 2>&1; then
      timeout 5 iftop -t -s 5 2>/dev/null || echo "Network monitoring not available"
    else
      echo "iftop not available for network monitoring"
    fi
    
    echo "--- Load Average ---"
    uptime || echo "Load average not available"
    
    echo "--- Java Processes ---"
    jps -v 2>/dev/null | grep -E "(SparkSubmit|ApplicationMaster|CoarseGrainedExecutorBackend)" || echo "No Spark processes found"
    
    echo "=================================="
    echo ""
  } >> "$output_file"
  
  echo "$output_file"
}

# Function to collect Spark metrics
collect_spark_metrics() {
  local timestamp=$(date '+%Y-%m-%d_%H-%M-%S')
  local output_file="$OUTPUT_DIR/spark_metrics_$timestamp.log"
  
  {
    echo "=== Spark Metrics at $(date) ==="
    
    # Try to get application list from YARN
    if command -v yarn >/dev/null 2>&1; then
      echo "--- YARN Applications ---"
      yarn application -list -appStates RUNNING 2>/dev/null || echo "YARN not available or no running applications"
      echo ""
    fi
    
    # Try to get Spark application info
    echo "--- Spark Applications ---"
    if command -v curl >/dev/null 2>&1; then
      # Try Spark History Server
      curl -s "$SPARK_HISTORY_SERVER/api/v1/applications" 2>/dev/null | head -20 || echo "Spark History Server not accessible"
      echo ""
      
      # Try YARN Resource Manager
      curl -s "$YARN_RESOURCE_MANAGER/ws/v1/cluster/apps" 2>/dev/null | head -20 || echo "YARN Resource Manager not accessible"
      echo ""
    else
      echo "curl not available for API calls"
    fi
    
    echo "=================================="
    echo ""
  } >> "$output_file"
  
  echo "$output_file"
}

# Function to analyze log files
analyze_logs() {
  local log_pattern=${1:-"*.log"}
  local analysis_file="$OUTPUT_DIR/log_analysis_$(date '+%Y-%m-%d_%H-%M-%S').txt"
  
  {
    echo "=== Log Analysis at $(date) ==="
    echo "Pattern: $log_pattern"
    echo ""
    
    # Find recent log files
    echo "--- Recent Log Files ---"
    find . -name "$log_pattern" -mtime -1 -type f 2>/dev/null | head -10
    echo ""
    
    # Look for errors and warnings
    echo "--- Recent Errors ---"
    find . -name "$log_pattern" -mtime -1 -type f -exec grep -l "ERROR\|Exception\|Failed" {} \; 2>/dev/null | head -5 | while read file; do
      echo "File: $file"
      grep -n "ERROR\|Exception\|Failed" "$file" 2>/dev/null | tail -5
      echo ""
    done
    
    echo "--- Recent Warnings ---"
    find . -name "$log_pattern" -mtime -1 -type f -exec grep -l "WARN\|Warning" {} \; 2>/dev/null | head -5 | while read file; do
      echo "File: $file"
      grep -n "WARN\|Warning" "$file" 2>/dev/null | tail -3
      echo ""
    done
    
    echo "=================================="
  } >> "$analysis_file"
  
  echo "$analysis_file"
}

# Function to generate performance report
generate_report() {
  local report_file="$OUTPUT_DIR/performance_report_$(date '+%Y-%m-%d_%H-%M-%S').html"
  
  {
    cat << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>PB-Scale Iceberg Benchmark Performance Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .metrics { background-color: #f9f9f9; }
        pre { background-color: #f5f5f5; padding: 10px; overflow-x: auto; }
        .timestamp { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="header">
        <h1>PB-Scale Iceberg Benchmark Performance Report</h1>
        <p class="timestamp">Generated at: $(date)</p>
    </div>
    
    <div class="section">
        <h2>System Overview</h2>
        <div class="metrics">
EOF
    
    # Add current system status
    echo "<h3>Current System Status</h3>"
    echo "<pre>"
    uptime 2>/dev/null || echo "System uptime not available"
    echo ""
    free -h 2>/dev/null || echo "Memory information not available"
    echo ""
    df -h 2>/dev/null | head -10 || echo "Disk information not available"
    echo "</pre>"
    
    echo "<h3>Recent Monitoring Data</h3>"
    echo "<pre>"
    ls -la "$OUTPUT_DIR"/*.log 2>/dev/null | tail -10 || echo "No recent monitoring data available"
    echo "</pre>"
    
    cat << 'EOF'
        </div>
    </div>
    
    <div class="section">
        <h2>Recommendations</h2>
        <ul>
            <li>Monitor memory usage - ensure executors have sufficient memory for PB-scale processing</li>
            <li>Check disk I/O - high I/O wait times may indicate storage bottlenecks</li>
            <li>Verify network bandwidth - large data transfers require adequate network capacity</li>
            <li>Monitor GC activity - frequent garbage collection can impact performance</li>
            <li>Check Spark UI for task distribution and data skew issues</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>Useful Commands</h2>
        <pre>
# Monitor system resources
htop

# Check Spark applications
yarn application -list

# View Spark History Server
# Open http://localhost:18080 in browser

# Monitor disk I/O
iostat -x 1

# Check network usage
iftop
        </pre>
    </div>
</body>
</html>
EOF
  } > "$report_file"
  
  echo "$report_file"
}

# Main monitoring loop
main() {
  log "Starting PB-scale monitoring..."
  log "Monitor interval: ${MONITOR_INTERVAL}s"
  log "Output directory: $OUTPUT_DIR"
  
  # Create initial report
  report_file=$(generate_report)
  log "Initial report generated: $report_file"
  
  # Monitoring loop
  while true; do
    log "Collecting metrics..."
    
    # Collect system metrics
    system_file=$(collect_system_metrics)
    log "System metrics saved to: $system_file"
    
    # Collect Spark metrics
    spark_file=$(collect_spark_metrics)
    log "Spark metrics saved to: $spark_file"
    
    # Analyze logs periodically (every 5 intervals)
    if (( $(date +%s) % (MONITOR_INTERVAL * 5) == 0 )); then
      analysis_file=$(analyze_logs)
      log "Log analysis saved to: $analysis_file"
    fi
    
    log "Sleeping for ${MONITOR_INTERVAL}s..."
    sleep "$MONITOR_INTERVAL"
  done
}

# Handle command line arguments
case "${1:-monitor}" in
  monitor)
    main
    ;;
  report)
    report_file=$(generate_report)
    echo "Performance report generated: $report_file"
    ;;
  analyze)
    analysis_file=$(analyze_logs "${2:-*.log}")
    echo "Log analysis completed: $analysis_file"
    ;;
  *)
    echo "Usage: $0 [monitor|report|analyze [pattern]]"
    echo "  monitor  - Start continuous monitoring (default)"
    echo "  report   - Generate performance report"
    echo "  analyze  - Analyze log files (optional pattern)"
    exit 1
    ;;
esac
