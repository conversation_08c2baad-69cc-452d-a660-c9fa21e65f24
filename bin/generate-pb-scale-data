#!/usr/bin/env bash

#
# Script for generating PB-scale TPC-DS data with Iceberg format
# This script is optimized for large-scale data generation
#

if [ -z "${SPARK_HOME}" ]; then
  echo "env SPARK_HOME not defined" 1>&2
  exit 1
fi

# Determine the current working directory
_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Load PB-scale configuration
source "${_DIR}/../conf/pb-scale-config.sh"

# Parse command line arguments
SCALE_FACTOR=${PB_SCALE_FACTOR}
OUTPUT_LOCATION=${PB_DATA_LOCATION}
WAREHOUSE_LOCATION=${PB_WAREHOUSE_LOCATION}
NUM_PARTITIONS=${PB_NUM_PARTITIONS}
TABLE_FILTER=""
OVERWRITE="false"

while [[ $# -gt 0 ]]; do
  case $1 in
    --scale-factor)
      SCALE_FACTOR="$2"
      shift 2
      ;;
    --output-location)
      OUTPUT_LOCATION="$2"
      shift 2
      ;;
    --warehouse-location)
      WAREHOUSE_LOCATION="$2"
      shift 2
      ;;
    --num-partitions)
      NUM_PARTITIONS="$2"
      shift 2
      ;;
    --table-filter)
      TABLE_FILTER="$2"
      shift 2
      ;;
    --overwrite)
      OVERWRITE="true"
      shift
      ;;
    --help)
      echo "Usage: $0 [OPTIONS]"
      echo "Options:"
      echo "  --scale-factor [NUM]        Scale factor for data generation (default: $PB_SCALE_FACTOR)"
      echo "  --output-location [PATH]    Output location for generated data (default: $PB_DATA_LOCATION)"
      echo "  --warehouse-location [PATH] Iceberg warehouse location (default: $PB_WAREHOUSE_LOCATION)"
      echo "  --num-partitions [NUM]      Number of partitions (default: $PB_NUM_PARTITIONS)"
      echo "  --table-filter [TABLES]     Comma-separated list of tables to generate"
      echo "  --overwrite                 Overwrite existing data"
      echo "  --help                      Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1" 1>&2
      exit 1
      ;;
  esac
done

# Validate scale factor
if [[ $SCALE_FACTOR -lt 1000 ]]; then
  echo "Warning: Scale factor $SCALE_FACTOR is less than 1000. This script is optimized for PB-scale (>=1000)." 1>&2
  read -p "Continue? (y/N): " -n 1 -r
  echo
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    exit 1
  fi
fi

# Create output directories
echo "Creating output directories..."
mkdir -p "$OUTPUT_LOCATION"
mkdir -p "$WAREHOUSE_LOCATION"

# Resolve jar location
find_resource() {
  local tpcds_datagen_version=`grep "<version>" "${_DIR}/../pom.xml" | head -n2 | tail -n1 | awk -F '[<>]' '{print $3}'`
  local jar_file="iceberg-spark-tpcds-benchmark-${tpcds_datagen_version}-with-dependencies.jar"
  local built_jar="$_DIR/../target/${jar_file}"
  if [ -e "$built_jar" ]; then
    RESOURCE=$built_jar
  else
    RESOURCE="$_DIR/../assembly/${jar_file}"
    echo "${built_jar} not found, so use pre-compiled ${RESOURCE}" 1>&2
  fi
}

find_resource

# Build command arguments
ARGS="--output-location $OUTPUT_LOCATION"
ARGS="$ARGS --scale-factor $SCALE_FACTOR"
ARGS="$ARGS --num-partitions $NUM_PARTITIONS"
ARGS="$ARGS --max-partitions-per-writer $PB_MAX_PARTITIONS_PER_WRITER"
ARGS="$ARGS --iceberg"
ARGS="$ARGS --partition-tables"
ARGS="$ARGS --cluster-by-partition-columns"
ARGS="$ARGS --filter-out-null-partition-values"

if [[ "$OVERWRITE" == "true" ]]; then
  ARGS="$ARGS --overwrite"
fi

if [[ -n "$TABLE_FILTER" ]]; then
  ARGS="$ARGS --table-filter $TABLE_FILTER"
fi

# Update Iceberg warehouse location in Spark config
ICEBERG_CONF="$ICEBERG_CONF --conf spark.sql.catalog.hadoop_prod.warehouse=$WAREHOUSE_LOCATION"

echo "Starting PB-scale data generation..."
echo "  Scale Factor: $SCALE_FACTOR"
echo "  Output Location: $OUTPUT_LOCATION"
echo "  Warehouse Location: $WAREHOUSE_LOCATION"
echo "  Num Partitions: $NUM_PARTITIONS"
echo "  Table Filter: ${TABLE_FILTER:-"all tables"}"
echo "  Overwrite: $OVERWRITE"
echo ""

# Execute data generation
exec "${SPARK_HOME}"/bin/spark-submit \
  --class org.apache.spark.sql.execution.benchmark.TPCDSDatagen \
  $SPARK_CONF \
  $ICEBERG_CONF \
  ${RESOURCE} \
  $ARGS
