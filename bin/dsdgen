#!/usr/bin/env bash

#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

#
# Shell script for generating TPCDS data

if [ -z "${SPARK_HOME}" ]; then
  echo "env SPARK_HOME not defined" 1>&2
  exit 1
fi

# Determine the current working directory
_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"


# Resolve a jar location for the TPCDS data generator
find_resource() {
  local tpcds_datagen_version=`grep "<version>" "${_DIR}/../pom.xml" | head -n2 | tail -n1 | awk -F '[<>]' '{print $3}'`
  local scala_version=`grep "<scala.binary.version>" "${_DIR}/../pom.xml" | head -n1 | awk -F '[<>]' '{print $3}'`
  local jar_file="iceberg-spark-tpcds-benchmark-${tpcds_datagen_version}-with-dependencies.jar"
  local built_jar="$_DIR/../target/${jar_file}"
  if [ -e "$built_jar" ]; then
    RESOURCE=$built_jar
  else
    RESOURCE="$_DIR/../assembly/${jar_file}"
    echo "${built_jar} not found, so use pre-compiled ${RESOURCE}" 1>&2
  fi
}

find_resource

# Detect scale factor to determine cluster configuration
SCALE_FACTOR=""
for arg in "$@"; do
  if [[ "$prev_arg" == "--scale-factor" ]]; then
    SCALE_FACTOR="$arg"
    break
  fi
  prev_arg="$arg"
done

# Configure Spark for PB-scale if scale factor >= 1000
if [[ -n "$SCALE_FACTOR" && "$SCALE_FACTOR" -ge 1000 ]]; then
  echo "Detected large scale factor ($SCALE_FACTOR), configuring for PB-scale processing..." 1>&2
  SPARK_CONF="$SPARK_CONF --master yarn --deploy-mode cluster \
    --driver-memory 16g --driver-cores 4 \
    --executor-memory 32g --executor-cores 8 \
    --num-executors 100 \
    --conf spark.executor.memoryFraction=0.8 \
    --conf spark.sql.shuffle.partitions=4000 \
    --conf spark.default.parallelism=4000 \
    --conf spark.sql.adaptive.enabled=true \
    --conf spark.sql.adaptive.coalescePartitions.enabled=true \
    --conf spark.sql.adaptive.coalescePartitions.minPartitionNum=200 \
    --conf spark.sql.adaptive.advisoryPartitionSizeInBytes=256MB \
    --conf spark.sql.files.maxPartitionBytes=1GB"
else
  SPARK_CONF="$SPARK_CONF --master local[*] \
    --driver-memory 8g \
    --conf spark.sql.shuffle.partitions=200"
fi

echo "Using \`spark-submit\` from path: $SPARK_HOME" 1>&2
exec "${SPARK_HOME}"/bin/spark-submit \
  --class org.apache.spark.sql.execution.benchmark.TPCDSDatagen \
  ${SPARK_CONF} \
  ${RESOURCE} \
  $@

