#!/usr/bin/env bash

#
# Test script for Iceberg catalog configurations
# This script helps validate different catalog setups
#

if [ -z "${SPARK_HOME}" ]; then
  echo "env SPARK_HOME not defined" 1>&2
  exit 1
fi

# Determine the current working directory
_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Default test parameters
TEST_SCALE_FACTOR="1"
TEST_OUTPUT_LOCATION="/tmp/iceberg-catalog-test"
TEST_WAREHOUSE_LOCATION="/tmp/iceberg-catalog-test-warehouse"
CATALOG_TYPE="hadoop"
CATALOG_NAME="test_catalog"
CLEANUP_AFTER_TEST="true"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --catalog-type)
      CATALOG_TYPE="$2"
      shift 2
      ;;
    --catalog-name)
      CATALOG_NAME="$2"
      shift 2
      ;;
    --scale-factor)
      TEST_SCALE_FACTOR="$2"
      shift 2
      ;;
    --output-location)
      TEST_OUTPUT_LOCATION="$2"
      shift 2
      ;;
    --warehouse-location)
      TEST_WAREHOUSE_LOCATION="$2"
      shift 2
      ;;
    --no-cleanup)
      CLEANUP_AFTER_TEST="false"
      shift
      ;;
    --help)
      echo "Usage: $0 [OPTIONS]"
      echo "Options:"
      echo "  --catalog-type [TYPE]       Catalog type to test (default: hadoop)"
      echo "  --catalog-name [NAME]       Catalog name (default: test_catalog)"
      echo "  --scale-factor [NUM]        Scale factor for test data (default: 1)"
      echo "  --output-location [PATH]    Test output location (default: /tmp/iceberg-catalog-test)"
      echo "  --warehouse-location [PATH] Test warehouse location (default: /tmp/iceberg-catalog-test-warehouse)"
      echo "  --no-cleanup               Don't cleanup test data after completion"
      echo "  --help                     Show this help message"
      echo ""
      echo "Supported catalog types: hadoop, hive, jdbc, rest, glue, nessie"
      exit 0
      ;;
    *)
      echo "Unknown option: $1" 1>&2
      exit 1
      ;;
  esac
done

# Function to log with timestamp
log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to cleanup test data
cleanup_test_data() {
  if [[ "$CLEANUP_AFTER_TEST" == "true" ]]; then
    log "Cleaning up test data..."
    rm -rf "$TEST_OUTPUT_LOCATION" 2>/dev/null || true
    rm -rf "$TEST_WAREHOUSE_LOCATION" 2>/dev/null || true
    log "Cleanup completed"
  else
    log "Skipping cleanup (--no-cleanup specified)"
    log "Test data location: $TEST_OUTPUT_LOCATION"
    log "Test warehouse location: $TEST_WAREHOUSE_LOCATION"
  fi
}

# Function to test data generation
test_data_generation() {
  log "Testing data generation with $CATALOG_TYPE catalog..."
  
  # Create test directories
  mkdir -p "$TEST_OUTPUT_LOCATION"
  mkdir -p "$TEST_WAREHOUSE_LOCATION"
  
  # Build generation command
  local gen_cmd="${_DIR}/generate-pb-scale-data"
  gen_cmd="$gen_cmd --scale-factor $TEST_SCALE_FACTOR"
  gen_cmd="$gen_cmd --output-location $TEST_OUTPUT_LOCATION"
  gen_cmd="$gen_cmd --warehouse-location $TEST_WAREHOUSE_LOCATION"
  gen_cmd="$gen_cmd --iceberg-catalog-type $CATALOG_TYPE"
  gen_cmd="$gen_cmd --iceberg-catalog-name $CATALOG_NAME"
  gen_cmd="$gen_cmd --table-filter catalog_sales"  # Only generate one small table for testing
  gen_cmd="$gen_cmd --overwrite"
  
  # Add catalog-specific parameters
  case "$CATALOG_TYPE" in
    "hive")
      gen_cmd="$gen_cmd --hive-metastore-uri ${HIVE_METASTORE_URI:-thrift://localhost:9083}"
      ;;
  esac
  
  log "Running command: $gen_cmd"
  
  # Execute data generation
  if eval "$gen_cmd"; then
    log "✓ Data generation completed successfully"
    return 0
  else
    log "✗ Data generation failed"
    return 1
  fi
}

# Function to test query execution
test_query_execution() {
  log "Testing query execution with $CATALOG_TYPE catalog..."
  
  # Build benchmark command
  local bench_cmd="${_DIR}/run-pb-scale-benchmark"
  bench_cmd="$bench_cmd --data-location $TEST_OUTPUT_LOCATION"
  bench_cmd="$bench_cmd --warehouse-location $TEST_WAREHOUSE_LOCATION"
  bench_cmd="$bench_cmd --iceberg-catalog-type $CATALOG_TYPE"
  bench_cmd="$bench_cmd --iceberg-catalog-name $CATALOG_NAME"
  bench_cmd="$bench_cmd --query-filter q1"  # Only run one simple query for testing
  bench_cmd="$bench_cmd --warmup-runs 0"
  bench_cmd="$bench_cmd --benchmark-runs 1"
  
  # Add catalog-specific parameters
  case "$CATALOG_TYPE" in
    "hive")
      bench_cmd="$bench_cmd --hive-metastore-uri ${HIVE_METASTORE_URI:-thrift://localhost:9083}"
      ;;
  esac
  
  log "Running command: $bench_cmd"
  
  # Execute benchmark
  if eval "$bench_cmd"; then
    log "✓ Query execution completed successfully"
    return 0
  else
    log "✗ Query execution failed"
    return 1
  fi
}

# Function to validate catalog prerequisites
validate_prerequisites() {
  log "Validating prerequisites for $CATALOG_TYPE catalog..."
  
  case "$CATALOG_TYPE" in
    "hadoop")
      log "✓ Hadoop catalog requires no external dependencies"
      ;;
    "hive")
      local hive_uri=${HIVE_METASTORE_URI:-"thrift://localhost:9083"}
      log "Checking Hive Metastore connectivity: $hive_uri"
      # Note: In a real scenario, you might want to test connectivity here
      log "⚠ Please ensure Hive Metastore is running at: $hive_uri"
      ;;
    "jdbc")
      log "⚠ JDBC catalog requires database connectivity"
      log "⚠ Please ensure JDBC database is accessible"
      ;;
    "rest")
      log "⚠ REST catalog requires REST server connectivity"
      log "⚠ Please ensure REST catalog server is running"
      ;;
    "glue")
      log "⚠ Glue catalog requires AWS credentials and permissions"
      log "⚠ Please ensure AWS CLI is configured or IAM roles are set"
      ;;
    "nessie")
      log "⚠ Nessie catalog requires Nessie server connectivity"
      log "⚠ Please ensure Nessie server is running"
      ;;
    *)
      log "✗ Unsupported catalog type: $CATALOG_TYPE"
      return 1
      ;;
  esac
  
  return 0
}

# Function to show test summary
show_test_summary() {
  local gen_result=$1
  local query_result=$2
  
  echo ""
  echo "=== Test Summary ==="
  echo "Catalog Type: $CATALOG_TYPE"
  echo "Catalog Name: $CATALOG_NAME"
  echo "Scale Factor: $TEST_SCALE_FACTOR"
  echo "Output Location: $TEST_OUTPUT_LOCATION"
  echo "Warehouse Location: $TEST_WAREHOUSE_LOCATION"
  echo ""
  
  if [[ $gen_result -eq 0 ]]; then
    echo "✓ Data Generation: PASSED"
  else
    echo "✗ Data Generation: FAILED"
  fi
  
  if [[ $query_result -eq 0 ]]; then
    echo "✓ Query Execution: PASSED"
  else
    echo "✗ Query Execution: FAILED"
  fi
  
  echo ""
  
  if [[ $gen_result -eq 0 && $query_result -eq 0 ]]; then
    echo "🎉 All tests PASSED! Catalog configuration is working correctly."
    return 0
  else
    echo "❌ Some tests FAILED. Please check the configuration and logs."
    return 1
  fi
}

# Main test execution
main() {
  log "Starting Iceberg catalog configuration test..."
  log "Catalog Type: $CATALOG_TYPE"
  log "Catalog Name: $CATALOG_NAME"
  
  # Validate prerequisites
  if ! validate_prerequisites; then
    log "Prerequisites validation failed"
    exit 1
  fi
  
  # Set up cleanup trap
  trap cleanup_test_data EXIT
  
  # Run tests
  local gen_result=1
  local query_result=1
  
  if test_data_generation; then
    gen_result=0
    
    if test_query_execution; then
      query_result=0
    fi
  fi
  
  # Show summary
  show_test_summary $gen_result $query_result
  local final_result=$?
  
  log "Test completed"
  exit $final_result
}

# Run main function
main "$@"
