#!/usr/bin/env bash

#
# Script to initialize Hadoop catalog for Iceberg
# This script creates the necessary namespace/database for Hadoop catalog
#

if [ -z "${SPARK_HOME}" ]; then
  echo "env SPARK_HOME not defined" 1>&2
  exit 1
fi

# Determine the current working directory
_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Parse command line arguments
WAREHOUSE_LOCATION="hdfs://NS1/bdoc/data/iceberg-warehouse"
CATALOG_NAME="hadoop_catalog"
NAMESPACE="tpcds"

while [[ $# -gt 0 ]]; do
  case $1 in
    --warehouse-location)
      WAREHOUSE_LOCATION="$2"
      shift 2
      ;;
    --catalog-name)
      CATALOG_NAME="$2"
      shift 2
      ;;
    --namespace)
      NAMESPACE="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [OPTIONS]"
      echo "Options:"
      echo "  --warehouse-location [PATH] Iceberg warehouse location (default: hdfs://NS1/bdoc/data/iceberg-warehouse)"
      echo "  --catalog-name [NAME]       Catalog name (default: hadoop_catalog)"
      echo "  --namespace [NAME]          Namespace to create (default: tpcds)"
      echo "  --help                      Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1" 1>&2
      exit 1
      ;;
  esac
done

# Resolve jar location
find_resource() {
  local tpcds_datagen_version=`grep "<version>" "${_DIR}/../pom.xml" | head -n2 | tail -n1 | awk -F '[<>]' '{print $3}'`
  local jar_file="iceberg-spark-tpcds-benchmark-${tpcds_datagen_version}-with-dependencies.jar"
  local built_jar="$_DIR/../target/${jar_file}"
  if [ -e "$built_jar" ]; then
    RESOURCE=$built_jar
  else
    RESOURCE="$_DIR/../assembly/${jar_file}"
    echo "${built_jar} not found, so use pre-compiled ${RESOURCE}" 1>&2
  fi
}

find_resource

# Spark configuration for initialization
SPARK_CONF="
--master yarn
--driver-memory 4g
--executor-memory 8g
--num-executors 2
"

# Iceberg configuration
ICEBERG_CONF="
--conf spark.sql.extensions=org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions
--conf spark.sql.catalog.spark_catalog=org.apache.iceberg.spark.SparkSessionCatalog
--conf spark.sql.catalog.spark_catalog.type=hive
--conf spark.sql.catalog.${CATALOG_NAME}=org.apache.iceberg.spark.SparkCatalog
--conf spark.sql.catalog.${CATALOG_NAME}.type=hadoop
--conf spark.sql.catalog.${CATALOG_NAME}.warehouse=${WAREHOUSE_LOCATION}
"

echo "Initializing Hadoop catalog..."
echo "  Warehouse Location: $WAREHOUSE_LOCATION"
echo "  Catalog Name: $CATALOG_NAME"
echo "  Namespace: $NAMESPACE"
echo ""

# Create initialization SQL
INIT_SQL="
CREATE NAMESPACE IF NOT EXISTS ${CATALOG_NAME}.${NAMESPACE};
SHOW NAMESPACES IN ${CATALOG_NAME};
"

# Create temporary SQL file
TEMP_SQL_FILE="/tmp/init_hadoop_catalog_$$.sql"
echo "$INIT_SQL" > "$TEMP_SQL_FILE"

echo "Executing initialization SQL:"
echo "$INIT_SQL"
echo ""

# Execute initialization
"${SPARK_HOME}"/bin/spark-sql \
  $SPARK_CONF \
  $ICEBERG_CONF \
  --file "$TEMP_SQL_FILE"

RESULT=$?

# Clean up
rm -f "$TEMP_SQL_FILE"

if [[ $RESULT -eq 0 ]]; then
  echo ""
  echo "=== Hadoop catalog initialized successfully! ==="
  echo "You can now use the following command to generate data:"
  echo ""
  echo "./bin/generate-pb-scale-data \\"
  echo "  --scale-factor 1000 \\"
  echo "  --num-partitions 1000 \\"
  echo "  --iceberg-catalog-type hadoop \\"
  echo "  --iceberg-catalog-name $CATALOG_NAME \\"
  echo "  --warehouse-location $WAREHOUSE_LOCATION \\"
  echo "  --output-location ${WAREHOUSE_LOCATION}/${NAMESPACE}"
  echo ""
else
  echo ""
  echo "=== Hadoop catalog initialization failed! ==="
  exit 1
fi
