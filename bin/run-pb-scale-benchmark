#!/usr/bin/env bash

#
# Script for running PB-scale TPC-DS benchmark queries with Iceberg
# This script is optimized for large-scale query performance testing
#

if [ -z "${SPARK_HOME}" ]; then
  echo "env SPARK_HOME not defined" 1>&2
  exit 1
fi

# Determine the current working directory
_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Load PB-scale configuration
source "${_DIR}/../conf/pb-scale-config.sh"

# Parse command line arguments
DATA_LOCATION=${PB_DATA_LOCATION}
WAREHOUSE_LOCATION=${PB_WAREHOUSE_LOCATION}
QUERY_FILTER=""
CBO_ENABLED="true"
WARMUP_RUNS="1"
BENCHMARK_RUNS="3"
OUTPUT_FILE=""

while [[ $# -gt 0 ]]; do
  case $1 in
    --data-location)
      DATA_LOCATION="$2"
      shift 2
      ;;
    --warehouse-location)
      WAREHOUSE_LOCATION="$2"
      shift 2
      ;;
    --query-filter)
      QUERY_FILTER="$2"
      shift 2
      ;;
    --disable-cbo)
      CBO_ENABLED="false"
      shift
      ;;
    --warmup-runs)
      WARMUP_RUNS="$2"
      shift 2
      ;;
    --benchmark-runs)
      BENCHMARK_RUNS="$2"
      shift 2
      ;;
    --output-file)
      OUTPUT_FILE="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [OPTIONS]"
      echo "Options:"
      echo "  --data-location [PATH]      Path to TPC-DS data (default: $PB_DATA_LOCATION)"
      echo "  --warehouse-location [PATH] Iceberg warehouse location (default: $PB_WAREHOUSE_LOCATION)"
      echo "  --query-filter [QUERIES]    Comma-separated list of queries to run (e.g., q1,q2,q3)"
      echo "  --disable-cbo               Disable cost-based optimization"
      echo "  --warmup-runs [NUM]         Number of warmup runs (default: 1)"
      echo "  --benchmark-runs [NUM]      Number of benchmark runs (default: 3)"
      echo "  --output-file [PATH]        Output file for benchmark results"
      echo "  --help                      Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1" 1>&2
      exit 1
      ;;
  esac
done

# Validate data location
if [[ ! -d "$DATA_LOCATION" ]]; then
  echo "Error: Data location $DATA_LOCATION does not exist" 1>&2
  echo "Please generate PB-scale data first using: ./bin/generate-pb-scale-data" 1>&2
  exit 1
fi

# Resolve jar location
find_resource() {
  local tpcds_datagen_version=`grep "<version>" "${_DIR}/../pom.xml" | head -n2 | tail -n1 | awk -F '[<>]' '{print $3}'`
  local jar_file="iceberg-spark-tpcds-benchmark-${tpcds_datagen_version}-with-dependencies.jar"
  local built_jar="$_DIR/../target/${jar_file}"
  if [ -e "$built_jar" ]; then
    RESOURCE=$built_jar
  else
    RESOURCE="$_DIR/../assembly/${jar_file}"
    echo "${built_jar} not found, so use pre-compiled ${RESOURCE}" 1>&2
  fi
}

find_resource

# Build command arguments
ARGS="--data-location $DATA_LOCATION"
ARGS="$ARGS --iceberg"

if [[ "$CBO_ENABLED" == "true" ]]; then
  ARGS="$ARGS --cbo"
fi

if [[ -n "$QUERY_FILTER" ]]; then
  ARGS="$ARGS --query-filter $QUERY_FILTER"
fi

# Update Iceberg warehouse location in Spark config
ICEBERG_CONF="$ICEBERG_CONF --conf spark.sql.catalog.hadoop_prod.warehouse=$WAREHOUSE_LOCATION"

# Create output directory if specified
if [[ -n "$OUTPUT_FILE" ]]; then
  mkdir -p "$(dirname "$OUTPUT_FILE")"
fi

echo "Starting PB-scale benchmark..."
echo "  Data Location: $DATA_LOCATION"
echo "  Warehouse Location: $WAREHOUSE_LOCATION"
echo "  Query Filter: ${QUERY_FILTER:-"all queries"}"
echo "  CBO Enabled: $CBO_ENABLED"
echo "  Warmup Runs: $WARMUP_RUNS"
echo "  Benchmark Runs: $BENCHMARK_RUNS"
echo "  Output File: ${OUTPUT_FILE:-"console"}"
echo ""

# Function to run benchmark
run_benchmark() {
  local run_type=$1
  local run_number=$2
  
  echo "[$run_type] Run $run_number starting at $(date)"
  
  local cmd="${SPARK_HOME}/bin/spark-submit \
    --class org.apache.spark.sql.execution.benchmark.TPCDSQueryBenchmark \
    $SPARK_CONF \
    $ICEBERG_CONF \
    ${RESOURCE} \
    $ARGS"
  
  if [[ -n "$OUTPUT_FILE" ]]; then
    echo "[$run_type] Run $run_number" >> "$OUTPUT_FILE"
    echo "Command: $cmd" >> "$OUTPUT_FILE"
    echo "Started at: $(date)" >> "$OUTPUT_FILE"
    echo "----------------------------------------" >> "$OUTPUT_FILE"
    
    eval $cmd >> "$OUTPUT_FILE" 2>&1
    local exit_code=$?
    
    echo "Completed at: $(date)" >> "$OUTPUT_FILE"
    echo "Exit code: $exit_code" >> "$OUTPUT_FILE"
    echo "========================================" >> "$OUTPUT_FILE"
    echo "" >> "$OUTPUT_FILE"
  else
    eval $cmd
    local exit_code=$?
  fi
  
  echo "[$run_type] Run $run_number completed with exit code $exit_code at $(date)"
  return $exit_code
}

# Run warmup iterations
if [[ $WARMUP_RUNS -gt 0 ]]; then
  echo "Running $WARMUP_RUNS warmup iteration(s)..."
  for ((i=1; i<=WARMUP_RUNS; i++)); do
    run_benchmark "WARMUP" $i
    if [[ $? -ne 0 ]]; then
      echo "Warmup run $i failed, continuing..." 1>&2
    fi
  done
  echo "Warmup completed."
  echo ""
fi

# Run benchmark iterations
echo "Running $BENCHMARK_RUNS benchmark iteration(s)..."
failed_runs=0
for ((i=1; i<=BENCHMARK_RUNS; i++)); do
  run_benchmark "BENCHMARK" $i
  if [[ $? -ne 0 ]]; then
    echo "Benchmark run $i failed" 1>&2
    ((failed_runs++))
  fi
done

echo ""
echo "Benchmark completed!"
echo "  Total runs: $BENCHMARK_RUNS"
echo "  Failed runs: $failed_runs"
echo "  Success rate: $(( (BENCHMARK_RUNS - failed_runs) * 100 / BENCHMARK_RUNS ))%"

if [[ -n "$OUTPUT_FILE" ]]; then
  echo "  Results saved to: $OUTPUT_FILE"
fi

exit $failed_runs
